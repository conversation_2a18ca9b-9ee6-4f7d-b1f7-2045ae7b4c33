1:HL["/_next/static/css/db95f5951fa8a14e.css","style",{"crossOrigin":""}]
0:["WMVkmVEjqvHZaHOgfl7Yg",[[["",{"children":["dashboard",{"children":["__PAGE__",{}]}]},"$undefined","$undefined",true],"$L2",[[["$","link","0",{"rel":"stylesheet","href":"/_next/static/css/db95f5951fa8a14e.css","precedence":"next","crossOrigin":""}]],"$L3"]]]]
4:I[69850,["73","static/chunks/73-a99bdb75f34c9c6d.js","185","static/chunks/app/layout-0e346c35535d154b.js"],"Providers"]
5:I[56954,[],""]
6:I[7264,[],""]
7:I[24672,["323","static/chunks/323-a4eccfab85e296e8.js","663","static/chunks/app/dashboard/layout-f375ad963987eb58.js"],""]
9:I[48297,[],""]
a:I[37112,["79","static/chunks/79-5f722215b0089452.js","702","static/chunks/app/dashboard/page-93ca58567a9abb43.js"],""]
b:{}
2:[null,["$","html",null,{"lang":"ar","dir":"rtl","suppressHydrationWarning":true,"children":[["$","head",null,{"children":["$","link",null,{"href":"https://fonts.googleapis.com/css2?family=Noto+Sans+Arabic:wght@300;400;500;600;700&display=swap","rel":"stylesheet"}]}],["$","body",null,{"className":"__variable_e8ce0c font-arabic antialiased","children":["$","$L4",null,{"children":["$","$L5",null,{"parallelRouterKey":"children","segmentPath":["children"],"loading":"$undefined","loadingStyles":"$undefined","loadingScripts":"$undefined","hasLoading":false,"error":"$undefined","errorStyles":"$undefined","errorScripts":"$undefined","template":["$","$L6",null,{}],"templateStyles":"$undefined","templateScripts":"$undefined","notFound":[["$","title",null,{"children":"404: This page could not be found."}],["$","div",null,{"style":{"fontFamily":"system-ui,\"Segoe UI\",Roboto,Helvetica,Arial,sans-serif,\"Apple Color Emoji\",\"Segoe UI Emoji\"","height":"100vh","textAlign":"center","display":"flex","flexDirection":"column","alignItems":"center","justifyContent":"center"},"children":["$","div",null,{"children":[["$","style",null,{"dangerouslySetInnerHTML":{"__html":"body{color:#000;background:#fff;margin:0}.next-error-h1{border-right:1px solid rgba(0,0,0,.3)}@media (prefers-color-scheme:dark){body{color:#fff;background:#000}.next-error-h1{border-right:1px solid rgba(255,255,255,.3)}}"}}],["$","h1",null,{"className":"next-error-h1","style":{"display":"inline-block","margin":"0 20px 0 0","padding":"0 23px 0 0","fontSize":24,"fontWeight":500,"verticalAlign":"top","lineHeight":"49px"},"children":"404"}],["$","div",null,{"style":{"display":"inline-block"},"children":["$","h2",null,{"style":{"fontSize":14,"fontWeight":400,"lineHeight":"49px","margin":0},"children":"This page could not be found."}]}]]}]}]],"notFoundStyles":[],"initialChildNode":[null,["$","$L7",null,{"children":["$","$L5",null,{"parallelRouterKey":"children","segmentPath":["children","dashboard","children"],"loading":"$undefined","loadingStyles":"$undefined","loadingScripts":"$undefined","hasLoading":false,"error":"$undefined","errorStyles":"$undefined","errorScripts":"$undefined","template":["$","$L6",null,{}],"templateStyles":"$undefined","templateScripts":"$undefined","notFound":"$undefined","notFoundStyles":"$undefined","initialChildNode":["$L8",["$","$L9",null,{"propsForComponent":{"params":{}},"Component":"$a","isStaticGeneration":true}],null],"childPropSegment":"__PAGE__","styles":null}],"params":"$b"}],null],"childPropSegment":"dashboard","styles":null}]}]}]]}],null]
3:[["$","meta","0",{"name":"viewport","content":"width=device-width, initial-scale=1"}],["$","meta","1",{"charSet":"utf-8"}],["$","title","2",{"children":"Freela Syria - Admin Dashboard"}],["$","meta","3",{"name":"description","content":"Administrative dashboard for Freela Syria marketplace"}],["$","meta","4",{"name":"author","content":"Freela Syria Team"}],["$","meta","5",{"name":"keywords","content":"freelance,syria,admin,dashboard"}]]
8:null
