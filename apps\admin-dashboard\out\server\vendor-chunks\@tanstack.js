"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@tanstack";
exports.ids = ["vendor-chunks/@tanstack"];
exports.modules = {

/***/ "(ssr)/../../node_modules/@tanstack/query-core/build/modern/focusManager.js":
/*!****************************************************************************!*\
  !*** ../../node_modules/@tanstack/query-core/build/modern/focusManager.js ***!
  \****************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   FocusManager: () => (/* binding */ FocusManager),\n/* harmony export */   focusManager: () => (/* binding */ focusManager)\n/* harmony export */ });\n/* harmony import */ var _subscribable_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./subscribable.js */ \"(ssr)/../../node_modules/@tanstack/query-core/build/modern/subscribable.js\");\n/* harmony import */ var _utils_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./utils.js */ \"(ssr)/../../node_modules/@tanstack/query-core/build/modern/utils.js\");\n// src/focusManager.ts\n\n\nvar FocusManager = class extends _subscribable_js__WEBPACK_IMPORTED_MODULE_0__.Subscribable {\n    #focused;\n    #cleanup;\n    #setup;\n    constructor(){\n        super();\n        this.#setup = (onFocus)=>{\n            if (!_utils_js__WEBPACK_IMPORTED_MODULE_1__.isServer && window.addEventListener) {\n                const listener = ()=>onFocus();\n                window.addEventListener(\"visibilitychange\", listener, false);\n                return ()=>{\n                    window.removeEventListener(\"visibilitychange\", listener);\n                };\n            }\n            return;\n        };\n    }\n    onSubscribe() {\n        if (!this.#cleanup) {\n            this.setEventListener(this.#setup);\n        }\n    }\n    onUnsubscribe() {\n        if (!this.hasListeners()) {\n            this.#cleanup?.();\n            this.#cleanup = void 0;\n        }\n    }\n    setEventListener(setup) {\n        this.#setup = setup;\n        this.#cleanup?.();\n        this.#cleanup = setup((focused)=>{\n            if (typeof focused === \"boolean\") {\n                this.setFocused(focused);\n            } else {\n                this.onFocus();\n            }\n        });\n    }\n    setFocused(focused) {\n        const changed = this.#focused !== focused;\n        if (changed) {\n            this.#focused = focused;\n            this.onFocus();\n        }\n    }\n    onFocus() {\n        const isFocused = this.isFocused();\n        this.listeners.forEach((listener)=>{\n            listener(isFocused);\n        });\n    }\n    isFocused() {\n        if (typeof this.#focused === \"boolean\") {\n            return this.#focused;\n        }\n        return globalThis.document?.visibilityState !== \"hidden\";\n    }\n};\nvar focusManager = new FocusManager();\n //# sourceMappingURL=focusManager.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/@tanstack/query-core/build/modern/focusManager.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/@tanstack/query-core/build/modern/infiniteQueryBehavior.js":
/*!*************************************************************************************!*\
  !*** ../../node_modules/@tanstack/query-core/build/modern/infiniteQueryBehavior.js ***!
  \*************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   hasNextPage: () => (/* binding */ hasNextPage),\n/* harmony export */   hasPreviousPage: () => (/* binding */ hasPreviousPage),\n/* harmony export */   infiniteQueryBehavior: () => (/* binding */ infiniteQueryBehavior)\n/* harmony export */ });\n/* harmony import */ var _utils_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./utils.js */ \"(ssr)/../../node_modules/@tanstack/query-core/build/modern/utils.js\");\n// src/infiniteQueryBehavior.ts\n\nfunction infiniteQueryBehavior(pages) {\n    return {\n        onFetch: (context, query)=>{\n            const options = context.options;\n            const direction = context.fetchOptions?.meta?.fetchMore?.direction;\n            const oldPages = context.state.data?.pages || [];\n            const oldPageParams = context.state.data?.pageParams || [];\n            let result = {\n                pages: [],\n                pageParams: []\n            };\n            let currentPage = 0;\n            const fetchFn = async ()=>{\n                let cancelled = false;\n                const addSignalProperty = (object)=>{\n                    Object.defineProperty(object, \"signal\", {\n                        enumerable: true,\n                        get: ()=>{\n                            if (context.signal.aborted) {\n                                cancelled = true;\n                            } else {\n                                context.signal.addEventListener(\"abort\", ()=>{\n                                    cancelled = true;\n                                });\n                            }\n                            return context.signal;\n                        }\n                    });\n                };\n                const queryFn = (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.ensureQueryFn)(context.options, context.fetchOptions);\n                const fetchPage = async (data, param, previous)=>{\n                    if (cancelled) {\n                        return Promise.reject();\n                    }\n                    if (param == null && data.pages.length) {\n                        return Promise.resolve(data);\n                    }\n                    const createQueryFnContext = ()=>{\n                        const queryFnContext2 = {\n                            client: context.client,\n                            queryKey: context.queryKey,\n                            pageParam: param,\n                            direction: previous ? \"backward\" : \"forward\",\n                            meta: context.options.meta\n                        };\n                        addSignalProperty(queryFnContext2);\n                        return queryFnContext2;\n                    };\n                    const queryFnContext = createQueryFnContext();\n                    const page = await queryFn(queryFnContext);\n                    const { maxPages } = context.options;\n                    const addTo = previous ? _utils_js__WEBPACK_IMPORTED_MODULE_0__.addToStart : _utils_js__WEBPACK_IMPORTED_MODULE_0__.addToEnd;\n                    return {\n                        pages: addTo(data.pages, page, maxPages),\n                        pageParams: addTo(data.pageParams, param, maxPages)\n                    };\n                };\n                if (direction && oldPages.length) {\n                    const previous = direction === \"backward\";\n                    const pageParamFn = previous ? getPreviousPageParam : getNextPageParam;\n                    const oldData = {\n                        pages: oldPages,\n                        pageParams: oldPageParams\n                    };\n                    const param = pageParamFn(options, oldData);\n                    result = await fetchPage(oldData, param, previous);\n                } else {\n                    const remainingPages = pages ?? oldPages.length;\n                    do {\n                        const param = currentPage === 0 ? oldPageParams[0] ?? options.initialPageParam : getNextPageParam(options, result);\n                        if (currentPage > 0 && param == null) {\n                            break;\n                        }\n                        result = await fetchPage(result, param);\n                        currentPage++;\n                    }while (currentPage < remainingPages);\n                }\n                return result;\n            };\n            if (context.options.persister) {\n                context.fetchFn = ()=>{\n                    return context.options.persister?.(fetchFn, {\n                        client: context.client,\n                        queryKey: context.queryKey,\n                        meta: context.options.meta,\n                        signal: context.signal\n                    }, query);\n                };\n            } else {\n                context.fetchFn = fetchFn;\n            }\n        }\n    };\n}\nfunction getNextPageParam(options, { pages, pageParams }) {\n    const lastIndex = pages.length - 1;\n    return pages.length > 0 ? options.getNextPageParam(pages[lastIndex], pages, pageParams[lastIndex], pageParams) : void 0;\n}\nfunction getPreviousPageParam(options, { pages, pageParams }) {\n    return pages.length > 0 ? options.getPreviousPageParam?.(pages[0], pages, pageParams[0], pageParams) : void 0;\n}\nfunction hasNextPage(options, data) {\n    if (!data) return false;\n    return getNextPageParam(options, data) != null;\n}\nfunction hasPreviousPage(options, data) {\n    if (!data || !options.getPreviousPageParam) return false;\n    return getPreviousPageParam(options, data) != null;\n}\n //# sourceMappingURL=infiniteQueryBehavior.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzL0B0YW5zdGFjay9xdWVyeS1jb3JlL2J1aWxkL21vZGVybi9pbmZpbml0ZVF1ZXJ5QmVoYXZpb3IuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7OztBQUFBLCtCQUErQjtBQUNrQztBQUNqRSxTQUFTRyxzQkFBc0JDLEtBQUs7SUFDbEMsT0FBTztRQUNMQyxTQUFTLENBQUNDLFNBQVNDO1lBQ2pCLE1BQU1DLFVBQVVGLFFBQVFFLE9BQU87WUFDL0IsTUFBTUMsWUFBWUgsUUFBUUksWUFBWSxFQUFFQyxNQUFNQyxXQUFXSDtZQUN6RCxNQUFNSSxXQUFXUCxRQUFRUSxLQUFLLENBQUNDLElBQUksRUFBRVgsU0FBUyxFQUFFO1lBQ2hELE1BQU1ZLGdCQUFnQlYsUUFBUVEsS0FBSyxDQUFDQyxJQUFJLEVBQUVFLGNBQWMsRUFBRTtZQUMxRCxJQUFJQyxTQUFTO2dCQUFFZCxPQUFPLEVBQUU7Z0JBQUVhLFlBQVksRUFBRTtZQUFDO1lBQ3pDLElBQUlFLGNBQWM7WUFDbEIsTUFBTUMsVUFBVTtnQkFDZCxJQUFJQyxZQUFZO2dCQUNoQixNQUFNQyxvQkFBb0IsQ0FBQ0M7b0JBQ3pCQyxPQUFPQyxjQUFjLENBQUNGLFFBQVEsVUFBVTt3QkFDdENHLFlBQVk7d0JBQ1pDLEtBQUs7NEJBQ0gsSUFBSXJCLFFBQVFzQixNQUFNLENBQUNDLE9BQU8sRUFBRTtnQ0FDMUJSLFlBQVk7NEJBQ2QsT0FBTztnQ0FDTGYsUUFBUXNCLE1BQU0sQ0FBQ0UsZ0JBQWdCLENBQUMsU0FBUztvQ0FDdkNULFlBQVk7Z0NBQ2Q7NEJBQ0Y7NEJBQ0EsT0FBT2YsUUFBUXNCLE1BQU07d0JBQ3ZCO29CQUNGO2dCQUNGO2dCQUNBLE1BQU1HLFVBQVU3Qix3REFBYUEsQ0FBQ0ksUUFBUUUsT0FBTyxFQUFFRixRQUFRSSxZQUFZO2dCQUNuRSxNQUFNc0IsWUFBWSxPQUFPakIsTUFBTWtCLE9BQU9DO29CQUNwQyxJQUFJYixXQUFXO3dCQUNiLE9BQU9jLFFBQVFDLE1BQU07b0JBQ3ZCO29CQUNBLElBQUlILFNBQVMsUUFBUWxCLEtBQUtYLEtBQUssQ0FBQ2lDLE1BQU0sRUFBRTt3QkFDdEMsT0FBT0YsUUFBUUcsT0FBTyxDQUFDdkI7b0JBQ3pCO29CQUNBLE1BQU13Qix1QkFBdUI7d0JBQzNCLE1BQU1DLGtCQUFrQjs0QkFDdEJDLFFBQVFuQyxRQUFRbUMsTUFBTTs0QkFDdEJDLFVBQVVwQyxRQUFRb0MsUUFBUTs0QkFDMUJDLFdBQVdWOzRCQUNYeEIsV0FBV3lCLFdBQVcsYUFBYTs0QkFDbkN2QixNQUFNTCxRQUFRRSxPQUFPLENBQUNHLElBQUk7d0JBQzVCO3dCQUNBVyxrQkFBa0JrQjt3QkFDbEIsT0FBT0E7b0JBQ1Q7b0JBQ0EsTUFBTUksaUJBQWlCTDtvQkFDdkIsTUFBTU0sT0FBTyxNQUFNZCxRQUFRYTtvQkFDM0IsTUFBTSxFQUFFRSxRQUFRLEVBQUUsR0FBR3hDLFFBQVFFLE9BQU87b0JBQ3BDLE1BQU11QyxRQUFRYixXQUFXakMsaURBQVVBLEdBQUdELCtDQUFRQTtvQkFDOUMsT0FBTzt3QkFDTEksT0FBTzJDLE1BQU1oQyxLQUFLWCxLQUFLLEVBQUV5QyxNQUFNQzt3QkFDL0I3QixZQUFZOEIsTUFBTWhDLEtBQUtFLFVBQVUsRUFBRWdCLE9BQU9hO29CQUM1QztnQkFDRjtnQkFDQSxJQUFJckMsYUFBYUksU0FBU3dCLE1BQU0sRUFBRTtvQkFDaEMsTUFBTUgsV0FBV3pCLGNBQWM7b0JBQy9CLE1BQU11QyxjQUFjZCxXQUFXZSx1QkFBdUJDO29CQUN0RCxNQUFNQyxVQUFVO3dCQUNkL0MsT0FBT1M7d0JBQ1BJLFlBQVlEO29CQUNkO29CQUNBLE1BQU1pQixRQUFRZSxZQUFZeEMsU0FBUzJDO29CQUNuQ2pDLFNBQVMsTUFBTWMsVUFBVW1CLFNBQVNsQixPQUFPQztnQkFDM0MsT0FBTztvQkFDTCxNQUFNa0IsaUJBQWlCaEQsU0FBU1MsU0FBU3dCLE1BQU07b0JBQy9DLEdBQUc7d0JBQ0QsTUFBTUosUUFBUWQsZ0JBQWdCLElBQUlILGFBQWEsQ0FBQyxFQUFFLElBQUlSLFFBQVE2QyxnQkFBZ0IsR0FBR0gsaUJBQWlCMUMsU0FBU1U7d0JBQzNHLElBQUlDLGNBQWMsS0FBS2MsU0FBUyxNQUFNOzRCQUNwQzt3QkFDRjt3QkFDQWYsU0FBUyxNQUFNYyxVQUFVZCxRQUFRZTt3QkFDakNkO29CQUNGLFFBQVNBLGNBQWNpQyxnQkFBZ0I7Z0JBQ3pDO2dCQUNBLE9BQU9sQztZQUNUO1lBQ0EsSUFBSVosUUFBUUUsT0FBTyxDQUFDOEMsU0FBUyxFQUFFO2dCQUM3QmhELFFBQVFjLE9BQU8sR0FBRztvQkFDaEIsT0FBT2QsUUFBUUUsT0FBTyxDQUFDOEMsU0FBUyxHQUM5QmxDLFNBQ0E7d0JBQ0VxQixRQUFRbkMsUUFBUW1DLE1BQU07d0JBQ3RCQyxVQUFVcEMsUUFBUW9DLFFBQVE7d0JBQzFCL0IsTUFBTUwsUUFBUUUsT0FBTyxDQUFDRyxJQUFJO3dCQUMxQmlCLFFBQVF0QixRQUFRc0IsTUFBTTtvQkFDeEIsR0FDQXJCO2dCQUVKO1lBQ0YsT0FBTztnQkFDTEQsUUFBUWMsT0FBTyxHQUFHQTtZQUNwQjtRQUNGO0lBQ0Y7QUFDRjtBQUNBLFNBQVM4QixpQkFBaUIxQyxPQUFPLEVBQUUsRUFBRUosS0FBSyxFQUFFYSxVQUFVLEVBQUU7SUFDdEQsTUFBTXNDLFlBQVluRCxNQUFNaUMsTUFBTSxHQUFHO0lBQ2pDLE9BQU9qQyxNQUFNaUMsTUFBTSxHQUFHLElBQUk3QixRQUFRMEMsZ0JBQWdCLENBQ2hEOUMsS0FBSyxDQUFDbUQsVUFBVSxFQUNoQm5ELE9BQ0FhLFVBQVUsQ0FBQ3NDLFVBQVUsRUFDckJ0QyxjQUNFLEtBQUs7QUFDWDtBQUNBLFNBQVNnQyxxQkFBcUJ6QyxPQUFPLEVBQUUsRUFBRUosS0FBSyxFQUFFYSxVQUFVLEVBQUU7SUFDMUQsT0FBT2IsTUFBTWlDLE1BQU0sR0FBRyxJQUFJN0IsUUFBUXlDLG9CQUFvQixHQUFHN0MsS0FBSyxDQUFDLEVBQUUsRUFBRUEsT0FBT2EsVUFBVSxDQUFDLEVBQUUsRUFBRUEsY0FBYyxLQUFLO0FBQzlHO0FBQ0EsU0FBU3VDLFlBQVloRCxPQUFPLEVBQUVPLElBQUk7SUFDaEMsSUFBSSxDQUFDQSxNQUFNLE9BQU87SUFDbEIsT0FBT21DLGlCQUFpQjFDLFNBQVNPLFNBQVM7QUFDNUM7QUFDQSxTQUFTMEMsZ0JBQWdCakQsT0FBTyxFQUFFTyxJQUFJO0lBQ3BDLElBQUksQ0FBQ0EsUUFBUSxDQUFDUCxRQUFReUMsb0JBQW9CLEVBQUUsT0FBTztJQUNuRCxPQUFPQSxxQkFBcUJ6QyxTQUFTTyxTQUFTO0FBQ2hEO0FBS0UsQ0FDRixpREFBaUQiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9AZnJlZWxhL2FkbWluLWRhc2hib2FyZC8uLi8uLi9ub2RlX21vZHVsZXMvQHRhbnN0YWNrL3F1ZXJ5LWNvcmUvYnVpbGQvbW9kZXJuL2luZmluaXRlUXVlcnlCZWhhdmlvci5qcz82ODBkIl0sInNvdXJjZXNDb250ZW50IjpbIi8vIHNyYy9pbmZpbml0ZVF1ZXJ5QmVoYXZpb3IudHNcbmltcG9ydCB7IGFkZFRvRW5kLCBhZGRUb1N0YXJ0LCBlbnN1cmVRdWVyeUZuIH0gZnJvbSBcIi4vdXRpbHMuanNcIjtcbmZ1bmN0aW9uIGluZmluaXRlUXVlcnlCZWhhdmlvcihwYWdlcykge1xuICByZXR1cm4ge1xuICAgIG9uRmV0Y2g6IChjb250ZXh0LCBxdWVyeSkgPT4ge1xuICAgICAgY29uc3Qgb3B0aW9ucyA9IGNvbnRleHQub3B0aW9ucztcbiAgICAgIGNvbnN0IGRpcmVjdGlvbiA9IGNvbnRleHQuZmV0Y2hPcHRpb25zPy5tZXRhPy5mZXRjaE1vcmU/LmRpcmVjdGlvbjtcbiAgICAgIGNvbnN0IG9sZFBhZ2VzID0gY29udGV4dC5zdGF0ZS5kYXRhPy5wYWdlcyB8fCBbXTtcbiAgICAgIGNvbnN0IG9sZFBhZ2VQYXJhbXMgPSBjb250ZXh0LnN0YXRlLmRhdGE/LnBhZ2VQYXJhbXMgfHwgW107XG4gICAgICBsZXQgcmVzdWx0ID0geyBwYWdlczogW10sIHBhZ2VQYXJhbXM6IFtdIH07XG4gICAgICBsZXQgY3VycmVudFBhZ2UgPSAwO1xuICAgICAgY29uc3QgZmV0Y2hGbiA9IGFzeW5jICgpID0+IHtcbiAgICAgICAgbGV0IGNhbmNlbGxlZCA9IGZhbHNlO1xuICAgICAgICBjb25zdCBhZGRTaWduYWxQcm9wZXJ0eSA9IChvYmplY3QpID0+IHtcbiAgICAgICAgICBPYmplY3QuZGVmaW5lUHJvcGVydHkob2JqZWN0LCBcInNpZ25hbFwiLCB7XG4gICAgICAgICAgICBlbnVtZXJhYmxlOiB0cnVlLFxuICAgICAgICAgICAgZ2V0OiAoKSA9PiB7XG4gICAgICAgICAgICAgIGlmIChjb250ZXh0LnNpZ25hbC5hYm9ydGVkKSB7XG4gICAgICAgICAgICAgICAgY2FuY2VsbGVkID0gdHJ1ZTtcbiAgICAgICAgICAgICAgfSBlbHNlIHtcbiAgICAgICAgICAgICAgICBjb250ZXh0LnNpZ25hbC5hZGRFdmVudExpc3RlbmVyKFwiYWJvcnRcIiwgKCkgPT4ge1xuICAgICAgICAgICAgICAgICAgY2FuY2VsbGVkID0gdHJ1ZTtcbiAgICAgICAgICAgICAgICB9KTtcbiAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICByZXR1cm4gY29udGV4dC5zaWduYWw7XG4gICAgICAgICAgICB9XG4gICAgICAgICAgfSk7XG4gICAgICAgIH07XG4gICAgICAgIGNvbnN0IHF1ZXJ5Rm4gPSBlbnN1cmVRdWVyeUZuKGNvbnRleHQub3B0aW9ucywgY29udGV4dC5mZXRjaE9wdGlvbnMpO1xuICAgICAgICBjb25zdCBmZXRjaFBhZ2UgPSBhc3luYyAoZGF0YSwgcGFyYW0sIHByZXZpb3VzKSA9PiB7XG4gICAgICAgICAgaWYgKGNhbmNlbGxlZCkge1xuICAgICAgICAgICAgcmV0dXJuIFByb21pc2UucmVqZWN0KCk7XG4gICAgICAgICAgfVxuICAgICAgICAgIGlmIChwYXJhbSA9PSBudWxsICYmIGRhdGEucGFnZXMubGVuZ3RoKSB7XG4gICAgICAgICAgICByZXR1cm4gUHJvbWlzZS5yZXNvbHZlKGRhdGEpO1xuICAgICAgICAgIH1cbiAgICAgICAgICBjb25zdCBjcmVhdGVRdWVyeUZuQ29udGV4dCA9ICgpID0+IHtcbiAgICAgICAgICAgIGNvbnN0IHF1ZXJ5Rm5Db250ZXh0MiA9IHtcbiAgICAgICAgICAgICAgY2xpZW50OiBjb250ZXh0LmNsaWVudCxcbiAgICAgICAgICAgICAgcXVlcnlLZXk6IGNvbnRleHQucXVlcnlLZXksXG4gICAgICAgICAgICAgIHBhZ2VQYXJhbTogcGFyYW0sXG4gICAgICAgICAgICAgIGRpcmVjdGlvbjogcHJldmlvdXMgPyBcImJhY2t3YXJkXCIgOiBcImZvcndhcmRcIixcbiAgICAgICAgICAgICAgbWV0YTogY29udGV4dC5vcHRpb25zLm1ldGFcbiAgICAgICAgICAgIH07XG4gICAgICAgICAgICBhZGRTaWduYWxQcm9wZXJ0eShxdWVyeUZuQ29udGV4dDIpO1xuICAgICAgICAgICAgcmV0dXJuIHF1ZXJ5Rm5Db250ZXh0MjtcbiAgICAgICAgICB9O1xuICAgICAgICAgIGNvbnN0IHF1ZXJ5Rm5Db250ZXh0ID0gY3JlYXRlUXVlcnlGbkNvbnRleHQoKTtcbiAgICAgICAgICBjb25zdCBwYWdlID0gYXdhaXQgcXVlcnlGbihxdWVyeUZuQ29udGV4dCk7XG4gICAgICAgICAgY29uc3QgeyBtYXhQYWdlcyB9ID0gY29udGV4dC5vcHRpb25zO1xuICAgICAgICAgIGNvbnN0IGFkZFRvID0gcHJldmlvdXMgPyBhZGRUb1N0YXJ0IDogYWRkVG9FbmQ7XG4gICAgICAgICAgcmV0dXJuIHtcbiAgICAgICAgICAgIHBhZ2VzOiBhZGRUbyhkYXRhLnBhZ2VzLCBwYWdlLCBtYXhQYWdlcyksXG4gICAgICAgICAgICBwYWdlUGFyYW1zOiBhZGRUbyhkYXRhLnBhZ2VQYXJhbXMsIHBhcmFtLCBtYXhQYWdlcylcbiAgICAgICAgICB9O1xuICAgICAgICB9O1xuICAgICAgICBpZiAoZGlyZWN0aW9uICYmIG9sZFBhZ2VzLmxlbmd0aCkge1xuICAgICAgICAgIGNvbnN0IHByZXZpb3VzID0gZGlyZWN0aW9uID09PSBcImJhY2t3YXJkXCI7XG4gICAgICAgICAgY29uc3QgcGFnZVBhcmFtRm4gPSBwcmV2aW91cyA/IGdldFByZXZpb3VzUGFnZVBhcmFtIDogZ2V0TmV4dFBhZ2VQYXJhbTtcbiAgICAgICAgICBjb25zdCBvbGREYXRhID0ge1xuICAgICAgICAgICAgcGFnZXM6IG9sZFBhZ2VzLFxuICAgICAgICAgICAgcGFnZVBhcmFtczogb2xkUGFnZVBhcmFtc1xuICAgICAgICAgIH07XG4gICAgICAgICAgY29uc3QgcGFyYW0gPSBwYWdlUGFyYW1GbihvcHRpb25zLCBvbGREYXRhKTtcbiAgICAgICAgICByZXN1bHQgPSBhd2FpdCBmZXRjaFBhZ2Uob2xkRGF0YSwgcGFyYW0sIHByZXZpb3VzKTtcbiAgICAgICAgfSBlbHNlIHtcbiAgICAgICAgICBjb25zdCByZW1haW5pbmdQYWdlcyA9IHBhZ2VzID8/IG9sZFBhZ2VzLmxlbmd0aDtcbiAgICAgICAgICBkbyB7XG4gICAgICAgICAgICBjb25zdCBwYXJhbSA9IGN1cnJlbnRQYWdlID09PSAwID8gb2xkUGFnZVBhcmFtc1swXSA/PyBvcHRpb25zLmluaXRpYWxQYWdlUGFyYW0gOiBnZXROZXh0UGFnZVBhcmFtKG9wdGlvbnMsIHJlc3VsdCk7XG4gICAgICAgICAgICBpZiAoY3VycmVudFBhZ2UgPiAwICYmIHBhcmFtID09IG51bGwpIHtcbiAgICAgICAgICAgICAgYnJlYWs7XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICByZXN1bHQgPSBhd2FpdCBmZXRjaFBhZ2UocmVzdWx0LCBwYXJhbSk7XG4gICAgICAgICAgICBjdXJyZW50UGFnZSsrO1xuICAgICAgICAgIH0gd2hpbGUgKGN1cnJlbnRQYWdlIDwgcmVtYWluaW5nUGFnZXMpO1xuICAgICAgICB9XG4gICAgICAgIHJldHVybiByZXN1bHQ7XG4gICAgICB9O1xuICAgICAgaWYgKGNvbnRleHQub3B0aW9ucy5wZXJzaXN0ZXIpIHtcbiAgICAgICAgY29udGV4dC5mZXRjaEZuID0gKCkgPT4ge1xuICAgICAgICAgIHJldHVybiBjb250ZXh0Lm9wdGlvbnMucGVyc2lzdGVyPy4oXG4gICAgICAgICAgICBmZXRjaEZuLFxuICAgICAgICAgICAge1xuICAgICAgICAgICAgICBjbGllbnQ6IGNvbnRleHQuY2xpZW50LFxuICAgICAgICAgICAgICBxdWVyeUtleTogY29udGV4dC5xdWVyeUtleSxcbiAgICAgICAgICAgICAgbWV0YTogY29udGV4dC5vcHRpb25zLm1ldGEsXG4gICAgICAgICAgICAgIHNpZ25hbDogY29udGV4dC5zaWduYWxcbiAgICAgICAgICAgIH0sXG4gICAgICAgICAgICBxdWVyeVxuICAgICAgICAgICk7XG4gICAgICAgIH07XG4gICAgICB9IGVsc2Uge1xuICAgICAgICBjb250ZXh0LmZldGNoRm4gPSBmZXRjaEZuO1xuICAgICAgfVxuICAgIH1cbiAgfTtcbn1cbmZ1bmN0aW9uIGdldE5leHRQYWdlUGFyYW0ob3B0aW9ucywgeyBwYWdlcywgcGFnZVBhcmFtcyB9KSB7XG4gIGNvbnN0IGxhc3RJbmRleCA9IHBhZ2VzLmxlbmd0aCAtIDE7XG4gIHJldHVybiBwYWdlcy5sZW5ndGggPiAwID8gb3B0aW9ucy5nZXROZXh0UGFnZVBhcmFtKFxuICAgIHBhZ2VzW2xhc3RJbmRleF0sXG4gICAgcGFnZXMsXG4gICAgcGFnZVBhcmFtc1tsYXN0SW5kZXhdLFxuICAgIHBhZ2VQYXJhbXNcbiAgKSA6IHZvaWQgMDtcbn1cbmZ1bmN0aW9uIGdldFByZXZpb3VzUGFnZVBhcmFtKG9wdGlvbnMsIHsgcGFnZXMsIHBhZ2VQYXJhbXMgfSkge1xuICByZXR1cm4gcGFnZXMubGVuZ3RoID4gMCA/IG9wdGlvbnMuZ2V0UHJldmlvdXNQYWdlUGFyYW0/LihwYWdlc1swXSwgcGFnZXMsIHBhZ2VQYXJhbXNbMF0sIHBhZ2VQYXJhbXMpIDogdm9pZCAwO1xufVxuZnVuY3Rpb24gaGFzTmV4dFBhZ2Uob3B0aW9ucywgZGF0YSkge1xuICBpZiAoIWRhdGEpIHJldHVybiBmYWxzZTtcbiAgcmV0dXJuIGdldE5leHRQYWdlUGFyYW0ob3B0aW9ucywgZGF0YSkgIT0gbnVsbDtcbn1cbmZ1bmN0aW9uIGhhc1ByZXZpb3VzUGFnZShvcHRpb25zLCBkYXRhKSB7XG4gIGlmICghZGF0YSB8fCAhb3B0aW9ucy5nZXRQcmV2aW91c1BhZ2VQYXJhbSkgcmV0dXJuIGZhbHNlO1xuICByZXR1cm4gZ2V0UHJldmlvdXNQYWdlUGFyYW0ob3B0aW9ucywgZGF0YSkgIT0gbnVsbDtcbn1cbmV4cG9ydCB7XG4gIGhhc05leHRQYWdlLFxuICBoYXNQcmV2aW91c1BhZ2UsXG4gIGluZmluaXRlUXVlcnlCZWhhdmlvclxufTtcbi8vIyBzb3VyY2VNYXBwaW5nVVJMPWluZmluaXRlUXVlcnlCZWhhdmlvci5qcy5tYXAiXSwibmFtZXMiOlsiYWRkVG9FbmQiLCJhZGRUb1N0YXJ0IiwiZW5zdXJlUXVlcnlGbiIsImluZmluaXRlUXVlcnlCZWhhdmlvciIsInBhZ2VzIiwib25GZXRjaCIsImNvbnRleHQiLCJxdWVyeSIsIm9wdGlvbnMiLCJkaXJlY3Rpb24iLCJmZXRjaE9wdGlvbnMiLCJtZXRhIiwiZmV0Y2hNb3JlIiwib2xkUGFnZXMiLCJzdGF0ZSIsImRhdGEiLCJvbGRQYWdlUGFyYW1zIiwicGFnZVBhcmFtcyIsInJlc3VsdCIsImN1cnJlbnRQYWdlIiwiZmV0Y2hGbiIsImNhbmNlbGxlZCIsImFkZFNpZ25hbFByb3BlcnR5Iiwib2JqZWN0IiwiT2JqZWN0IiwiZGVmaW5lUHJvcGVydHkiLCJlbnVtZXJhYmxlIiwiZ2V0Iiwic2lnbmFsIiwiYWJvcnRlZCIsImFkZEV2ZW50TGlzdGVuZXIiLCJxdWVyeUZuIiwiZmV0Y2hQYWdlIiwicGFyYW0iLCJwcmV2aW91cyIsIlByb21pc2UiLCJyZWplY3QiLCJsZW5ndGgiLCJyZXNvbHZlIiwiY3JlYXRlUXVlcnlGbkNvbnRleHQiLCJxdWVyeUZuQ29udGV4dDIiLCJjbGllbnQiLCJxdWVyeUtleSIsInBhZ2VQYXJhbSIsInF1ZXJ5Rm5Db250ZXh0IiwicGFnZSIsIm1heFBhZ2VzIiwiYWRkVG8iLCJwYWdlUGFyYW1GbiIsImdldFByZXZpb3VzUGFnZVBhcmFtIiwiZ2V0TmV4dFBhZ2VQYXJhbSIsIm9sZERhdGEiLCJyZW1haW5pbmdQYWdlcyIsImluaXRpYWxQYWdlUGFyYW0iLCJwZXJzaXN0ZXIiLCJsYXN0SW5kZXgiLCJoYXNOZXh0UGFnZSIsImhhc1ByZXZpb3VzUGFnZSJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/@tanstack/query-core/build/modern/infiniteQueryBehavior.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/@tanstack/query-core/build/modern/mutation.js":
/*!************************************************************************!*\
  !*** ../../node_modules/@tanstack/query-core/build/modern/mutation.js ***!
  \************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Mutation: () => (/* binding */ Mutation),\n/* harmony export */   getDefaultState: () => (/* binding */ getDefaultState)\n/* harmony export */ });\n/* harmony import */ var _notifyManager_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./notifyManager.js */ \"(ssr)/../../node_modules/@tanstack/query-core/build/modern/notifyManager.js\");\n/* harmony import */ var _removable_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./removable.js */ \"(ssr)/../../node_modules/@tanstack/query-core/build/modern/removable.js\");\n/* harmony import */ var _retryer_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./retryer.js */ \"(ssr)/../../node_modules/@tanstack/query-core/build/modern/retryer.js\");\n// src/mutation.ts\n\n\n\nvar Mutation = class extends _removable_js__WEBPACK_IMPORTED_MODULE_0__.Removable {\n    #observers;\n    #mutationCache;\n    #retryer;\n    constructor(config){\n        super();\n        this.mutationId = config.mutationId;\n        this.#mutationCache = config.mutationCache;\n        this.#observers = [];\n        this.state = config.state || getDefaultState();\n        this.setOptions(config.options);\n        this.scheduleGc();\n    }\n    setOptions(options) {\n        this.options = options;\n        this.updateGcTime(this.options.gcTime);\n    }\n    get meta() {\n        return this.options.meta;\n    }\n    addObserver(observer) {\n        if (!this.#observers.includes(observer)) {\n            this.#observers.push(observer);\n            this.clearGcTimeout();\n            this.#mutationCache.notify({\n                type: \"observerAdded\",\n                mutation: this,\n                observer\n            });\n        }\n    }\n    removeObserver(observer) {\n        this.#observers = this.#observers.filter((x)=>x !== observer);\n        this.scheduleGc();\n        this.#mutationCache.notify({\n            type: \"observerRemoved\",\n            mutation: this,\n            observer\n        });\n    }\n    optionalRemove() {\n        if (!this.#observers.length) {\n            if (this.state.status === \"pending\") {\n                this.scheduleGc();\n            } else {\n                this.#mutationCache.remove(this);\n            }\n        }\n    }\n    continue() {\n        return this.#retryer?.continue() ?? // continuing a mutation assumes that variables are set, mutation must have been dehydrated before\n        this.execute(this.state.variables);\n    }\n    async execute(variables) {\n        const onContinue = ()=>{\n            this.#dispatch({\n                type: \"continue\"\n            });\n        };\n        this.#retryer = (0,_retryer_js__WEBPACK_IMPORTED_MODULE_1__.createRetryer)({\n            fn: ()=>{\n                if (!this.options.mutationFn) {\n                    return Promise.reject(new Error(\"No mutationFn found\"));\n                }\n                return this.options.mutationFn(variables);\n            },\n            onFail: (failureCount, error)=>{\n                this.#dispatch({\n                    type: \"failed\",\n                    failureCount,\n                    error\n                });\n            },\n            onPause: ()=>{\n                this.#dispatch({\n                    type: \"pause\"\n                });\n            },\n            onContinue,\n            retry: this.options.retry ?? 0,\n            retryDelay: this.options.retryDelay,\n            networkMode: this.options.networkMode,\n            canRun: ()=>this.#mutationCache.canRun(this)\n        });\n        const restored = this.state.status === \"pending\";\n        const isPaused = !this.#retryer.canStart();\n        try {\n            if (restored) {\n                onContinue();\n            } else {\n                this.#dispatch({\n                    type: \"pending\",\n                    variables,\n                    isPaused\n                });\n                await this.#mutationCache.config.onMutate?.(variables, this);\n                const context = await this.options.onMutate?.(variables);\n                if (context !== this.state.context) {\n                    this.#dispatch({\n                        type: \"pending\",\n                        context,\n                        variables,\n                        isPaused\n                    });\n                }\n            }\n            const data = await this.#retryer.start();\n            await this.#mutationCache.config.onSuccess?.(data, variables, this.state.context, this);\n            await this.options.onSuccess?.(data, variables, this.state.context);\n            await this.#mutationCache.config.onSettled?.(data, null, this.state.variables, this.state.context, this);\n            await this.options.onSettled?.(data, null, variables, this.state.context);\n            this.#dispatch({\n                type: \"success\",\n                data\n            });\n            return data;\n        } catch (error) {\n            try {\n                await this.#mutationCache.config.onError?.(error, variables, this.state.context, this);\n                await this.options.onError?.(error, variables, this.state.context);\n                await this.#mutationCache.config.onSettled?.(void 0, error, this.state.variables, this.state.context, this);\n                await this.options.onSettled?.(void 0, error, variables, this.state.context);\n                throw error;\n            } finally{\n                this.#dispatch({\n                    type: \"error\",\n                    error\n                });\n            }\n        } finally{\n            this.#mutationCache.runNext(this);\n        }\n    }\n    #dispatch(action) {\n        const reducer = (state)=>{\n            switch(action.type){\n                case \"failed\":\n                    return {\n                        ...state,\n                        failureCount: action.failureCount,\n                        failureReason: action.error\n                    };\n                case \"pause\":\n                    return {\n                        ...state,\n                        isPaused: true\n                    };\n                case \"continue\":\n                    return {\n                        ...state,\n                        isPaused: false\n                    };\n                case \"pending\":\n                    return {\n                        ...state,\n                        context: action.context,\n                        data: void 0,\n                        failureCount: 0,\n                        failureReason: null,\n                        error: null,\n                        isPaused: action.isPaused,\n                        status: \"pending\",\n                        variables: action.variables,\n                        submittedAt: Date.now()\n                    };\n                case \"success\":\n                    return {\n                        ...state,\n                        data: action.data,\n                        failureCount: 0,\n                        failureReason: null,\n                        error: null,\n                        status: \"success\",\n                        isPaused: false\n                    };\n                case \"error\":\n                    return {\n                        ...state,\n                        data: void 0,\n                        error: action.error,\n                        failureCount: state.failureCount + 1,\n                        failureReason: action.error,\n                        isPaused: false,\n                        status: \"error\"\n                    };\n            }\n        };\n        this.state = reducer(this.state);\n        _notifyManager_js__WEBPACK_IMPORTED_MODULE_2__.notifyManager.batch(()=>{\n            this.#observers.forEach((observer)=>{\n                observer.onMutationUpdate(action);\n            });\n            this.#mutationCache.notify({\n                mutation: this,\n                type: \"updated\",\n                action\n            });\n        });\n    }\n};\nfunction getDefaultState() {\n    return {\n        context: void 0,\n        data: void 0,\n        error: null,\n        failureCount: 0,\n        failureReason: null,\n        isPaused: false,\n        status: \"idle\",\n        variables: void 0,\n        submittedAt: 0\n    };\n}\n //# sourceMappingURL=mutation.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/@tanstack/query-core/build/modern/mutation.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/@tanstack/query-core/build/modern/mutationCache.js":
/*!*****************************************************************************!*\
  !*** ../../node_modules/@tanstack/query-core/build/modern/mutationCache.js ***!
  \*****************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   MutationCache: () => (/* binding */ MutationCache)\n/* harmony export */ });\n/* harmony import */ var _notifyManager_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./notifyManager.js */ \"(ssr)/../../node_modules/@tanstack/query-core/build/modern/notifyManager.js\");\n/* harmony import */ var _mutation_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./mutation.js */ \"(ssr)/../../node_modules/@tanstack/query-core/build/modern/mutation.js\");\n/* harmony import */ var _utils_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./utils.js */ \"(ssr)/../../node_modules/@tanstack/query-core/build/modern/utils.js\");\n/* harmony import */ var _subscribable_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./subscribable.js */ \"(ssr)/../../node_modules/@tanstack/query-core/build/modern/subscribable.js\");\n// src/mutationCache.ts\n\n\n\n\nvar MutationCache = class extends _subscribable_js__WEBPACK_IMPORTED_MODULE_0__.Subscribable {\n    constructor(config = {}){\n        super();\n        this.config = config;\n        this.#mutations = /* @__PURE__ */ new Set();\n        this.#scopes = /* @__PURE__ */ new Map();\n        this.#mutationId = 0;\n    }\n    #mutations;\n    #scopes;\n    #mutationId;\n    build(client, options, state) {\n        const mutation = new _mutation_js__WEBPACK_IMPORTED_MODULE_1__.Mutation({\n            mutationCache: this,\n            mutationId: ++this.#mutationId,\n            options: client.defaultMutationOptions(options),\n            state\n        });\n        this.add(mutation);\n        return mutation;\n    }\n    add(mutation) {\n        this.#mutations.add(mutation);\n        const scope = scopeFor(mutation);\n        if (typeof scope === \"string\") {\n            const scopedMutations = this.#scopes.get(scope);\n            if (scopedMutations) {\n                scopedMutations.push(mutation);\n            } else {\n                this.#scopes.set(scope, [\n                    mutation\n                ]);\n            }\n        }\n        this.notify({\n            type: \"added\",\n            mutation\n        });\n    }\n    remove(mutation) {\n        if (this.#mutations.delete(mutation)) {\n            const scope = scopeFor(mutation);\n            if (typeof scope === \"string\") {\n                const scopedMutations = this.#scopes.get(scope);\n                if (scopedMutations) {\n                    if (scopedMutations.length > 1) {\n                        const index = scopedMutations.indexOf(mutation);\n                        if (index !== -1) {\n                            scopedMutations.splice(index, 1);\n                        }\n                    } else if (scopedMutations[0] === mutation) {\n                        this.#scopes.delete(scope);\n                    }\n                }\n            }\n        }\n        this.notify({\n            type: \"removed\",\n            mutation\n        });\n    }\n    canRun(mutation) {\n        const scope = scopeFor(mutation);\n        if (typeof scope === \"string\") {\n            const mutationsWithSameScope = this.#scopes.get(scope);\n            const firstPendingMutation = mutationsWithSameScope?.find((m)=>m.state.status === \"pending\");\n            return !firstPendingMutation || firstPendingMutation === mutation;\n        } else {\n            return true;\n        }\n    }\n    runNext(mutation) {\n        const scope = scopeFor(mutation);\n        if (typeof scope === \"string\") {\n            const foundMutation = this.#scopes.get(scope)?.find((m)=>m !== mutation && m.state.isPaused);\n            return foundMutation?.continue() ?? Promise.resolve();\n        } else {\n            return Promise.resolve();\n        }\n    }\n    clear() {\n        _notifyManager_js__WEBPACK_IMPORTED_MODULE_2__.notifyManager.batch(()=>{\n            this.#mutations.forEach((mutation)=>{\n                this.notify({\n                    type: \"removed\",\n                    mutation\n                });\n            });\n            this.#mutations.clear();\n            this.#scopes.clear();\n        });\n    }\n    getAll() {\n        return Array.from(this.#mutations);\n    }\n    find(filters) {\n        const defaultedFilters = {\n            exact: true,\n            ...filters\n        };\n        return this.getAll().find((mutation)=>(0,_utils_js__WEBPACK_IMPORTED_MODULE_3__.matchMutation)(defaultedFilters, mutation));\n    }\n    findAll(filters = {}) {\n        return this.getAll().filter((mutation)=>(0,_utils_js__WEBPACK_IMPORTED_MODULE_3__.matchMutation)(filters, mutation));\n    }\n    notify(event) {\n        _notifyManager_js__WEBPACK_IMPORTED_MODULE_2__.notifyManager.batch(()=>{\n            this.listeners.forEach((listener)=>{\n                listener(event);\n            });\n        });\n    }\n    resumePausedMutations() {\n        const pausedMutations = this.getAll().filter((x)=>x.state.isPaused);\n        return _notifyManager_js__WEBPACK_IMPORTED_MODULE_2__.notifyManager.batch(()=>Promise.all(pausedMutations.map((mutation)=>mutation.continue().catch(_utils_js__WEBPACK_IMPORTED_MODULE_3__.noop))));\n    }\n};\nfunction scopeFor(mutation) {\n    return mutation.options.scope?.id;\n}\n //# sourceMappingURL=mutationCache.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/@tanstack/query-core/build/modern/mutationCache.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/@tanstack/query-core/build/modern/notifyManager.js":
/*!*****************************************************************************!*\
  !*** ../../node_modules/@tanstack/query-core/build/modern/notifyManager.js ***!
  \*****************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createNotifyManager: () => (/* binding */ createNotifyManager),\n/* harmony export */   defaultScheduler: () => (/* binding */ defaultScheduler),\n/* harmony export */   notifyManager: () => (/* binding */ notifyManager)\n/* harmony export */ });\n// src/notifyManager.ts\nvar defaultScheduler = (cb)=>setTimeout(cb, 0);\nfunction createNotifyManager() {\n    let queue = [];\n    let transactions = 0;\n    let notifyFn = (callback)=>{\n        callback();\n    };\n    let batchNotifyFn = (callback)=>{\n        callback();\n    };\n    let scheduleFn = defaultScheduler;\n    const schedule = (callback)=>{\n        if (transactions) {\n            queue.push(callback);\n        } else {\n            scheduleFn(()=>{\n                notifyFn(callback);\n            });\n        }\n    };\n    const flush = ()=>{\n        const originalQueue = queue;\n        queue = [];\n        if (originalQueue.length) {\n            scheduleFn(()=>{\n                batchNotifyFn(()=>{\n                    originalQueue.forEach((callback)=>{\n                        notifyFn(callback);\n                    });\n                });\n            });\n        }\n    };\n    return {\n        batch: (callback)=>{\n            let result;\n            transactions++;\n            try {\n                result = callback();\n            } finally{\n                transactions--;\n                if (!transactions) {\n                    flush();\n                }\n            }\n            return result;\n        },\n        /**\n     * All calls to the wrapped function will be batched.\n     */ batchCalls: (callback)=>{\n            return (...args)=>{\n                schedule(()=>{\n                    callback(...args);\n                });\n            };\n        },\n        schedule,\n        /**\n     * Use this method to set a custom notify function.\n     * This can be used to for example wrap notifications with `React.act` while running tests.\n     */ setNotifyFunction: (fn)=>{\n            notifyFn = fn;\n        },\n        /**\n     * Use this method to set a custom function to batch notifications together into a single tick.\n     * By default React Query will use the batch function provided by ReactDOM or React Native.\n     */ setBatchNotifyFunction: (fn)=>{\n            batchNotifyFn = fn;\n        },\n        setScheduler: (fn)=>{\n            scheduleFn = fn;\n        }\n    };\n}\nvar notifyManager = createNotifyManager();\n //# sourceMappingURL=notifyManager.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/@tanstack/query-core/build/modern/notifyManager.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/@tanstack/query-core/build/modern/onlineManager.js":
/*!*****************************************************************************!*\
  !*** ../../node_modules/@tanstack/query-core/build/modern/onlineManager.js ***!
  \*****************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   OnlineManager: () => (/* binding */ OnlineManager),\n/* harmony export */   onlineManager: () => (/* binding */ onlineManager)\n/* harmony export */ });\n/* harmony import */ var _subscribable_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./subscribable.js */ \"(ssr)/../../node_modules/@tanstack/query-core/build/modern/subscribable.js\");\n/* harmony import */ var _utils_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./utils.js */ \"(ssr)/../../node_modules/@tanstack/query-core/build/modern/utils.js\");\n// src/onlineManager.ts\n\n\nvar OnlineManager = class extends _subscribable_js__WEBPACK_IMPORTED_MODULE_0__.Subscribable {\n    #online;\n    #cleanup;\n    #setup;\n    constructor(){\n        super();\n        this.#online = true;\n        this.#setup = (onOnline)=>{\n            if (!_utils_js__WEBPACK_IMPORTED_MODULE_1__.isServer && window.addEventListener) {\n                const onlineListener = ()=>onOnline(true);\n                const offlineListener = ()=>onOnline(false);\n                window.addEventListener(\"online\", onlineListener, false);\n                window.addEventListener(\"offline\", offlineListener, false);\n                return ()=>{\n                    window.removeEventListener(\"online\", onlineListener);\n                    window.removeEventListener(\"offline\", offlineListener);\n                };\n            }\n            return;\n        };\n    }\n    onSubscribe() {\n        if (!this.#cleanup) {\n            this.setEventListener(this.#setup);\n        }\n    }\n    onUnsubscribe() {\n        if (!this.hasListeners()) {\n            this.#cleanup?.();\n            this.#cleanup = void 0;\n        }\n    }\n    setEventListener(setup) {\n        this.#setup = setup;\n        this.#cleanup?.();\n        this.#cleanup = setup(this.setOnline.bind(this));\n    }\n    setOnline(online) {\n        const changed = this.#online !== online;\n        if (changed) {\n            this.#online = online;\n            this.listeners.forEach((listener)=>{\n                listener(online);\n            });\n        }\n    }\n    isOnline() {\n        return this.#online;\n    }\n};\nvar onlineManager = new OnlineManager();\n //# sourceMappingURL=onlineManager.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzL0B0YW5zdGFjay9xdWVyeS1jb3JlL2J1aWxkL21vZGVybi9vbmxpbmVNYW5hZ2VyLmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7QUFBQSx1QkFBdUI7QUFDMEI7QUFDWDtBQUN0QyxJQUFJRSxnQkFBZ0IsY0FBY0YsMERBQVlBO0lBQzVDLENBQUNHLE1BQU0sQ0FBUTtJQUNmLENBQUNDLE9BQU8sQ0FBQztJQUNULENBQUNDLEtBQUssQ0FBQztJQUNQQyxhQUFjO1FBQ1osS0FBSzthQUpQLENBQUNILE1BQU0sR0FBRztRQUtSLElBQUksQ0FBQyxDQUFDRSxLQUFLLEdBQUcsQ0FBQ0U7WUFDYixJQUFJLENBQUNOLCtDQUFRQSxJQUFJTyxPQUFPQyxnQkFBZ0IsRUFBRTtnQkFDeEMsTUFBTUMsaUJBQWlCLElBQU1ILFNBQVM7Z0JBQ3RDLE1BQU1JLGtCQUFrQixJQUFNSixTQUFTO2dCQUN2Q0MsT0FBT0MsZ0JBQWdCLENBQUMsVUFBVUMsZ0JBQWdCO2dCQUNsREYsT0FBT0MsZ0JBQWdCLENBQUMsV0FBV0UsaUJBQWlCO2dCQUNwRCxPQUFPO29CQUNMSCxPQUFPSSxtQkFBbUIsQ0FBQyxVQUFVRjtvQkFDckNGLE9BQU9JLG1CQUFtQixDQUFDLFdBQVdEO2dCQUN4QztZQUNGO1lBQ0E7UUFDRjtJQUNGO0lBQ0FFLGNBQWM7UUFDWixJQUFJLENBQUMsSUFBSSxDQUFDLENBQUNULE9BQU8sRUFBRTtZQUNsQixJQUFJLENBQUNVLGdCQUFnQixDQUFDLElBQUksQ0FBQyxDQUFDVCxLQUFLO1FBQ25DO0lBQ0Y7SUFDQVUsZ0JBQWdCO1FBQ2QsSUFBSSxDQUFDLElBQUksQ0FBQ0MsWUFBWSxJQUFJO1lBQ3hCLElBQUksQ0FBQyxDQUFDWixPQUFPO1lBQ2IsSUFBSSxDQUFDLENBQUNBLE9BQU8sR0FBRyxLQUFLO1FBQ3ZCO0lBQ0Y7SUFDQVUsaUJBQWlCVCxLQUFLLEVBQUU7UUFDdEIsSUFBSSxDQUFDLENBQUNBLEtBQUssR0FBR0E7UUFDZCxJQUFJLENBQUMsQ0FBQ0QsT0FBTztRQUNiLElBQUksQ0FBQyxDQUFDQSxPQUFPLEdBQUdDLE1BQU0sSUFBSSxDQUFDWSxTQUFTLENBQUNDLElBQUksQ0FBQyxJQUFJO0lBQ2hEO0lBQ0FELFVBQVVkLE1BQU0sRUFBRTtRQUNoQixNQUFNZ0IsVUFBVSxJQUFJLENBQUMsQ0FBQ2hCLE1BQU0sS0FBS0E7UUFDakMsSUFBSWdCLFNBQVM7WUFDWCxJQUFJLENBQUMsQ0FBQ2hCLE1BQU0sR0FBR0E7WUFDZixJQUFJLENBQUNpQixTQUFTLENBQUNDLE9BQU8sQ0FBQyxDQUFDQztnQkFDdEJBLFNBQVNuQjtZQUNYO1FBQ0Y7SUFDRjtJQUNBb0IsV0FBVztRQUNULE9BQU8sSUFBSSxDQUFDLENBQUNwQixNQUFNO0lBQ3JCO0FBQ0Y7QUFDQSxJQUFJcUIsZ0JBQWdCLElBQUl0QjtBQUl0QixDQUNGLHlDQUF5QyIsInNvdXJjZXMiOlsid2VicGFjazovL0BmcmVlbGEvYWRtaW4tZGFzaGJvYXJkLy4uLy4uL25vZGVfbW9kdWxlcy9AdGFuc3RhY2svcXVlcnktY29yZS9idWlsZC9tb2Rlcm4vb25saW5lTWFuYWdlci5qcz9iZTY4Il0sInNvdXJjZXNDb250ZW50IjpbIi8vIHNyYy9vbmxpbmVNYW5hZ2VyLnRzXG5pbXBvcnQgeyBTdWJzY3JpYmFibGUgfSBmcm9tIFwiLi9zdWJzY3JpYmFibGUuanNcIjtcbmltcG9ydCB7IGlzU2VydmVyIH0gZnJvbSBcIi4vdXRpbHMuanNcIjtcbnZhciBPbmxpbmVNYW5hZ2VyID0gY2xhc3MgZXh0ZW5kcyBTdWJzY3JpYmFibGUge1xuICAjb25saW5lID0gdHJ1ZTtcbiAgI2NsZWFudXA7XG4gICNzZXR1cDtcbiAgY29uc3RydWN0b3IoKSB7XG4gICAgc3VwZXIoKTtcbiAgICB0aGlzLiNzZXR1cCA9IChvbk9ubGluZSkgPT4ge1xuICAgICAgaWYgKCFpc1NlcnZlciAmJiB3aW5kb3cuYWRkRXZlbnRMaXN0ZW5lcikge1xuICAgICAgICBjb25zdCBvbmxpbmVMaXN0ZW5lciA9ICgpID0+IG9uT25saW5lKHRydWUpO1xuICAgICAgICBjb25zdCBvZmZsaW5lTGlzdGVuZXIgPSAoKSA9PiBvbk9ubGluZShmYWxzZSk7XG4gICAgICAgIHdpbmRvdy5hZGRFdmVudExpc3RlbmVyKFwib25saW5lXCIsIG9ubGluZUxpc3RlbmVyLCBmYWxzZSk7XG4gICAgICAgIHdpbmRvdy5hZGRFdmVudExpc3RlbmVyKFwib2ZmbGluZVwiLCBvZmZsaW5lTGlzdGVuZXIsIGZhbHNlKTtcbiAgICAgICAgcmV0dXJuICgpID0+IHtcbiAgICAgICAgICB3aW5kb3cucmVtb3ZlRXZlbnRMaXN0ZW5lcihcIm9ubGluZVwiLCBvbmxpbmVMaXN0ZW5lcik7XG4gICAgICAgICAgd2luZG93LnJlbW92ZUV2ZW50TGlzdGVuZXIoXCJvZmZsaW5lXCIsIG9mZmxpbmVMaXN0ZW5lcik7XG4gICAgICAgIH07XG4gICAgICB9XG4gICAgICByZXR1cm47XG4gICAgfTtcbiAgfVxuICBvblN1YnNjcmliZSgpIHtcbiAgICBpZiAoIXRoaXMuI2NsZWFudXApIHtcbiAgICAgIHRoaXMuc2V0RXZlbnRMaXN0ZW5lcih0aGlzLiNzZXR1cCk7XG4gICAgfVxuICB9XG4gIG9uVW5zdWJzY3JpYmUoKSB7XG4gICAgaWYgKCF0aGlzLmhhc0xpc3RlbmVycygpKSB7XG4gICAgICB0aGlzLiNjbGVhbnVwPy4oKTtcbiAgICAgIHRoaXMuI2NsZWFudXAgPSB2b2lkIDA7XG4gICAgfVxuICB9XG4gIHNldEV2ZW50TGlzdGVuZXIoc2V0dXApIHtcbiAgICB0aGlzLiNzZXR1cCA9IHNldHVwO1xuICAgIHRoaXMuI2NsZWFudXA/LigpO1xuICAgIHRoaXMuI2NsZWFudXAgPSBzZXR1cCh0aGlzLnNldE9ubGluZS5iaW5kKHRoaXMpKTtcbiAgfVxuICBzZXRPbmxpbmUob25saW5lKSB7XG4gICAgY29uc3QgY2hhbmdlZCA9IHRoaXMuI29ubGluZSAhPT0gb25saW5lO1xuICAgIGlmIChjaGFuZ2VkKSB7XG4gICAgICB0aGlzLiNvbmxpbmUgPSBvbmxpbmU7XG4gICAgICB0aGlzLmxpc3RlbmVycy5mb3JFYWNoKChsaXN0ZW5lcikgPT4ge1xuICAgICAgICBsaXN0ZW5lcihvbmxpbmUpO1xuICAgICAgfSk7XG4gICAgfVxuICB9XG4gIGlzT25saW5lKCkge1xuICAgIHJldHVybiB0aGlzLiNvbmxpbmU7XG4gIH1cbn07XG52YXIgb25saW5lTWFuYWdlciA9IG5ldyBPbmxpbmVNYW5hZ2VyKCk7XG5leHBvcnQge1xuICBPbmxpbmVNYW5hZ2VyLFxuICBvbmxpbmVNYW5hZ2VyXG59O1xuLy8jIHNvdXJjZU1hcHBpbmdVUkw9b25saW5lTWFuYWdlci5qcy5tYXAiXSwibmFtZXMiOlsiU3Vic2NyaWJhYmxlIiwiaXNTZXJ2ZXIiLCJPbmxpbmVNYW5hZ2VyIiwib25saW5lIiwiY2xlYW51cCIsInNldHVwIiwiY29uc3RydWN0b3IiLCJvbk9ubGluZSIsIndpbmRvdyIsImFkZEV2ZW50TGlzdGVuZXIiLCJvbmxpbmVMaXN0ZW5lciIsIm9mZmxpbmVMaXN0ZW5lciIsInJlbW92ZUV2ZW50TGlzdGVuZXIiLCJvblN1YnNjcmliZSIsInNldEV2ZW50TGlzdGVuZXIiLCJvblVuc3Vic2NyaWJlIiwiaGFzTGlzdGVuZXJzIiwic2V0T25saW5lIiwiYmluZCIsImNoYW5nZWQiLCJsaXN0ZW5lcnMiLCJmb3JFYWNoIiwibGlzdGVuZXIiLCJpc09ubGluZSIsIm9ubGluZU1hbmFnZXIiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/@tanstack/query-core/build/modern/onlineManager.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/@tanstack/query-core/build/modern/query.js":
/*!*********************************************************************!*\
  !*** ../../node_modules/@tanstack/query-core/build/modern/query.js ***!
  \*********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Query: () => (/* binding */ Query),\n/* harmony export */   fetchState: () => (/* binding */ fetchState)\n/* harmony export */ });\n/* harmony import */ var _utils_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./utils.js */ \"(ssr)/../../node_modules/@tanstack/query-core/build/modern/utils.js\");\n/* harmony import */ var _notifyManager_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./notifyManager.js */ \"(ssr)/../../node_modules/@tanstack/query-core/build/modern/notifyManager.js\");\n/* harmony import */ var _retryer_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./retryer.js */ \"(ssr)/../../node_modules/@tanstack/query-core/build/modern/retryer.js\");\n/* harmony import */ var _removable_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./removable.js */ \"(ssr)/../../node_modules/@tanstack/query-core/build/modern/removable.js\");\n// src/query.ts\n\n\n\n\nvar Query = class extends _removable_js__WEBPACK_IMPORTED_MODULE_0__.Removable {\n    #initialState;\n    #revertState;\n    #cache;\n    #client;\n    #retryer;\n    #defaultOptions;\n    #abortSignalConsumed;\n    constructor(config){\n        super();\n        this.#abortSignalConsumed = false;\n        this.#defaultOptions = config.defaultOptions;\n        this.setOptions(config.options);\n        this.observers = [];\n        this.#client = config.client;\n        this.#cache = this.#client.getQueryCache();\n        this.queryKey = config.queryKey;\n        this.queryHash = config.queryHash;\n        this.#initialState = getDefaultState(this.options);\n        this.state = config.state ?? this.#initialState;\n        this.scheduleGc();\n    }\n    get meta() {\n        return this.options.meta;\n    }\n    get promise() {\n        return this.#retryer?.promise;\n    }\n    setOptions(options) {\n        this.options = {\n            ...this.#defaultOptions,\n            ...options\n        };\n        this.updateGcTime(this.options.gcTime);\n    }\n    optionalRemove() {\n        if (!this.observers.length && this.state.fetchStatus === \"idle\") {\n            this.#cache.remove(this);\n        }\n    }\n    setData(newData, options) {\n        const data = (0,_utils_js__WEBPACK_IMPORTED_MODULE_1__.replaceData)(this.state.data, newData, this.options);\n        this.#dispatch({\n            data,\n            type: \"success\",\n            dataUpdatedAt: options?.updatedAt,\n            manual: options?.manual\n        });\n        return data;\n    }\n    setState(state, setStateOptions) {\n        this.#dispatch({\n            type: \"setState\",\n            state,\n            setStateOptions\n        });\n    }\n    cancel(options) {\n        const promise = this.#retryer?.promise;\n        this.#retryer?.cancel(options);\n        return promise ? promise.then(_utils_js__WEBPACK_IMPORTED_MODULE_1__.noop).catch(_utils_js__WEBPACK_IMPORTED_MODULE_1__.noop) : Promise.resolve();\n    }\n    destroy() {\n        super.destroy();\n        this.cancel({\n            silent: true\n        });\n    }\n    reset() {\n        this.destroy();\n        this.setState(this.#initialState);\n    }\n    isActive() {\n        return this.observers.some((observer)=>(0,_utils_js__WEBPACK_IMPORTED_MODULE_1__.resolveEnabled)(observer.options.enabled, this) !== false);\n    }\n    isDisabled() {\n        if (this.getObserversCount() > 0) {\n            return !this.isActive();\n        }\n        return this.options.queryFn === _utils_js__WEBPACK_IMPORTED_MODULE_1__.skipToken || this.state.dataUpdateCount + this.state.errorUpdateCount === 0;\n    }\n    isStatic() {\n        if (this.getObserversCount() > 0) {\n            return this.observers.some((observer)=>(0,_utils_js__WEBPACK_IMPORTED_MODULE_1__.resolveStaleTime)(observer.options.staleTime, this) === \"static\");\n        }\n        return false;\n    }\n    isStale() {\n        if (this.getObserversCount() > 0) {\n            return this.observers.some((observer)=>observer.getCurrentResult().isStale);\n        }\n        return this.state.data === void 0 || this.state.isInvalidated;\n    }\n    isStaleByTime(staleTime = 0) {\n        if (this.state.data === void 0) {\n            return true;\n        }\n        if (staleTime === \"static\") {\n            return false;\n        }\n        if (this.state.isInvalidated) {\n            return true;\n        }\n        return !(0,_utils_js__WEBPACK_IMPORTED_MODULE_1__.timeUntilStale)(this.state.dataUpdatedAt, staleTime);\n    }\n    onFocus() {\n        const observer = this.observers.find((x)=>x.shouldFetchOnWindowFocus());\n        observer?.refetch({\n            cancelRefetch: false\n        });\n        this.#retryer?.continue();\n    }\n    onOnline() {\n        const observer = this.observers.find((x)=>x.shouldFetchOnReconnect());\n        observer?.refetch({\n            cancelRefetch: false\n        });\n        this.#retryer?.continue();\n    }\n    addObserver(observer) {\n        if (!this.observers.includes(observer)) {\n            this.observers.push(observer);\n            this.clearGcTimeout();\n            this.#cache.notify({\n                type: \"observerAdded\",\n                query: this,\n                observer\n            });\n        }\n    }\n    removeObserver(observer) {\n        if (this.observers.includes(observer)) {\n            this.observers = this.observers.filter((x)=>x !== observer);\n            if (!this.observers.length) {\n                if (this.#retryer) {\n                    if (this.#abortSignalConsumed) {\n                        this.#retryer.cancel({\n                            revert: true\n                        });\n                    } else {\n                        this.#retryer.cancelRetry();\n                    }\n                }\n                this.scheduleGc();\n            }\n            this.#cache.notify({\n                type: \"observerRemoved\",\n                query: this,\n                observer\n            });\n        }\n    }\n    getObserversCount() {\n        return this.observers.length;\n    }\n    invalidate() {\n        if (!this.state.isInvalidated) {\n            this.#dispatch({\n                type: \"invalidate\"\n            });\n        }\n    }\n    fetch(options, fetchOptions) {\n        if (this.state.fetchStatus !== \"idle\") {\n            if (this.state.data !== void 0 && fetchOptions?.cancelRefetch) {\n                this.cancel({\n                    silent: true\n                });\n            } else if (this.#retryer) {\n                this.#retryer.continueRetry();\n                return this.#retryer.promise;\n            }\n        }\n        if (options) {\n            this.setOptions(options);\n        }\n        if (!this.options.queryFn) {\n            const observer = this.observers.find((x)=>x.options.queryFn);\n            if (observer) {\n                this.setOptions(observer.options);\n            }\n        }\n        if (true) {\n            if (!Array.isArray(this.options.queryKey)) {\n                console.error(`As of v4, queryKey needs to be an Array. If you are using a string like 'repoData', please change it to an Array, e.g. ['repoData']`);\n            }\n        }\n        const abortController = new AbortController();\n        const addSignalProperty = (object)=>{\n            Object.defineProperty(object, \"signal\", {\n                enumerable: true,\n                get: ()=>{\n                    this.#abortSignalConsumed = true;\n                    return abortController.signal;\n                }\n            });\n        };\n        const fetchFn = ()=>{\n            const queryFn = (0,_utils_js__WEBPACK_IMPORTED_MODULE_1__.ensureQueryFn)(this.options, fetchOptions);\n            const createQueryFnContext = ()=>{\n                const queryFnContext2 = {\n                    client: this.#client,\n                    queryKey: this.queryKey,\n                    meta: this.meta\n                };\n                addSignalProperty(queryFnContext2);\n                return queryFnContext2;\n            };\n            const queryFnContext = createQueryFnContext();\n            this.#abortSignalConsumed = false;\n            if (this.options.persister) {\n                return this.options.persister(queryFn, queryFnContext, this);\n            }\n            return queryFn(queryFnContext);\n        };\n        const createFetchContext = ()=>{\n            const context2 = {\n                fetchOptions,\n                options: this.options,\n                queryKey: this.queryKey,\n                client: this.#client,\n                state: this.state,\n                fetchFn\n            };\n            addSignalProperty(context2);\n            return context2;\n        };\n        const context = createFetchContext();\n        this.options.behavior?.onFetch(context, this);\n        this.#revertState = this.state;\n        if (this.state.fetchStatus === \"idle\" || this.state.fetchMeta !== context.fetchOptions?.meta) {\n            this.#dispatch({\n                type: \"fetch\",\n                meta: context.fetchOptions?.meta\n            });\n        }\n        const onError = (error)=>{\n            if (!((0,_retryer_js__WEBPACK_IMPORTED_MODULE_2__.isCancelledError)(error) && error.silent)) {\n                this.#dispatch({\n                    type: \"error\",\n                    error\n                });\n            }\n            if (!(0,_retryer_js__WEBPACK_IMPORTED_MODULE_2__.isCancelledError)(error)) {\n                this.#cache.config.onError?.(error, this);\n                this.#cache.config.onSettled?.(this.state.data, error, this);\n            }\n            this.scheduleGc();\n        };\n        this.#retryer = (0,_retryer_js__WEBPACK_IMPORTED_MODULE_2__.createRetryer)({\n            initialPromise: fetchOptions?.initialPromise,\n            fn: context.fetchFn,\n            abort: abortController.abort.bind(abortController),\n            onSuccess: (data)=>{\n                if (data === void 0) {\n                    if (true) {\n                        console.error(`Query data cannot be undefined. Please make sure to return a value other than undefined from your query function. Affected query key: ${this.queryHash}`);\n                    }\n                    onError(new Error(`${this.queryHash} data is undefined`));\n                    return;\n                }\n                try {\n                    this.setData(data);\n                } catch (error) {\n                    onError(error);\n                    return;\n                }\n                this.#cache.config.onSuccess?.(data, this);\n                this.#cache.config.onSettled?.(data, this.state.error, this);\n                this.scheduleGc();\n            },\n            onError,\n            onFail: (failureCount, error)=>{\n                this.#dispatch({\n                    type: \"failed\",\n                    failureCount,\n                    error\n                });\n            },\n            onPause: ()=>{\n                this.#dispatch({\n                    type: \"pause\"\n                });\n            },\n            onContinue: ()=>{\n                this.#dispatch({\n                    type: \"continue\"\n                });\n            },\n            retry: context.options.retry,\n            retryDelay: context.options.retryDelay,\n            networkMode: context.options.networkMode,\n            canRun: ()=>true\n        });\n        return this.#retryer.start();\n    }\n    #dispatch(action) {\n        const reducer = (state)=>{\n            switch(action.type){\n                case \"failed\":\n                    return {\n                        ...state,\n                        fetchFailureCount: action.failureCount,\n                        fetchFailureReason: action.error\n                    };\n                case \"pause\":\n                    return {\n                        ...state,\n                        fetchStatus: \"paused\"\n                    };\n                case \"continue\":\n                    return {\n                        ...state,\n                        fetchStatus: \"fetching\"\n                    };\n                case \"fetch\":\n                    return {\n                        ...state,\n                        ...fetchState(state.data, this.options),\n                        fetchMeta: action.meta ?? null\n                    };\n                case \"success\":\n                    return {\n                        ...state,\n                        data: action.data,\n                        dataUpdateCount: state.dataUpdateCount + 1,\n                        dataUpdatedAt: action.dataUpdatedAt ?? Date.now(),\n                        error: null,\n                        isInvalidated: false,\n                        status: \"success\",\n                        ...!action.manual && {\n                            fetchStatus: \"idle\",\n                            fetchFailureCount: 0,\n                            fetchFailureReason: null\n                        }\n                    };\n                case \"error\":\n                    const error = action.error;\n                    if ((0,_retryer_js__WEBPACK_IMPORTED_MODULE_2__.isCancelledError)(error) && error.revert && this.#revertState) {\n                        return {\n                            ...this.#revertState,\n                            fetchStatus: \"idle\"\n                        };\n                    }\n                    return {\n                        ...state,\n                        error,\n                        errorUpdateCount: state.errorUpdateCount + 1,\n                        errorUpdatedAt: Date.now(),\n                        fetchFailureCount: state.fetchFailureCount + 1,\n                        fetchFailureReason: error,\n                        fetchStatus: \"idle\",\n                        status: \"error\"\n                    };\n                case \"invalidate\":\n                    return {\n                        ...state,\n                        isInvalidated: true\n                    };\n                case \"setState\":\n                    return {\n                        ...state,\n                        ...action.state\n                    };\n            }\n        };\n        this.state = reducer(this.state);\n        _notifyManager_js__WEBPACK_IMPORTED_MODULE_3__.notifyManager.batch(()=>{\n            this.observers.forEach((observer)=>{\n                observer.onQueryUpdate();\n            });\n            this.#cache.notify({\n                query: this,\n                type: \"updated\",\n                action\n            });\n        });\n    }\n};\nfunction fetchState(data, options) {\n    return {\n        fetchFailureCount: 0,\n        fetchFailureReason: null,\n        fetchStatus: (0,_retryer_js__WEBPACK_IMPORTED_MODULE_2__.canFetch)(options.networkMode) ? \"fetching\" : \"paused\",\n        ...data === void 0 && {\n            error: null,\n            status: \"pending\"\n        }\n    };\n}\nfunction getDefaultState(options) {\n    const data = typeof options.initialData === \"function\" ? options.initialData() : options.initialData;\n    const hasData = data !== void 0;\n    const initialDataUpdatedAt = hasData ? typeof options.initialDataUpdatedAt === \"function\" ? options.initialDataUpdatedAt() : options.initialDataUpdatedAt : 0;\n    return {\n        data,\n        dataUpdateCount: 0,\n        dataUpdatedAt: hasData ? initialDataUpdatedAt ?? Date.now() : 0,\n        error: null,\n        errorUpdateCount: 0,\n        errorUpdatedAt: 0,\n        fetchFailureCount: 0,\n        fetchFailureReason: null,\n        fetchMeta: null,\n        isInvalidated: false,\n        status: hasData ? \"success\" : \"pending\",\n        fetchStatus: \"idle\"\n    };\n}\n //# sourceMappingURL=query.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/@tanstack/query-core/build/modern/query.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/@tanstack/query-core/build/modern/queryCache.js":
/*!**************************************************************************!*\
  !*** ../../node_modules/@tanstack/query-core/build/modern/queryCache.js ***!
  \**************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   QueryCache: () => (/* binding */ QueryCache)\n/* harmony export */ });\n/* harmony import */ var _utils_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./utils.js */ \"(ssr)/../../node_modules/@tanstack/query-core/build/modern/utils.js\");\n/* harmony import */ var _query_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./query.js */ \"(ssr)/../../node_modules/@tanstack/query-core/build/modern/query.js\");\n/* harmony import */ var _notifyManager_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./notifyManager.js */ \"(ssr)/../../node_modules/@tanstack/query-core/build/modern/notifyManager.js\");\n/* harmony import */ var _subscribable_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./subscribable.js */ \"(ssr)/../../node_modules/@tanstack/query-core/build/modern/subscribable.js\");\n// src/queryCache.ts\n\n\n\n\nvar QueryCache = class extends _subscribable_js__WEBPACK_IMPORTED_MODULE_0__.Subscribable {\n    constructor(config = {}){\n        super();\n        this.config = config;\n        this.#queries = /* @__PURE__ */ new Map();\n    }\n    #queries;\n    build(client, options, state) {\n        const queryKey = options.queryKey;\n        const queryHash = options.queryHash ?? (0,_utils_js__WEBPACK_IMPORTED_MODULE_1__.hashQueryKeyByOptions)(queryKey, options);\n        let query = this.get(queryHash);\n        if (!query) {\n            query = new _query_js__WEBPACK_IMPORTED_MODULE_2__.Query({\n                client,\n                queryKey,\n                queryHash,\n                options: client.defaultQueryOptions(options),\n                state,\n                defaultOptions: client.getQueryDefaults(queryKey)\n            });\n            this.add(query);\n        }\n        return query;\n    }\n    add(query) {\n        if (!this.#queries.has(query.queryHash)) {\n            this.#queries.set(query.queryHash, query);\n            this.notify({\n                type: \"added\",\n                query\n            });\n        }\n    }\n    remove(query) {\n        const queryInMap = this.#queries.get(query.queryHash);\n        if (queryInMap) {\n            query.destroy();\n            if (queryInMap === query) {\n                this.#queries.delete(query.queryHash);\n            }\n            this.notify({\n                type: \"removed\",\n                query\n            });\n        }\n    }\n    clear() {\n        _notifyManager_js__WEBPACK_IMPORTED_MODULE_3__.notifyManager.batch(()=>{\n            this.getAll().forEach((query)=>{\n                this.remove(query);\n            });\n        });\n    }\n    get(queryHash) {\n        return this.#queries.get(queryHash);\n    }\n    getAll() {\n        return [\n            ...this.#queries.values()\n        ];\n    }\n    find(filters) {\n        const defaultedFilters = {\n            exact: true,\n            ...filters\n        };\n        return this.getAll().find((query)=>(0,_utils_js__WEBPACK_IMPORTED_MODULE_1__.matchQuery)(defaultedFilters, query));\n    }\n    findAll(filters = {}) {\n        const queries = this.getAll();\n        return Object.keys(filters).length > 0 ? queries.filter((query)=>(0,_utils_js__WEBPACK_IMPORTED_MODULE_1__.matchQuery)(filters, query)) : queries;\n    }\n    notify(event) {\n        _notifyManager_js__WEBPACK_IMPORTED_MODULE_3__.notifyManager.batch(()=>{\n            this.listeners.forEach((listener)=>{\n                listener(event);\n            });\n        });\n    }\n    onFocus() {\n        _notifyManager_js__WEBPACK_IMPORTED_MODULE_3__.notifyManager.batch(()=>{\n            this.getAll().forEach((query)=>{\n                query.onFocus();\n            });\n        });\n    }\n    onOnline() {\n        _notifyManager_js__WEBPACK_IMPORTED_MODULE_3__.notifyManager.batch(()=>{\n            this.getAll().forEach((query)=>{\n                query.onOnline();\n            });\n        });\n    }\n};\n //# sourceMappingURL=queryCache.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/@tanstack/query-core/build/modern/queryCache.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/@tanstack/query-core/build/modern/queryClient.js":
/*!***************************************************************************!*\
  !*** ../../node_modules/@tanstack/query-core/build/modern/queryClient.js ***!
  \***************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   QueryClient: () => (/* binding */ QueryClient)\n/* harmony export */ });\n/* harmony import */ var _utils_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./utils.js */ \"(ssr)/../../node_modules/@tanstack/query-core/build/modern/utils.js\");\n/* harmony import */ var _queryCache_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./queryCache.js */ \"(ssr)/../../node_modules/@tanstack/query-core/build/modern/queryCache.js\");\n/* harmony import */ var _mutationCache_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./mutationCache.js */ \"(ssr)/../../node_modules/@tanstack/query-core/build/modern/mutationCache.js\");\n/* harmony import */ var _focusManager_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./focusManager.js */ \"(ssr)/../../node_modules/@tanstack/query-core/build/modern/focusManager.js\");\n/* harmony import */ var _onlineManager_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./onlineManager.js */ \"(ssr)/../../node_modules/@tanstack/query-core/build/modern/onlineManager.js\");\n/* harmony import */ var _notifyManager_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./notifyManager.js */ \"(ssr)/../../node_modules/@tanstack/query-core/build/modern/notifyManager.js\");\n/* harmony import */ var _infiniteQueryBehavior_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./infiniteQueryBehavior.js */ \"(ssr)/../../node_modules/@tanstack/query-core/build/modern/infiniteQueryBehavior.js\");\n// src/queryClient.ts\n\n\n\n\n\n\n\nvar QueryClient = class {\n    #queryCache;\n    #mutationCache;\n    #defaultOptions;\n    #queryDefaults;\n    #mutationDefaults;\n    #mountCount;\n    #unsubscribeFocus;\n    #unsubscribeOnline;\n    constructor(config = {}){\n        this.#queryCache = config.queryCache || new _queryCache_js__WEBPACK_IMPORTED_MODULE_0__.QueryCache();\n        this.#mutationCache = config.mutationCache || new _mutationCache_js__WEBPACK_IMPORTED_MODULE_1__.MutationCache();\n        this.#defaultOptions = config.defaultOptions || {};\n        this.#queryDefaults = /* @__PURE__ */ new Map();\n        this.#mutationDefaults = /* @__PURE__ */ new Map();\n        this.#mountCount = 0;\n    }\n    mount() {\n        this.#mountCount++;\n        if (this.#mountCount !== 1) return;\n        this.#unsubscribeFocus = _focusManager_js__WEBPACK_IMPORTED_MODULE_2__.focusManager.subscribe(async (focused)=>{\n            if (focused) {\n                await this.resumePausedMutations();\n                this.#queryCache.onFocus();\n            }\n        });\n        this.#unsubscribeOnline = _onlineManager_js__WEBPACK_IMPORTED_MODULE_3__.onlineManager.subscribe(async (online)=>{\n            if (online) {\n                await this.resumePausedMutations();\n                this.#queryCache.onOnline();\n            }\n        });\n    }\n    unmount() {\n        this.#mountCount--;\n        if (this.#mountCount !== 0) return;\n        this.#unsubscribeFocus?.();\n        this.#unsubscribeFocus = void 0;\n        this.#unsubscribeOnline?.();\n        this.#unsubscribeOnline = void 0;\n    }\n    isFetching(filters) {\n        return this.#queryCache.findAll({\n            ...filters,\n            fetchStatus: \"fetching\"\n        }).length;\n    }\n    isMutating(filters) {\n        return this.#mutationCache.findAll({\n            ...filters,\n            status: \"pending\"\n        }).length;\n    }\n    /**\n   * Imperative (non-reactive) way to retrieve data for a QueryKey.\n   * Should only be used in callbacks or functions where reading the latest data is necessary, e.g. for optimistic updates.\n   *\n   * Hint: Do not use this function inside a component, because it won't receive updates.\n   * Use `useQuery` to create a `QueryObserver` that subscribes to changes.\n   */ getQueryData(queryKey) {\n        const options = this.defaultQueryOptions({\n            queryKey\n        });\n        return this.#queryCache.get(options.queryHash)?.state.data;\n    }\n    ensureQueryData(options) {\n        const defaultedOptions = this.defaultQueryOptions(options);\n        const query = this.#queryCache.build(this, defaultedOptions);\n        const cachedData = query.state.data;\n        if (cachedData === void 0) {\n            return this.fetchQuery(options);\n        }\n        if (options.revalidateIfStale && query.isStaleByTime((0,_utils_js__WEBPACK_IMPORTED_MODULE_4__.resolveStaleTime)(defaultedOptions.staleTime, query))) {\n            void this.prefetchQuery(defaultedOptions);\n        }\n        return Promise.resolve(cachedData);\n    }\n    getQueriesData(filters) {\n        return this.#queryCache.findAll(filters).map(({ queryKey, state })=>{\n            const data = state.data;\n            return [\n                queryKey,\n                data\n            ];\n        });\n    }\n    setQueryData(queryKey, updater, options) {\n        const defaultedOptions = this.defaultQueryOptions({\n            queryKey\n        });\n        const query = this.#queryCache.get(defaultedOptions.queryHash);\n        const prevData = query?.state.data;\n        const data = (0,_utils_js__WEBPACK_IMPORTED_MODULE_4__.functionalUpdate)(updater, prevData);\n        if (data === void 0) {\n            return void 0;\n        }\n        return this.#queryCache.build(this, defaultedOptions).setData(data, {\n            ...options,\n            manual: true\n        });\n    }\n    setQueriesData(filters, updater, options) {\n        return _notifyManager_js__WEBPACK_IMPORTED_MODULE_5__.notifyManager.batch(()=>this.#queryCache.findAll(filters).map(({ queryKey })=>[\n                    queryKey,\n                    this.setQueryData(queryKey, updater, options)\n                ]));\n    }\n    getQueryState(queryKey) {\n        const options = this.defaultQueryOptions({\n            queryKey\n        });\n        return this.#queryCache.get(options.queryHash)?.state;\n    }\n    removeQueries(filters) {\n        const queryCache = this.#queryCache;\n        _notifyManager_js__WEBPACK_IMPORTED_MODULE_5__.notifyManager.batch(()=>{\n            queryCache.findAll(filters).forEach((query)=>{\n                queryCache.remove(query);\n            });\n        });\n    }\n    resetQueries(filters, options) {\n        const queryCache = this.#queryCache;\n        return _notifyManager_js__WEBPACK_IMPORTED_MODULE_5__.notifyManager.batch(()=>{\n            queryCache.findAll(filters).forEach((query)=>{\n                query.reset();\n            });\n            return this.refetchQueries({\n                type: \"active\",\n                ...filters\n            }, options);\n        });\n    }\n    cancelQueries(filters, cancelOptions = {}) {\n        const defaultedCancelOptions = {\n            revert: true,\n            ...cancelOptions\n        };\n        const promises = _notifyManager_js__WEBPACK_IMPORTED_MODULE_5__.notifyManager.batch(()=>this.#queryCache.findAll(filters).map((query)=>query.cancel(defaultedCancelOptions)));\n        return Promise.all(promises).then(_utils_js__WEBPACK_IMPORTED_MODULE_4__.noop).catch(_utils_js__WEBPACK_IMPORTED_MODULE_4__.noop);\n    }\n    invalidateQueries(filters, options = {}) {\n        return _notifyManager_js__WEBPACK_IMPORTED_MODULE_5__.notifyManager.batch(()=>{\n            this.#queryCache.findAll(filters).forEach((query)=>{\n                query.invalidate();\n            });\n            if (filters?.refetchType === \"none\") {\n                return Promise.resolve();\n            }\n            return this.refetchQueries({\n                ...filters,\n                type: filters?.refetchType ?? filters?.type ?? \"active\"\n            }, options);\n        });\n    }\n    refetchQueries(filters, options = {}) {\n        const fetchOptions = {\n            ...options,\n            cancelRefetch: options.cancelRefetch ?? true\n        };\n        const promises = _notifyManager_js__WEBPACK_IMPORTED_MODULE_5__.notifyManager.batch(()=>this.#queryCache.findAll(filters).filter((query)=>!query.isDisabled() && !query.isStatic()).map((query)=>{\n                let promise = query.fetch(void 0, fetchOptions);\n                if (!fetchOptions.throwOnError) {\n                    promise = promise.catch(_utils_js__WEBPACK_IMPORTED_MODULE_4__.noop);\n                }\n                return query.state.fetchStatus === \"paused\" ? Promise.resolve() : promise;\n            }));\n        return Promise.all(promises).then(_utils_js__WEBPACK_IMPORTED_MODULE_4__.noop);\n    }\n    fetchQuery(options) {\n        const defaultedOptions = this.defaultQueryOptions(options);\n        if (defaultedOptions.retry === void 0) {\n            defaultedOptions.retry = false;\n        }\n        const query = this.#queryCache.build(this, defaultedOptions);\n        return query.isStaleByTime((0,_utils_js__WEBPACK_IMPORTED_MODULE_4__.resolveStaleTime)(defaultedOptions.staleTime, query)) ? query.fetch(defaultedOptions) : Promise.resolve(query.state.data);\n    }\n    prefetchQuery(options) {\n        return this.fetchQuery(options).then(_utils_js__WEBPACK_IMPORTED_MODULE_4__.noop).catch(_utils_js__WEBPACK_IMPORTED_MODULE_4__.noop);\n    }\n    fetchInfiniteQuery(options) {\n        options.behavior = (0,_infiniteQueryBehavior_js__WEBPACK_IMPORTED_MODULE_6__.infiniteQueryBehavior)(options.pages);\n        return this.fetchQuery(options);\n    }\n    prefetchInfiniteQuery(options) {\n        return this.fetchInfiniteQuery(options).then(_utils_js__WEBPACK_IMPORTED_MODULE_4__.noop).catch(_utils_js__WEBPACK_IMPORTED_MODULE_4__.noop);\n    }\n    ensureInfiniteQueryData(options) {\n        options.behavior = (0,_infiniteQueryBehavior_js__WEBPACK_IMPORTED_MODULE_6__.infiniteQueryBehavior)(options.pages);\n        return this.ensureQueryData(options);\n    }\n    resumePausedMutations() {\n        if (_onlineManager_js__WEBPACK_IMPORTED_MODULE_3__.onlineManager.isOnline()) {\n            return this.#mutationCache.resumePausedMutations();\n        }\n        return Promise.resolve();\n    }\n    getQueryCache() {\n        return this.#queryCache;\n    }\n    getMutationCache() {\n        return this.#mutationCache;\n    }\n    getDefaultOptions() {\n        return this.#defaultOptions;\n    }\n    setDefaultOptions(options) {\n        this.#defaultOptions = options;\n    }\n    setQueryDefaults(queryKey, options) {\n        this.#queryDefaults.set((0,_utils_js__WEBPACK_IMPORTED_MODULE_4__.hashKey)(queryKey), {\n            queryKey,\n            defaultOptions: options\n        });\n    }\n    getQueryDefaults(queryKey) {\n        const defaults = [\n            ...this.#queryDefaults.values()\n        ];\n        const result = {};\n        defaults.forEach((queryDefault)=>{\n            if ((0,_utils_js__WEBPACK_IMPORTED_MODULE_4__.partialMatchKey)(queryKey, queryDefault.queryKey)) {\n                Object.assign(result, queryDefault.defaultOptions);\n            }\n        });\n        return result;\n    }\n    setMutationDefaults(mutationKey, options) {\n        this.#mutationDefaults.set((0,_utils_js__WEBPACK_IMPORTED_MODULE_4__.hashKey)(mutationKey), {\n            mutationKey,\n            defaultOptions: options\n        });\n    }\n    getMutationDefaults(mutationKey) {\n        const defaults = [\n            ...this.#mutationDefaults.values()\n        ];\n        const result = {};\n        defaults.forEach((queryDefault)=>{\n            if ((0,_utils_js__WEBPACK_IMPORTED_MODULE_4__.partialMatchKey)(mutationKey, queryDefault.mutationKey)) {\n                Object.assign(result, queryDefault.defaultOptions);\n            }\n        });\n        return result;\n    }\n    defaultQueryOptions(options) {\n        if (options._defaulted) {\n            return options;\n        }\n        const defaultedOptions = {\n            ...this.#defaultOptions.queries,\n            ...this.getQueryDefaults(options.queryKey),\n            ...options,\n            _defaulted: true\n        };\n        if (!defaultedOptions.queryHash) {\n            defaultedOptions.queryHash = (0,_utils_js__WEBPACK_IMPORTED_MODULE_4__.hashQueryKeyByOptions)(defaultedOptions.queryKey, defaultedOptions);\n        }\n        if (defaultedOptions.refetchOnReconnect === void 0) {\n            defaultedOptions.refetchOnReconnect = defaultedOptions.networkMode !== \"always\";\n        }\n        if (defaultedOptions.throwOnError === void 0) {\n            defaultedOptions.throwOnError = !!defaultedOptions.suspense;\n        }\n        if (!defaultedOptions.networkMode && defaultedOptions.persister) {\n            defaultedOptions.networkMode = \"offlineFirst\";\n        }\n        if (defaultedOptions.queryFn === _utils_js__WEBPACK_IMPORTED_MODULE_4__.skipToken) {\n            defaultedOptions.enabled = false;\n        }\n        return defaultedOptions;\n    }\n    defaultMutationOptions(options) {\n        if (options?._defaulted) {\n            return options;\n        }\n        return {\n            ...this.#defaultOptions.mutations,\n            ...options?.mutationKey && this.getMutationDefaults(options.mutationKey),\n            ...options,\n            _defaulted: true\n        };\n    }\n    clear() {\n        this.#queryCache.clear();\n        this.#mutationCache.clear();\n    }\n};\n //# sourceMappingURL=queryClient.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/@tanstack/query-core/build/modern/queryClient.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/@tanstack/query-core/build/modern/removable.js":
/*!*************************************************************************!*\
  !*** ../../node_modules/@tanstack/query-core/build/modern/removable.js ***!
  \*************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Removable: () => (/* binding */ Removable)\n/* harmony export */ });\n/* harmony import */ var _utils_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./utils.js */ \"(ssr)/../../node_modules/@tanstack/query-core/build/modern/utils.js\");\n// src/removable.ts\n\nvar Removable = class {\n    #gcTimeout;\n    destroy() {\n        this.clearGcTimeout();\n    }\n    scheduleGc() {\n        this.clearGcTimeout();\n        if ((0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.isValidTimeout)(this.gcTime)) {\n            this.#gcTimeout = setTimeout(()=>{\n                this.optionalRemove();\n            }, this.gcTime);\n        }\n    }\n    updateGcTime(newGcTime) {\n        this.gcTime = Math.max(this.gcTime || 0, newGcTime ?? (_utils_js__WEBPACK_IMPORTED_MODULE_0__.isServer ? Infinity : 5 * 60 * 1e3));\n    }\n    clearGcTimeout() {\n        if (this.#gcTimeout) {\n            clearTimeout(this.#gcTimeout);\n            this.#gcTimeout = void 0;\n        }\n    }\n};\n //# sourceMappingURL=removable.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/@tanstack/query-core/build/modern/removable.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/@tanstack/query-core/build/modern/retryer.js":
/*!***********************************************************************!*\
  !*** ../../node_modules/@tanstack/query-core/build/modern/retryer.js ***!
  \***********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CancelledError: () => (/* binding */ CancelledError),\n/* harmony export */   canFetch: () => (/* binding */ canFetch),\n/* harmony export */   createRetryer: () => (/* binding */ createRetryer),\n/* harmony export */   isCancelledError: () => (/* binding */ isCancelledError)\n/* harmony export */ });\n/* harmony import */ var _focusManager_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./focusManager.js */ \"(ssr)/../../node_modules/@tanstack/query-core/build/modern/focusManager.js\");\n/* harmony import */ var _onlineManager_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./onlineManager.js */ \"(ssr)/../../node_modules/@tanstack/query-core/build/modern/onlineManager.js\");\n/* harmony import */ var _thenable_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./thenable.js */ \"(ssr)/../../node_modules/@tanstack/query-core/build/modern/thenable.js\");\n/* harmony import */ var _utils_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./utils.js */ \"(ssr)/../../node_modules/@tanstack/query-core/build/modern/utils.js\");\n// src/retryer.ts\n\n\n\n\nfunction defaultRetryDelay(failureCount) {\n    return Math.min(1e3 * 2 ** failureCount, 3e4);\n}\nfunction canFetch(networkMode) {\n    return (networkMode ?? \"online\") === \"online\" ? _onlineManager_js__WEBPACK_IMPORTED_MODULE_0__.onlineManager.isOnline() : true;\n}\nvar CancelledError = class extends Error {\n    constructor(options){\n        super(\"CancelledError\");\n        this.revert = options?.revert;\n        this.silent = options?.silent;\n    }\n};\nfunction isCancelledError(value) {\n    return value instanceof CancelledError;\n}\nfunction createRetryer(config) {\n    let isRetryCancelled = false;\n    let failureCount = 0;\n    let isResolved = false;\n    let continueFn;\n    const thenable = (0,_thenable_js__WEBPACK_IMPORTED_MODULE_1__.pendingThenable)();\n    const cancel = (cancelOptions)=>{\n        if (!isResolved) {\n            reject(new CancelledError(cancelOptions));\n            config.abort?.();\n        }\n    };\n    const cancelRetry = ()=>{\n        isRetryCancelled = true;\n    };\n    const continueRetry = ()=>{\n        isRetryCancelled = false;\n    };\n    const canContinue = ()=>_focusManager_js__WEBPACK_IMPORTED_MODULE_2__.focusManager.isFocused() && (config.networkMode === \"always\" || _onlineManager_js__WEBPACK_IMPORTED_MODULE_0__.onlineManager.isOnline()) && config.canRun();\n    const canStart = ()=>canFetch(config.networkMode) && config.canRun();\n    const resolve = (value)=>{\n        if (!isResolved) {\n            isResolved = true;\n            config.onSuccess?.(value);\n            continueFn?.();\n            thenable.resolve(value);\n        }\n    };\n    const reject = (value)=>{\n        if (!isResolved) {\n            isResolved = true;\n            config.onError?.(value);\n            continueFn?.();\n            thenable.reject(value);\n        }\n    };\n    const pause = ()=>{\n        return new Promise((continueResolve)=>{\n            continueFn = (value)=>{\n                if (isResolved || canContinue()) {\n                    continueResolve(value);\n                }\n            };\n            config.onPause?.();\n        }).then(()=>{\n            continueFn = void 0;\n            if (!isResolved) {\n                config.onContinue?.();\n            }\n        });\n    };\n    const run = ()=>{\n        if (isResolved) {\n            return;\n        }\n        let promiseOrValue;\n        const initialPromise = failureCount === 0 ? config.initialPromise : void 0;\n        try {\n            promiseOrValue = initialPromise ?? config.fn();\n        } catch (error) {\n            promiseOrValue = Promise.reject(error);\n        }\n        Promise.resolve(promiseOrValue).then(resolve).catch((error)=>{\n            if (isResolved) {\n                return;\n            }\n            const retry = config.retry ?? (_utils_js__WEBPACK_IMPORTED_MODULE_3__.isServer ? 0 : 3);\n            const retryDelay = config.retryDelay ?? defaultRetryDelay;\n            const delay = typeof retryDelay === \"function\" ? retryDelay(failureCount, error) : retryDelay;\n            const shouldRetry = retry === true || typeof retry === \"number\" && failureCount < retry || typeof retry === \"function\" && retry(failureCount, error);\n            if (isRetryCancelled || !shouldRetry) {\n                reject(error);\n                return;\n            }\n            failureCount++;\n            config.onFail?.(failureCount, error);\n            (0,_utils_js__WEBPACK_IMPORTED_MODULE_3__.sleep)(delay).then(()=>{\n                return canContinue() ? void 0 : pause();\n            }).then(()=>{\n                if (isRetryCancelled) {\n                    reject(error);\n                } else {\n                    run();\n                }\n            });\n        });\n    };\n    return {\n        promise: thenable,\n        cancel,\n        continue: ()=>{\n            continueFn?.();\n            return thenable;\n        },\n        cancelRetry,\n        continueRetry,\n        canStart,\n        start: ()=>{\n            if (canStart()) {\n                run();\n            } else {\n                pause().then(run);\n            }\n            return thenable;\n        }\n    };\n}\n //# sourceMappingURL=retryer.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/@tanstack/query-core/build/modern/retryer.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/@tanstack/query-core/build/modern/subscribable.js":
/*!****************************************************************************!*\
  !*** ../../node_modules/@tanstack/query-core/build/modern/subscribable.js ***!
  \****************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Subscribable: () => (/* binding */ Subscribable)\n/* harmony export */ });\n// src/subscribable.ts\nvar Subscribable = class {\n    constructor(){\n        this.listeners = /* @__PURE__ */ new Set();\n        this.subscribe = this.subscribe.bind(this);\n    }\n    subscribe(listener) {\n        this.listeners.add(listener);\n        this.onSubscribe();\n        return ()=>{\n            this.listeners.delete(listener);\n            this.onUnsubscribe();\n        };\n    }\n    hasListeners() {\n        return this.listeners.size > 0;\n    }\n    onSubscribe() {}\n    onUnsubscribe() {}\n};\n //# sourceMappingURL=subscribable.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzL0B0YW5zdGFjay9xdWVyeS1jb3JlL2J1aWxkL21vZGVybi9zdWJzY3JpYmFibGUuanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLHNCQUFzQjtBQUN0QixJQUFJQSxlQUFlO0lBQ2pCQyxhQUFjO1FBQ1osSUFBSSxDQUFDQyxTQUFTLEdBQUcsYUFBYSxHQUFHLElBQUlDO1FBQ3JDLElBQUksQ0FBQ0MsU0FBUyxHQUFHLElBQUksQ0FBQ0EsU0FBUyxDQUFDQyxJQUFJLENBQUMsSUFBSTtJQUMzQztJQUNBRCxVQUFVRSxRQUFRLEVBQUU7UUFDbEIsSUFBSSxDQUFDSixTQUFTLENBQUNLLEdBQUcsQ0FBQ0Q7UUFDbkIsSUFBSSxDQUFDRSxXQUFXO1FBQ2hCLE9BQU87WUFDTCxJQUFJLENBQUNOLFNBQVMsQ0FBQ08sTUFBTSxDQUFDSDtZQUN0QixJQUFJLENBQUNJLGFBQWE7UUFDcEI7SUFDRjtJQUNBQyxlQUFlO1FBQ2IsT0FBTyxJQUFJLENBQUNULFNBQVMsQ0FBQ1UsSUFBSSxHQUFHO0lBQy9CO0lBQ0FKLGNBQWMsQ0FDZDtJQUNBRSxnQkFBZ0IsQ0FDaEI7QUFDRjtBQUdFLENBQ0Ysd0NBQXdDIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vQGZyZWVsYS9hZG1pbi1kYXNoYm9hcmQvLi4vLi4vbm9kZV9tb2R1bGVzL0B0YW5zdGFjay9xdWVyeS1jb3JlL2J1aWxkL21vZGVybi9zdWJzY3JpYmFibGUuanM/ZGIzYSJdLCJzb3VyY2VzQ29udGVudCI6WyIvLyBzcmMvc3Vic2NyaWJhYmxlLnRzXG52YXIgU3Vic2NyaWJhYmxlID0gY2xhc3Mge1xuICBjb25zdHJ1Y3RvcigpIHtcbiAgICB0aGlzLmxpc3RlbmVycyA9IC8qIEBfX1BVUkVfXyAqLyBuZXcgU2V0KCk7XG4gICAgdGhpcy5zdWJzY3JpYmUgPSB0aGlzLnN1YnNjcmliZS5iaW5kKHRoaXMpO1xuICB9XG4gIHN1YnNjcmliZShsaXN0ZW5lcikge1xuICAgIHRoaXMubGlzdGVuZXJzLmFkZChsaXN0ZW5lcik7XG4gICAgdGhpcy5vblN1YnNjcmliZSgpO1xuICAgIHJldHVybiAoKSA9PiB7XG4gICAgICB0aGlzLmxpc3RlbmVycy5kZWxldGUobGlzdGVuZXIpO1xuICAgICAgdGhpcy5vblVuc3Vic2NyaWJlKCk7XG4gICAgfTtcbiAgfVxuICBoYXNMaXN0ZW5lcnMoKSB7XG4gICAgcmV0dXJuIHRoaXMubGlzdGVuZXJzLnNpemUgPiAwO1xuICB9XG4gIG9uU3Vic2NyaWJlKCkge1xuICB9XG4gIG9uVW5zdWJzY3JpYmUoKSB7XG4gIH1cbn07XG5leHBvcnQge1xuICBTdWJzY3JpYmFibGVcbn07XG4vLyMgc291cmNlTWFwcGluZ1VSTD1zdWJzY3JpYmFibGUuanMubWFwIl0sIm5hbWVzIjpbIlN1YnNjcmliYWJsZSIsImNvbnN0cnVjdG9yIiwibGlzdGVuZXJzIiwiU2V0Iiwic3Vic2NyaWJlIiwiYmluZCIsImxpc3RlbmVyIiwiYWRkIiwib25TdWJzY3JpYmUiLCJkZWxldGUiLCJvblVuc3Vic2NyaWJlIiwiaGFzTGlzdGVuZXJzIiwic2l6ZSJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/@tanstack/query-core/build/modern/subscribable.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/@tanstack/query-core/build/modern/thenable.js":
/*!************************************************************************!*\
  !*** ../../node_modules/@tanstack/query-core/build/modern/thenable.js ***!
  \************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   pendingThenable: () => (/* binding */ pendingThenable),\n/* harmony export */   tryResolveSync: () => (/* binding */ tryResolveSync)\n/* harmony export */ });\n/* harmony import */ var _utils_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./utils.js */ \"(ssr)/../../node_modules/@tanstack/query-core/build/modern/utils.js\");\n// src/thenable.ts\n\nfunction pendingThenable() {\n    let resolve;\n    let reject;\n    const thenable = new Promise((_resolve, _reject)=>{\n        resolve = _resolve;\n        reject = _reject;\n    });\n    thenable.status = \"pending\";\n    thenable.catch(()=>{});\n    function finalize(data) {\n        Object.assign(thenable, data);\n        delete thenable.resolve;\n        delete thenable.reject;\n    }\n    thenable.resolve = (value)=>{\n        finalize({\n            status: \"fulfilled\",\n            value\n        });\n        resolve(value);\n    };\n    thenable.reject = (reason)=>{\n        finalize({\n            status: \"rejected\",\n            reason\n        });\n        reject(reason);\n    };\n    return thenable;\n}\nfunction tryResolveSync(promise) {\n    let data;\n    promise.then((result)=>{\n        data = result;\n        return result;\n    }, _utils_js__WEBPACK_IMPORTED_MODULE_0__.noop)?.catch(_utils_js__WEBPACK_IMPORTED_MODULE_0__.noop);\n    if (data !== void 0) {\n        return {\n            data\n        };\n    }\n    return void 0;\n}\n //# sourceMappingURL=thenable.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/@tanstack/query-core/build/modern/thenable.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/@tanstack/query-core/build/modern/utils.js":
/*!*********************************************************************!*\
  !*** ../../node_modules/@tanstack/query-core/build/modern/utils.js ***!
  \*********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   addToEnd: () => (/* binding */ addToEnd),\n/* harmony export */   addToStart: () => (/* binding */ addToStart),\n/* harmony export */   ensureQueryFn: () => (/* binding */ ensureQueryFn),\n/* harmony export */   functionalUpdate: () => (/* binding */ functionalUpdate),\n/* harmony export */   hashKey: () => (/* binding */ hashKey),\n/* harmony export */   hashQueryKeyByOptions: () => (/* binding */ hashQueryKeyByOptions),\n/* harmony export */   isPlainArray: () => (/* binding */ isPlainArray),\n/* harmony export */   isPlainObject: () => (/* binding */ isPlainObject),\n/* harmony export */   isServer: () => (/* binding */ isServer),\n/* harmony export */   isValidTimeout: () => (/* binding */ isValidTimeout),\n/* harmony export */   keepPreviousData: () => (/* binding */ keepPreviousData),\n/* harmony export */   matchMutation: () => (/* binding */ matchMutation),\n/* harmony export */   matchQuery: () => (/* binding */ matchQuery),\n/* harmony export */   noop: () => (/* binding */ noop),\n/* harmony export */   partialMatchKey: () => (/* binding */ partialMatchKey),\n/* harmony export */   replaceData: () => (/* binding */ replaceData),\n/* harmony export */   replaceEqualDeep: () => (/* binding */ replaceEqualDeep),\n/* harmony export */   resolveEnabled: () => (/* binding */ resolveEnabled),\n/* harmony export */   resolveStaleTime: () => (/* binding */ resolveStaleTime),\n/* harmony export */   shallowEqualObjects: () => (/* binding */ shallowEqualObjects),\n/* harmony export */   shouldThrowError: () => (/* binding */ shouldThrowError),\n/* harmony export */   skipToken: () => (/* binding */ skipToken),\n/* harmony export */   sleep: () => (/* binding */ sleep),\n/* harmony export */   timeUntilStale: () => (/* binding */ timeUntilStale)\n/* harmony export */ });\n// src/utils.ts\nvar isServer =  true || 0;\nfunction noop() {}\nfunction functionalUpdate(updater, input) {\n    return typeof updater === \"function\" ? updater(input) : updater;\n}\nfunction isValidTimeout(value) {\n    return typeof value === \"number\" && value >= 0 && value !== Infinity;\n}\nfunction timeUntilStale(updatedAt, staleTime) {\n    return Math.max(updatedAt + (staleTime || 0) - Date.now(), 0);\n}\nfunction resolveStaleTime(staleTime, query) {\n    return typeof staleTime === \"function\" ? staleTime(query) : staleTime;\n}\nfunction resolveEnabled(enabled, query) {\n    return typeof enabled === \"function\" ? enabled(query) : enabled;\n}\nfunction matchQuery(filters, query) {\n    const { type = \"all\", exact, fetchStatus, predicate, queryKey, stale } = filters;\n    if (queryKey) {\n        if (exact) {\n            if (query.queryHash !== hashQueryKeyByOptions(queryKey, query.options)) {\n                return false;\n            }\n        } else if (!partialMatchKey(query.queryKey, queryKey)) {\n            return false;\n        }\n    }\n    if (type !== \"all\") {\n        const isActive = query.isActive();\n        if (type === \"active\" && !isActive) {\n            return false;\n        }\n        if (type === \"inactive\" && isActive) {\n            return false;\n        }\n    }\n    if (typeof stale === \"boolean\" && query.isStale() !== stale) {\n        return false;\n    }\n    if (fetchStatus && fetchStatus !== query.state.fetchStatus) {\n        return false;\n    }\n    if (predicate && !predicate(query)) {\n        return false;\n    }\n    return true;\n}\nfunction matchMutation(filters, mutation) {\n    const { exact, status, predicate, mutationKey } = filters;\n    if (mutationKey) {\n        if (!mutation.options.mutationKey) {\n            return false;\n        }\n        if (exact) {\n            if (hashKey(mutation.options.mutationKey) !== hashKey(mutationKey)) {\n                return false;\n            }\n        } else if (!partialMatchKey(mutation.options.mutationKey, mutationKey)) {\n            return false;\n        }\n    }\n    if (status && mutation.state.status !== status) {\n        return false;\n    }\n    if (predicate && !predicate(mutation)) {\n        return false;\n    }\n    return true;\n}\nfunction hashQueryKeyByOptions(queryKey, options) {\n    const hashFn = options?.queryKeyHashFn || hashKey;\n    return hashFn(queryKey);\n}\nfunction hashKey(queryKey) {\n    return JSON.stringify(queryKey, (_, val)=>isPlainObject(val) ? Object.keys(val).sort().reduce((result, key)=>{\n            result[key] = val[key];\n            return result;\n        }, {}) : val);\n}\nfunction partialMatchKey(a, b) {\n    if (a === b) {\n        return true;\n    }\n    if (typeof a !== typeof b) {\n        return false;\n    }\n    if (a && b && typeof a === \"object\" && typeof b === \"object\") {\n        return Object.keys(b).every((key)=>partialMatchKey(a[key], b[key]));\n    }\n    return false;\n}\nfunction replaceEqualDeep(a, b) {\n    if (a === b) {\n        return a;\n    }\n    const array = isPlainArray(a) && isPlainArray(b);\n    if (array || isPlainObject(a) && isPlainObject(b)) {\n        const aItems = array ? a : Object.keys(a);\n        const aSize = aItems.length;\n        const bItems = array ? b : Object.keys(b);\n        const bSize = bItems.length;\n        const copy = array ? [] : {};\n        const aItemsSet = new Set(aItems);\n        let equalItems = 0;\n        for(let i = 0; i < bSize; i++){\n            const key = array ? i : bItems[i];\n            if ((!array && aItemsSet.has(key) || array) && a[key] === void 0 && b[key] === void 0) {\n                copy[key] = void 0;\n                equalItems++;\n            } else {\n                copy[key] = replaceEqualDeep(a[key], b[key]);\n                if (copy[key] === a[key] && a[key] !== void 0) {\n                    equalItems++;\n                }\n            }\n        }\n        return aSize === bSize && equalItems === aSize ? a : copy;\n    }\n    return b;\n}\nfunction shallowEqualObjects(a, b) {\n    if (!b || Object.keys(a).length !== Object.keys(b).length) {\n        return false;\n    }\n    for(const key in a){\n        if (a[key] !== b[key]) {\n            return false;\n        }\n    }\n    return true;\n}\nfunction isPlainArray(value) {\n    return Array.isArray(value) && value.length === Object.keys(value).length;\n}\nfunction isPlainObject(o) {\n    if (!hasObjectPrototype(o)) {\n        return false;\n    }\n    const ctor = o.constructor;\n    if (ctor === void 0) {\n        return true;\n    }\n    const prot = ctor.prototype;\n    if (!hasObjectPrototype(prot)) {\n        return false;\n    }\n    if (!prot.hasOwnProperty(\"isPrototypeOf\")) {\n        return false;\n    }\n    if (Object.getPrototypeOf(o) !== Object.prototype) {\n        return false;\n    }\n    return true;\n}\nfunction hasObjectPrototype(o) {\n    return Object.prototype.toString.call(o) === \"[object Object]\";\n}\nfunction sleep(timeout) {\n    return new Promise((resolve)=>{\n        setTimeout(resolve, timeout);\n    });\n}\nfunction replaceData(prevData, data, options) {\n    if (typeof options.structuralSharing === \"function\") {\n        return options.structuralSharing(prevData, data);\n    } else if (options.structuralSharing !== false) {\n        if (true) {\n            try {\n                return replaceEqualDeep(prevData, data);\n            } catch (error) {\n                console.error(`Structural sharing requires data to be JSON serializable. To fix this, turn off structuralSharing or return JSON-serializable data from your queryFn. [${options.queryHash}]: ${error}`);\n                throw error;\n            }\n        }\n        return replaceEqualDeep(prevData, data);\n    }\n    return data;\n}\nfunction keepPreviousData(previousData) {\n    return previousData;\n}\nfunction addToEnd(items, item, max = 0) {\n    const newItems = [\n        ...items,\n        item\n    ];\n    return max && newItems.length > max ? newItems.slice(1) : newItems;\n}\nfunction addToStart(items, item, max = 0) {\n    const newItems = [\n        item,\n        ...items\n    ];\n    return max && newItems.length > max ? newItems.slice(0, -1) : newItems;\n}\nvar skipToken = Symbol();\nfunction ensureQueryFn(options, fetchOptions) {\n    if (true) {\n        if (options.queryFn === skipToken) {\n            console.error(`Attempted to invoke queryFn when set to skipToken. This is likely a configuration error. Query hash: '${options.queryHash}'`);\n        }\n    }\n    if (!options.queryFn && fetchOptions?.initialPromise) {\n        return ()=>fetchOptions.initialPromise;\n    }\n    if (!options.queryFn || options.queryFn === skipToken) {\n        return ()=>Promise.reject(new Error(`Missing queryFn: '${options.queryHash}'`));\n    }\n    return options.queryFn;\n}\nfunction shouldThrowError(throwOnError, params) {\n    if (typeof throwOnError === \"function\") {\n        return throwOnError(...params);\n    }\n    return !!throwOnError;\n}\n //# sourceMappingURL=utils.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/@tanstack/query-core/build/modern/utils.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/@tanstack/react-query/build/modern/QueryClientProvider.js":
/*!************************************************************************************!*\
  !*** ../../node_modules/@tanstack/react-query/build/modern/QueryClientProvider.js ***!
  \************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   QueryClientContext: () => (/* binding */ QueryClientContext),\n/* harmony export */   QueryClientProvider: () => (/* binding */ QueryClientProvider),\n/* harmony export */   useQueryClient: () => (/* binding */ useQueryClient)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n/* __next_internal_client_entry_do_not_use__ QueryClientContext,QueryClientProvider,useQueryClient auto */ // src/QueryClientProvider.tsx\n\n\nvar QueryClientContext = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createContext(void 0);\nvar useQueryClient = (queryClient)=>{\n    const client = react__WEBPACK_IMPORTED_MODULE_0__.useContext(QueryClientContext);\n    if (queryClient) {\n        return queryClient;\n    }\n    if (!client) {\n        throw new Error(\"No QueryClient set, use QueryClientProvider to set one\");\n    }\n    return client;\n};\nvar QueryClientProvider = ({ client, children })=>{\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        client.mount();\n        return ()=>{\n            client.unmount();\n        };\n    }, [\n        client\n    ]);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(QueryClientContext.Provider, {\n        value: client,\n        children\n    });\n};\n //# sourceMappingURL=QueryClientProvider.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/@tanstack/react-query/build/modern/QueryClientProvider.js\n");

/***/ })

};
;