(()=>{var e={};e.id=702,e.ids=[702],e.modules={5403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},4749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},5528:e=>{"use strict";e.exports=require("next/dist\\client\\components\\action-async-storage.external.js")},1877:e=>{"use strict";e.exports=require("next/dist\\client\\components\\request-async-storage.external.js")},5319:e=>{"use strict";e.exports=require("next/dist\\client\\components\\static-generation-async-storage.external.js")},2756:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>a.a,__next_app__:()=>p,originalPathname:()=>f,pages:()=>s,routeModule:()=>d,tree:()=>l});var n=r(7096),o=r(6132),i=r(7284),a=r.n(i),u=r(2564),c={};for(let e in u)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(c[e]=()=>u[e]);r.d(t,c);let l=["",{children:["dashboard",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,9228)),"C:\\Users\\<USER>\\Documents\\Freela\\apps\\admin-dashboard\\src\\app\\dashboard\\page.tsx"]}]},{layout:[()=>Promise.resolve().then(r.bind(r,8182)),"C:\\Users\\<USER>\\Documents\\Freela\\apps\\admin-dashboard\\src\\app\\dashboard\\layout.tsx"]}]},{layout:[()=>Promise.resolve().then(r.bind(r,2594)),"C:\\Users\\<USER>\\Documents\\Freela\\apps\\admin-dashboard\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,9291,23)),"next/dist/client/components/not-found-error"]}],s=["C:\\Users\\<USER>\\Documents\\Freela\\apps\\admin-dashboard\\src\\app\\dashboard\\page.tsx"],f="/dashboard/page",p={require:r,loadChunk:()=>Promise.resolve()},d=new n.AppPageRouteModule({definition:{kind:o.x.APP_PAGE,page:"/dashboard/page",pathname:"/dashboard",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:l}})},3526:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,3579,23)),Promise.resolve().then(r.t.bind(r,619,23)),Promise.resolve().then(r.t.bind(r,1459,23)),Promise.resolve().then(r.t.bind(r,3456,23)),Promise.resolve().then(r.t.bind(r,847,23)),Promise.resolve().then(r.t.bind(r,7303,23))},2757:(e,t,r)=>{Promise.resolve().then(r.bind(r,5068))},4730:(e,t,r)=>{Promise.resolve().then(r.bind(r,8318))},9666:(e,t,r)=>{Promise.resolve().then(r.bind(r,4779))},5922:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"addLocale",{enumerable:!0,get:function(){return n}}),r(2038);let n=function(e){for(var t=arguments.length,r=Array(t>1?t-1:0),n=1;n<t;n++)r[n-1]=arguments[n];return e};("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},8475:(e,t,r)=>{"use strict";function n(e,t,r,n){return!1}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"getDomainLocale",{enumerable:!0,get:function(){return n}}),r(2038),("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},7490:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"default",{enumerable:!0,get:function(){return g}});let n=r(8151),o=n._(r(4218)),i=r(9377),a=r(9677),u=r(7860),c=r(703),l=r(5922),s=r(3102),f=r(9236),p=r(3582),d=r(8475),h=r(216),y=r(2247);function v(e){return"string"==typeof e?e:(0,u.formatUrl)(e)}let m=o.default.forwardRef(function(e,t){let r,n;let{href:u,as:m,children:g,prefetch:b=null,passHref:x,replace:w,shallow:O,scroll:j,locale:S,onClick:P,onMouseEnter:E,onTouchStart:k,legacyBehavior:A=!1,...M}=e;r=g,A&&("string"==typeof r||"number"==typeof r)&&(r=o.default.createElement("a",null,r));let T=o.default.useContext(s.RouterContext),_=o.default.useContext(f.AppRouterContext),C=null!=T?T:_,N=!T,D=!1!==b,I=null===b?y.PrefetchKind.AUTO:y.PrefetchKind.FULL,{href:R,as:L}=o.default.useMemo(()=>{if(!T){let e=v(u);return{href:e,as:m?v(m):e}}let[e,t]=(0,i.resolveHref)(T,u,!0);return{href:e,as:m?(0,i.resolveHref)(T,m):t||e}},[T,u,m]),B=o.default.useRef(R),F=o.default.useRef(L);A&&(n=o.default.Children.only(r));let U=A?n&&"object"==typeof n&&n.ref:t,[z,$,W]=(0,p.useIntersection)({rootMargin:"200px"}),q=o.default.useCallback(e=>{(F.current!==L||B.current!==R)&&(W(),F.current=L,B.current=R),z(e),U&&("function"==typeof U?U(e):"object"==typeof U&&(U.current=e))},[L,U,R,W,z]);o.default.useEffect(()=>{},[L,R,$,S,D,null==T?void 0:T.locale,C,N,I]);let H={ref:q,onClick(e){A||"function"!=typeof P||P(e),A&&n.props&&"function"==typeof n.props.onClick&&n.props.onClick(e),C&&!e.defaultPrevented&&function(e,t,r,n,i,u,c,l,s){let{nodeName:f}=e.currentTarget,p="A"===f.toUpperCase();if(p&&(function(e){let t=e.currentTarget,r=t.getAttribute("target");return r&&"_self"!==r||e.metaKey||e.ctrlKey||e.shiftKey||e.altKey||e.nativeEvent&&2===e.nativeEvent.which}(e)||!s&&!(0,a.isLocalURL)(r)))return;e.preventDefault();let d=()=>{let e=null==c||c;"beforePopState"in t?t[i?"replace":"push"](r,n,{shallow:u,locale:l,scroll:e}):t[i?"replace":"push"](n||r,{scroll:e})};s?o.default.startTransition(d):d()}(e,C,R,L,w,O,j,S,N)},onMouseEnter(e){A||"function"!=typeof E||E(e),A&&n.props&&"function"==typeof n.props.onMouseEnter&&n.props.onMouseEnter(e)},onTouchStart(e){A||"function"!=typeof k||k(e),A&&n.props&&"function"==typeof n.props.onTouchStart&&n.props.onTouchStart(e)}};if((0,c.isAbsoluteUrl)(L))H.href=L;else if(!A||x||"a"===n.type&&!("href"in n.props)){let e=void 0!==S?S:null==T?void 0:T.locale,t=(null==T?void 0:T.isLocaleDomain)&&(0,d.getDomainLocale)(L,e,null==T?void 0:T.locales,null==T?void 0:T.domainLocales);H.href=t||(0,h.addBasePath)((0,l.addLocale)(L,e,null==T?void 0:T.defaultLocale))}return A?o.default.cloneElement(n,H):o.default.createElement("a",{...M,...H},r)}),g=m;("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},2295:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{requestIdleCallback:function(){return r},cancelIdleCallback:function(){return n}});let r="undefined"!=typeof self&&self.requestIdleCallback&&self.requestIdleCallback.bind(window)||function(e){let t=Date.now();return self.setTimeout(function(){e({didTimeout:!1,timeRemaining:function(){return Math.max(0,50-(Date.now()-t))}})},1)},n="undefined"!=typeof self&&self.cancelIdleCallback&&self.cancelIdleCallback.bind(window)||function(e){return clearTimeout(e)};("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},9377:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"resolveHref",{enumerable:!0,get:function(){return f}});let n=r(95),o=r(7860),i=r(3302),a=r(703),u=r(2038),c=r(9677),l=r(6553),s=r(2157);function f(e,t,r){let f;let p="string"==typeof t?t:(0,o.formatWithValidation)(t),d=p.match(/^[a-zA-Z]{1,}:\/\//),h=d?p.slice(d[0].length):p,y=h.split("?",1);if((y[0]||"").match(/(\/\/|\\)/)){console.error("Invalid href '"+p+"' passed to next/router in page: '"+e.pathname+"'. Repeated forward-slashes (//) or backslashes \\ are not valid in the href.");let t=(0,a.normalizeRepeatedSlashes)(h);p=(d?d[0]:"")+t}if(!(0,c.isLocalURL)(p))return r?[p]:p;try{f=new URL(p.startsWith("#")?e.asPath:e.pathname,"http://n")}catch(e){f=new URL("/","http://n")}try{let e=new URL(p,f);e.pathname=(0,u.normalizePathTrailingSlash)(e.pathname);let t="";if((0,l.isDynamicRoute)(e.pathname)&&e.searchParams&&r){let r=(0,n.searchParamsToUrlQuery)(e.searchParams),{result:a,params:u}=(0,s.interpolateAs)(e.pathname,e.pathname,r);a&&(t=(0,o.formatWithValidation)({pathname:a,hash:e.hash,query:(0,i.omit)(r,u)}))}let a=e.origin===f.origin?e.href.slice(e.origin.length):e.href;return r?[a,t||a]:a}catch(e){return r?[p]:p}}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},3582:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"useIntersection",{enumerable:!0,get:function(){return c}});let n=r(4218),o=r(2295),i="function"==typeof IntersectionObserver,a=new Map,u=[];function c(e){let{rootRef:t,rootMargin:r,disabled:c}=e,l=c||!i,[s,f]=(0,n.useState)(!1),p=(0,n.useRef)(null),d=(0,n.useCallback)(e=>{p.current=e},[]);(0,n.useEffect)(()=>{if(i){if(l||s)return;let e=p.current;if(e&&e.tagName){let n=function(e,t,r){let{id:n,observer:o,elements:i}=function(e){let t;let r={root:e.root||null,margin:e.rootMargin||""},n=u.find(e=>e.root===r.root&&e.margin===r.margin);if(n&&(t=a.get(n)))return t;let o=new Map,i=new IntersectionObserver(e=>{e.forEach(e=>{let t=o.get(e.target),r=e.isIntersecting||e.intersectionRatio>0;t&&r&&t(r)})},e);return t={id:r,observer:i,elements:o},u.push(r),a.set(r,t),t}(r);return i.set(e,t),o.observe(e),function(){if(i.delete(e),o.unobserve(e),0===i.size){o.disconnect(),a.delete(n);let e=u.findIndex(e=>e.root===n.root&&e.margin===n.margin);e>-1&&u.splice(e,1)}}}(e,e=>e&&f(e),{root:null==t?void 0:t.current,rootMargin:r});return n}}else if(!s){let e=(0,o.requestIdleCallback)(()=>f(!0));return()=>(0,o.cancelIdleCallback)(e)}},[l,r,t,s,p.current]);let h=(0,n.useCallback)(()=>{f(!1)},[]);return[d,s,h]}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},3102:(e,t,r)=>{"use strict";e.exports=r(2337).vendored.contexts.RouterContext},9743:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"escapeStringRegexp",{enumerable:!0,get:function(){return o}});let r=/[|\\{}()[\]^$+*?.-]/,n=/[|\\{}()[\]^$+*?.-]/g;function o(e){return r.test(e)?e.replace(n,"\\$&"):e}},7860:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{formatUrl:function(){return a},urlObjectKeys:function(){return u},formatWithValidation:function(){return c}});let n=r(2008),o=n._(r(95)),i=/https?|ftp|gopher|file/;function a(e){let{auth:t,hostname:r}=e,n=e.protocol||"",a=e.pathname||"",u=e.hash||"",c=e.query||"",l=!1;t=t?encodeURIComponent(t).replace(/%3A/i,":")+"@":"",e.host?l=t+e.host:r&&(l=t+(~r.indexOf(":")?"["+r+"]":r),e.port&&(l+=":"+e.port)),c&&"object"==typeof c&&(c=String(o.urlQueryToSearchParams(c)));let s=e.search||c&&"?"+c||"";return n&&!n.endsWith(":")&&(n+=":"),e.slashes||(!n||i.test(n))&&!1!==l?(l="//"+(l||""),a&&"/"!==a[0]&&(a="/"+a)):l||(l=""),u&&"#"!==u[0]&&(u="#"+u),s&&"?"!==s[0]&&(s="?"+s),""+n+l+(a=a.replace(/[?#]/g,encodeURIComponent))+(s=s.replace("#","%23"))+u}let u=["auth","hash","host","hostname","href","path","pathname","port","protocol","query","search","slashes"];function c(e){return a(e)}},6553:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{getSortedRoutes:function(){return n.getSortedRoutes},isDynamicRoute:function(){return o.isDynamicRoute}});let n=r(6760),o=r(6537)},2157:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"interpolateAs",{enumerable:!0,get:function(){return i}});let n=r(2413),o=r(745);function i(e,t,r){let i="",a=(0,o.getRouteRegex)(e),u=a.groups,c=(t!==e?(0,n.getRouteMatcher)(a)(t):"")||r;i=e;let l=Object.keys(u);return l.every(e=>{let t=c[e]||"",{repeat:r,optional:n}=u[e],o="["+(r?"...":"")+e+"]";return n&&(o=(t?"":"/")+"["+o+"]"),r&&!Array.isArray(t)&&(t=[t]),(n||e in c)&&(i=i.replace(o,r?t.map(e=>encodeURIComponent(e)).join("/"):encodeURIComponent(t))||"/")})||(i=""),{params:l,result:i}}},6537:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"isDynamicRoute",{enumerable:!0,get:function(){return i}});let n=r(5513),o=/\/\[[^/]+?\](?=\/|$)/;function i(e){return(0,n.isInterceptionRouteAppPath)(e)&&(e=(0,n.extractInterceptionRouteInformation)(e).interceptedRoute),o.test(e)}},9677:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"isLocalURL",{enumerable:!0,get:function(){return i}});let n=r(703),o=r(8423);function i(e){if(!(0,n.isAbsoluteUrl)(e))return!0;try{let t=(0,n.getLocationOrigin)(),r=new URL(e,t);return r.origin===t&&(0,o.hasBasePath)(r.pathname)}catch(e){return!1}}},3302:(e,t)=>{"use strict";function r(e,t){let r={};return Object.keys(e).forEach(n=>{t.includes(n)||(r[n]=e[n])}),r}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"omit",{enumerable:!0,get:function(){return r}})},95:(e,t)=>{"use strict";function r(e){let t={};return e.forEach((e,r)=>{void 0===t[r]?t[r]=e:Array.isArray(t[r])?t[r].push(e):t[r]=[t[r],e]}),t}function n(e){return"string"!=typeof e&&("number"!=typeof e||isNaN(e))&&"boolean"!=typeof e?"":String(e)}function o(e){let t=new URLSearchParams;return Object.entries(e).forEach(e=>{let[r,o]=e;Array.isArray(o)?o.forEach(e=>t.append(r,n(e))):t.set(r,n(o))}),t}function i(e){for(var t=arguments.length,r=Array(t>1?t-1:0),n=1;n<t;n++)r[n-1]=arguments[n];return r.forEach(t=>{Array.from(t.keys()).forEach(t=>e.delete(t)),t.forEach((t,r)=>e.append(r,t))}),e}Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{searchParamsToUrlQuery:function(){return r},urlQueryToSearchParams:function(){return o},assign:function(){return i}})},2413:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"getRouteMatcher",{enumerable:!0,get:function(){return o}});let n=r(703);function o(e){let{re:t,groups:r}=e;return e=>{let o=t.exec(e);if(!o)return!1;let i=e=>{try{return decodeURIComponent(e)}catch(e){throw new n.DecodeError("failed to decode param")}},a={};return Object.keys(r).forEach(e=>{let t=r[e],n=o[t.pos];void 0!==n&&(a[e]=~n.indexOf("/")?n.split("/").map(e=>i(e)):t.repeat?[i(n)]:i(n))}),a}}},745:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{getRouteRegex:function(){return c},getNamedRouteRegex:function(){return f},getNamedMiddlewareRegex:function(){return p}});let n=r(5513),o=r(9743),i=r(4538);function a(e){let t=e.startsWith("[")&&e.endsWith("]");t&&(e=e.slice(1,-1));let r=e.startsWith("...");return r&&(e=e.slice(3)),{key:e,repeat:r,optional:t}}function u(e){let t=(0,i.removeTrailingSlash)(e).slice(1).split("/"),r={},u=1;return{parameterizedRoute:t.map(e=>{let t=n.INTERCEPTION_ROUTE_MARKERS.find(t=>e.startsWith(t)),i=e.match(/\[((?:\[.*\])|.+)\]/);if(t&&i){let{key:e,optional:n,repeat:c}=a(i[1]);return r[e]={pos:u++,repeat:c,optional:n},"/"+(0,o.escapeStringRegexp)(t)+"([^/]+?)"}if(!i)return"/"+(0,o.escapeStringRegexp)(e);{let{key:e,repeat:t,optional:n}=a(i[1]);return r[e]={pos:u++,repeat:t,optional:n},t?n?"(?:/(.+?))?":"/(.+?)":"/([^/]+?)"}}).join(""),groups:r}}function c(e){let{parameterizedRoute:t,groups:r}=u(e);return{re:RegExp("^"+t+"(?:/)?$"),groups:r}}function l(e){let{getSafeRouteKey:t,segment:r,routeKeys:n,keyPrefix:o}=e,{key:i,optional:u,repeat:c}=a(r),l=i.replace(/\W/g,"");o&&(l=""+o+l);let s=!1;return(0===l.length||l.length>30)&&(s=!0),isNaN(parseInt(l.slice(0,1)))||(s=!0),s&&(l=t()),o?n[l]=""+o+i:n[l]=""+i,c?u?"(?:/(?<"+l+">.+?))?":"/(?<"+l+">.+?)":"/(?<"+l+">[^/]+?)"}function s(e,t){let r;let a=(0,i.removeTrailingSlash)(e).slice(1).split("/"),u=(r=0,()=>{let e="",t=++r;for(;t>0;)e+=String.fromCharCode(97+(t-1)%26),t=Math.floor((t-1)/26);return e}),c={};return{namedParameterizedRoute:a.map(e=>{let r=n.INTERCEPTION_ROUTE_MARKERS.some(t=>e.startsWith(t)),i=e.match(/\[((?:\[.*\])|.+)\]/);return r&&i?l({getSafeRouteKey:u,segment:i[1],routeKeys:c,keyPrefix:t?"nxtI":void 0}):i?l({getSafeRouteKey:u,segment:i[1],routeKeys:c,keyPrefix:t?"nxtP":void 0}):"/"+(0,o.escapeStringRegexp)(e)}).join(""),routeKeys:c}}function f(e,t){let r=s(e,t);return{...c(e),namedRegex:"^"+r.namedParameterizedRoute+"(?:/)?$",routeKeys:r.routeKeys}}function p(e,t){let{parameterizedRoute:r}=u(e),{catchAll:n=!0}=t;if("/"===r)return{namedRegex:"^/"+(n?".*":"")+"$"};let{namedParameterizedRoute:o}=s(e,!1);return{namedRegex:"^"+o+(n?"(?:(/.*)?)":"")+"$"}}},6760:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"getSortedRoutes",{enumerable:!0,get:function(){return n}});class r{insert(e){this._insert(e.split("/").filter(Boolean),[],!1)}smoosh(){return this._smoosh()}_smoosh(e){void 0===e&&(e="/");let t=[...this.children.keys()].sort();null!==this.slugName&&t.splice(t.indexOf("[]"),1),null!==this.restSlugName&&t.splice(t.indexOf("[...]"),1),null!==this.optionalRestSlugName&&t.splice(t.indexOf("[[...]]"),1);let r=t.map(t=>this.children.get(t)._smoosh(""+e+t+"/")).reduce((e,t)=>[...e,...t],[]);if(null!==this.slugName&&r.push(...this.children.get("[]")._smoosh(e+"["+this.slugName+"]/")),!this.placeholder){let t="/"===e?"/":e.slice(0,-1);if(null!=this.optionalRestSlugName)throw Error('You cannot define a route with the same specificity as a optional catch-all route ("'+t+'" and "'+t+"[[..."+this.optionalRestSlugName+']]").');r.unshift(t)}return null!==this.restSlugName&&r.push(...this.children.get("[...]")._smoosh(e+"[..."+this.restSlugName+"]/")),null!==this.optionalRestSlugName&&r.push(...this.children.get("[[...]]")._smoosh(e+"[[..."+this.optionalRestSlugName+"]]/")),r}_insert(e,t,n){if(0===e.length){this.placeholder=!1;return}if(n)throw Error("Catch-all must be the last part of the URL.");let o=e[0];if(o.startsWith("[")&&o.endsWith("]")){let r=o.slice(1,-1),a=!1;if(r.startsWith("[")&&r.endsWith("]")&&(r=r.slice(1,-1),a=!0),r.startsWith("...")&&(r=r.substring(3),n=!0),r.startsWith("[")||r.endsWith("]"))throw Error("Segment names may not start or end with extra brackets ('"+r+"').");if(r.startsWith("."))throw Error("Segment names may not start with erroneous periods ('"+r+"').");function i(e,r){if(null!==e&&e!==r)throw Error("You cannot use different slug names for the same dynamic path ('"+e+"' !== '"+r+"').");t.forEach(e=>{if(e===r)throw Error('You cannot have the same slug name "'+r+'" repeat within a single dynamic path');if(e.replace(/\W/g,"")===o.replace(/\W/g,""))throw Error('You cannot have the slug names "'+e+'" and "'+r+'" differ only by non-word symbols within a single dynamic path')}),t.push(r)}if(n){if(a){if(null!=this.restSlugName)throw Error('You cannot use both an required and optional catch-all route at the same level ("[...'+this.restSlugName+']" and "'+e[0]+'" ).');i(this.optionalRestSlugName,r),this.optionalRestSlugName=r,o="[[...]]"}else{if(null!=this.optionalRestSlugName)throw Error('You cannot use both an optional and required catch-all route at the same level ("[[...'+this.optionalRestSlugName+']]" and "'+e[0]+'").');i(this.restSlugName,r),this.restSlugName=r,o="[...]"}}else{if(a)throw Error('Optional route parameters are not yet supported ("'+e[0]+'").');i(this.slugName,r),this.slugName=r,o="[]"}}this.children.has(o)||this.children.set(o,new r),this.children.get(o)._insert(e.slice(1),t,n)}constructor(){this.placeholder=!0,this.children=new Map,this.slugName=null,this.restSlugName=null,this.optionalRestSlugName=null}}function n(e){let t=new r;return e.forEach(e=>t.insert(e)),t.smoosh()}},703:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{WEB_VITALS:function(){return r},execOnce:function(){return n},isAbsoluteUrl:function(){return i},getLocationOrigin:function(){return a},getURL:function(){return u},getDisplayName:function(){return c},isResSent:function(){return l},normalizeRepeatedSlashes:function(){return s},loadGetInitialProps:function(){return f},SP:function(){return p},ST:function(){return d},DecodeError:function(){return h},NormalizeError:function(){return y},PageNotFoundError:function(){return v},MissingStaticPage:function(){return m},MiddlewareNotFoundError:function(){return g},stringifyError:function(){return b}});let r=["CLS","FCP","FID","INP","LCP","TTFB"];function n(e){let t,r=!1;return function(){for(var n=arguments.length,o=Array(n),i=0;i<n;i++)o[i]=arguments[i];return r||(r=!0,t=e(...o)),t}}let o=/^[a-zA-Z][a-zA-Z\d+\-.]*?:/,i=e=>o.test(e);function a(){let{protocol:e,hostname:t,port:r}=window.location;return e+"//"+t+(r?":"+r:"")}function u(){let{href:e}=window.location,t=a();return e.substring(t.length)}function c(e){return"string"==typeof e?e:e.displayName||e.name||"Unknown"}function l(e){return e.finished||e.headersSent}function s(e){let t=e.split("?"),r=t[0];return r.replace(/\\/g,"/").replace(/\/\/+/g,"/")+(t[1]?"?"+t.slice(1).join("?"):"")}async function f(e,t){let r=t.res||t.ctx&&t.ctx.res;if(!e.getInitialProps)return t.ctx&&t.Component?{pageProps:await f(t.Component,t.ctx)}:{};let n=await e.getInitialProps(t);if(r&&l(r))return n;if(!n){let t='"'+c(e)+'.getInitialProps()" should resolve to an object. But found "'+n+'" instead.';throw Error(t)}return n}let p="undefined"!=typeof performance,d=p&&["mark","measure","getEntriesByName"].every(e=>"function"==typeof performance[e]);class h extends Error{}class y extends Error{}class v extends Error{constructor(e){super(),this.code="ENOENT",this.name="PageNotFoundError",this.message="Cannot find module for page: "+e}}class m extends Error{constructor(e,t){super(),this.message="Failed to load static file for page: "+e+" "+t}}class g extends Error{constructor(){super(),this.code="ENOENT",this.message="Cannot find the middleware module"}}function b(e){return JSON.stringify({message:e.message,stack:e.stack})}},5548:(e,t,r)=>{"use strict";e.exports=r(7490)},1018:(e,t,r)=>{"use strict";e.exports=r(7804)},5457:(e,t)=>{"use strict";/**
 * @license React
 * react-is.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var r=Symbol.for("react.element"),n=Symbol.for("react.portal"),o=Symbol.for("react.fragment"),i=Symbol.for("react.strict_mode"),a=Symbol.for("react.profiler"),u=Symbol.for("react.provider"),c=Symbol.for("react.context"),l=Symbol.for("react.server_context"),s=Symbol.for("react.forward_ref"),f=Symbol.for("react.suspense"),p=Symbol.for("react.suspense_list"),d=Symbol.for("react.memo"),h=Symbol.for("react.lazy");Symbol.for("react.offscreen"),Symbol.for("react.module.reference"),t.isFragment=function(e){return function(e){if("object"==typeof e&&null!==e){var t=e.$$typeof;switch(t){case r:switch(e=e.type){case o:case a:case i:case f:case p:return e;default:switch(e=e&&e.$$typeof){case l:case c:case s:case h:case d:case u:return e;default:return t}}case n:return t}}}(e)===o}},5812:(e,t,r)=>{"use strict";e.exports=r(5457)},8318:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>t2});var n,o,i,a,u,c,l,s,f,p,d,h,y,v,m,g,b,x,w,O,j,S=r(3854),P=r(4218),E=r.t(P,2);function k(e){"function"==typeof queueMicrotask?queueMicrotask(e):Promise.resolve().then(e).catch(e=>setTimeout(()=>{throw e}))}function A(){let e=[],t={addEventListener:(e,r,n,o)=>(e.addEventListener(r,n,o),t.add(()=>e.removeEventListener(r,n,o))),requestAnimationFrame(...e){let r=requestAnimationFrame(...e);return t.add(()=>cancelAnimationFrame(r))},nextFrame:(...e)=>t.requestAnimationFrame(()=>t.requestAnimationFrame(...e)),setTimeout(...e){let r=setTimeout(...e);return t.add(()=>clearTimeout(r))},microTask(...e){let r={current:!0};return k(()=>{r.current&&e[0]()}),t.add(()=>{r.current=!1})},style(e,t,r){let n=e.style.getPropertyValue(t);return Object.assign(e.style,{[t]:r}),this.add(()=>{Object.assign(e.style,{[t]:n})})},group(e){let t=A();return e(t),this.add(()=>t.dispose())},add:t=>(e.push(t),()=>{let r=e.indexOf(t);if(r>=0)for(let t of e.splice(r,1))t()}),dispose(){for(let t of e.splice(0))t()}};return t}function M(){let[e]=(0,P.useState)(A);return(0,P.useEffect)(()=>()=>e.dispose(),[e]),e}var T=Object.defineProperty,_=(e,t,r)=>t in e?T(e,t,{enumerable:!0,configurable:!0,writable:!0,value:r}):e[t]=r,C=(e,t,r)=>(_(e,"symbol"!=typeof t?t+"":t,r),r);class N{constructor(){C(this,"current",this.detect()),C(this,"handoffState","pending"),C(this,"currentId",0)}set(e){this.current!==e&&(this.handoffState="pending",this.currentId=0,this.current=e)}reset(){this.set(this.detect())}nextId(){return++this.currentId}get isServer(){return"server"===this.current}get isClient(){return"client"===this.current}detect(){return"server"}handoff(){"pending"===this.handoffState&&(this.handoffState="complete")}get isHandoffComplete(){return"complete"===this.handoffState}}let D=new N,I=(e,t)=>{D.isServer?(0,P.useEffect)(e,t):(0,P.useLayoutEffect)(e,t)};function R(e){let t=(0,P.useRef)(e);return I(()=>{t.current=e},[e]),t}let L=function(e){let t=R(e);return P.useCallback((...e)=>t.current(...e),[t])};function B(){let e=(0,P.useRef)(!1);return I(()=>(e.current=!0,()=>{e.current=!1}),[]),e}function F(){let e;let t=(e="undefined"==typeof document,"useSyncExternalStore"in E&&(0,E.useSyncExternalStore)(()=>()=>{},()=>!1,()=>!e)),[r,n]=P.useState(D.isHandoffComplete);return r&&!1===D.isHandoffComplete&&n(!1),P.useEffect(()=>{!0!==r&&n(!0)},[r]),P.useEffect(()=>D.handoff(),[]),!t&&r}let U=Symbol();function z(...e){let t=(0,P.useRef)(e);(0,P.useEffect)(()=>{t.current=e},[e]);let r=L(e=>{for(let r of t.current)null!=r&&("function"==typeof r?r(e):r.current=e)});return e.every(e=>null==e||(null==e?void 0:e[U]))?void 0:r}function $(e,t,...r){if(e in t){let n=t[e];return"function"==typeof n?n(...r):n}let n=Error(`Tried to handle "${e}" but there is no handler defined. Only defined handlers are: ${Object.keys(t).map(e=>`"${e}"`).join(", ")}.`);throw Error.captureStackTrace&&Error.captureStackTrace(n,$),n}function W(e,...t){e&&t.length>0&&e.classList.add(...t)}function q(e,...t){e&&t.length>0&&e.classList.remove(...t)}let H=(0,P.createContext)(null);H.displayName="OpenClosedContext";var V=((n=V||{})[n.Open=1]="Open",n[n.Closed=2]="Closed",n[n.Closing=4]="Closing",n[n.Opening=8]="Opening",n);function X(){return(0,P.useContext)(H)}function Z({value:e,children:t}){return P.createElement(H.Provider,{value:e},t)}function G(...e){return Array.from(new Set(e.flatMap(e=>"string"==typeof e?e.split(" "):[]))).filter(Boolean).join(" ")}var Y=((o=Y||{})[o.None=0]="None",o[o.RenderStrategy=1]="RenderStrategy",o[o.Static=2]="Static",o),K=((i=K||{})[i.Unmount=0]="Unmount",i[i.Hidden=1]="Hidden",i);function Q({ourProps:e,theirProps:t,slot:r,defaultTag:n,features:o,visible:i=!0,name:a,mergeRefs:u}){u=null!=u?u:ee;let c=et(t,e);if(i)return J(c,r,n,a,u);let l=null!=o?o:0;if(2&l){let{static:e=!1,...t}=c;if(e)return J(t,r,n,a,u)}if(1&l){let{unmount:e=!0,...t}=c;return $(e?0:1,{0:()=>null,1:()=>J({...t,hidden:!0,style:{display:"none"}},r,n,a,u)})}return J(c,r,n,a,u)}function J(e,t={},r,n,o){let{as:i=r,children:a,refName:u="ref",...c}=eo(e,["unmount","static"]),l=void 0!==e.ref?{[u]:e.ref}:{},s="function"==typeof a?a(t):a;"className"in c&&c.className&&"function"==typeof c.className&&(c.className=c.className(t));let f={};if(t){let e=!1,r=[];for(let[n,o]of Object.entries(t))"boolean"==typeof o&&(e=!0),!0===o&&r.push(n);e&&(f["data-headlessui-state"]=r.join(" "))}if(i===P.Fragment&&Object.keys(en(c)).length>0){if(!(0,P.isValidElement)(s)||Array.isArray(s)&&s.length>1)throw Error(['Passing props on "Fragment"!',"",`The current component <${n} /> is rendering a "Fragment".`,"However we need to passthrough the following props:",Object.keys(c).map(e=>`  - ${e}`).join(`
`),"","You can apply a few solutions:",['Add an `as="..."` prop, to ensure that we render an actual element instead of a "Fragment".',"Render a single element as the child so that we can forward the props onto that element."].map(e=>`  - ${e}`).join(`
`)].join(`
`));let e=s.props,t="function"==typeof(null==e?void 0:e.className)?(...t)=>G(null==e?void 0:e.className(...t),c.className):G(null==e?void 0:e.className,c.className);return(0,P.cloneElement)(s,Object.assign({},et(s.props,en(eo(c,["ref"]))),f,l,{ref:o(s.ref,l.ref)},t?{className:t}:{}))}return(0,P.createElement)(i,Object.assign({},eo(c,["ref"]),i!==P.Fragment&&l,i!==P.Fragment&&f),s)}function ee(...e){return e.every(e=>null==e)?void 0:t=>{for(let r of e)null!=r&&("function"==typeof r?r(t):r.current=t)}}function et(...e){if(0===e.length)return{};if(1===e.length)return e[0];let t={},r={};for(let n of e)for(let e in n)e.startsWith("on")&&"function"==typeof n[e]?(null!=r[e]||(r[e]=[]),r[e].push(n[e])):t[e]=n[e];if(t.disabled||t["aria-disabled"])return Object.assign(t,Object.fromEntries(Object.keys(r).map(e=>[e,void 0])));for(let e in r)Object.assign(t,{[e](t,...n){for(let o of r[e]){if((t instanceof Event||(null==t?void 0:t.nativeEvent)instanceof Event)&&t.defaultPrevented)return;o(t,...n)}}});return t}function er(e){var t;return Object.assign((0,P.forwardRef)(e),{displayName:null!=(t=e.displayName)?t:e.name})}function en(e){let t=Object.assign({},e);for(let e in t)void 0===t[e]&&delete t[e];return t}function eo(e,t=[]){let r=Object.assign({},e);for(let e of t)e in r&&delete r[e];return r}function ei(e=""){return e.split(/\s+/).filter(e=>e.length>1)}let ea=(0,P.createContext)(null);ea.displayName="TransitionContext";var eu=((a=eu||{}).Visible="visible",a.Hidden="hidden",a);let ec=(0,P.createContext)(null);function el(e){return"children"in e?el(e.children):e.current.filter(({el:e})=>null!==e.current).filter(({state:e})=>"visible"===e).length>0}function es(e,t){let r=R(e),n=(0,P.useRef)([]),o=B(),i=M(),a=L((e,t=K.Hidden)=>{let a=n.current.findIndex(({el:t})=>t===e);-1!==a&&($(t,{[K.Unmount](){n.current.splice(a,1)},[K.Hidden](){n.current[a].state="hidden"}}),i.microTask(()=>{var e;!el(n)&&o.current&&(null==(e=r.current)||e.call(r))}))}),u=L(e=>{let t=n.current.find(({el:t})=>t===e);return t?"visible"!==t.state&&(t.state="visible"):n.current.push({el:e,state:"visible"}),()=>a(e,K.Unmount)}),c=(0,P.useRef)([]),l=(0,P.useRef)(Promise.resolve()),s=(0,P.useRef)({enter:[],leave:[],idle:[]}),f=L((e,r,n)=>{c.current.splice(0),t&&(t.chains.current[r]=t.chains.current[r].filter(([t])=>t!==e)),null==t||t.chains.current[r].push([e,new Promise(e=>{c.current.push(e)})]),null==t||t.chains.current[r].push([e,new Promise(e=>{Promise.all(s.current[r].map(([e,t])=>t)).then(()=>e())})]),"enter"===r?l.current=l.current.then(()=>null==t?void 0:t.wait.current).then(()=>n(r)):n(r)}),p=L((e,t,r)=>{Promise.all(s.current[t].splice(0).map(([e,t])=>t)).then(()=>{var e;null==(e=c.current.shift())||e()}).then(()=>r(t))});return(0,P.useMemo)(()=>({children:n,register:u,unregister:a,onStart:f,onStop:p,wait:l,chains:s}),[u,a,n,f,p,s,l])}function ef(){}ec.displayName="NestingContext";let ep=["beforeEnter","afterEnter","beforeLeave","afterLeave"];function ed(e){var t;let r={};for(let n of ep)r[n]=null!=(t=e[n])?t:ef;return r}let eh=Y.RenderStrategy,ey=er(function(e,t){let{show:r,appear:n=!1,unmount:o=!0,...i}=e,a=(0,P.useRef)(null),u=z(a,t);F();let c=X();if(void 0===r&&null!==c&&(r=(c&V.Open)===V.Open),![!0,!1].includes(r))throw Error("A <Transition /> is used but it is missing a `show={true | false}` prop.");let[l,s]=(0,P.useState)(r?"visible":"hidden"),f=es(()=>{s("hidden")}),[p,d]=(0,P.useState)(!0),h=(0,P.useRef)([r]);I(()=>{!1!==p&&h.current[h.current.length-1]!==r&&(h.current.push(r),d(!1))},[h,r]);let y=(0,P.useMemo)(()=>({show:r,appear:n,initial:p}),[r,n,p]);(0,P.useEffect)(()=>{if(r)s("visible");else if(el(f)){let e=a.current;if(!e)return;let t=e.getBoundingClientRect();0===t.x&&0===t.y&&0===t.width&&0===t.height&&s("hidden")}else s("hidden")},[r,f]);let v={unmount:o},m=L(()=>{var t;p&&d(!1),null==(t=e.beforeEnter)||t.call(e)}),g=L(()=>{var t;p&&d(!1),null==(t=e.beforeLeave)||t.call(e)});return P.createElement(ec.Provider,{value:f},P.createElement(ea.Provider,{value:y},Q({ourProps:{...v,as:P.Fragment,children:P.createElement(ev,{ref:u,...v,...i,beforeEnter:m,beforeLeave:g})},theirProps:{},defaultTag:P.Fragment,features:eh,visible:"visible"===l,name:"Transition"})))}),ev=er(function(e,t){var r,n,o;let i;let{beforeEnter:a,afterEnter:u,beforeLeave:c,afterLeave:l,enter:s,enterFrom:f,enterTo:p,entered:d,leave:h,leaveFrom:y,leaveTo:v,...m}=e,g=(0,P.useRef)(null),b=z(g,t),x=null==(r=m.unmount)||r?K.Unmount:K.Hidden,{show:w,appear:O,initial:j}=function(){let e=(0,P.useContext)(ea);if(null===e)throw Error("A <Transition.Child /> is used but it is missing a parent <Transition /> or <Transition.Root />.");return e}(),[S,E]=(0,P.useState)(w?"visible":"hidden"),k=function(){let e=(0,P.useContext)(ec);if(null===e)throw Error("A <Transition.Child /> is used but it is missing a parent <Transition /> or <Transition.Root />.");return e}(),{register:T,unregister:_}=k;(0,P.useEffect)(()=>T(g),[T,g]),(0,P.useEffect)(()=>{if(x===K.Hidden&&g.current){if(w&&"visible"!==S){E("visible");return}return $(S,{hidden:()=>_(g),visible:()=>T(g)})}},[S,g,T,_,w,x]);let C=R({base:ei(m.className),enter:ei(s),enterFrom:ei(f),enterTo:ei(p),entered:ei(d),leave:ei(h),leaveFrom:ei(y),leaveTo:ei(v)}),N=(o={beforeEnter:a,afterEnter:u,beforeLeave:c,afterLeave:l},i=(0,P.useRef)(ed(o)),(0,P.useEffect)(()=>{i.current=ed(o)},[o]),i),D=F();(0,P.useEffect)(()=>{if(D&&"visible"===S&&null===g.current)throw Error("Did you forget to passthrough the `ref` to the actual DOM node?")},[g,S,D]);let U=O&&w&&j,H=D&&(!j||O)?w?"enter":"leave":"idle",X=function(e=0){let[t,r]=(0,P.useState)(e),n=B(),o=(0,P.useCallback)(e=>{n.current&&r(t=>t|e)},[t,n]),i=(0,P.useCallback)(e=>!!(t&e),[t]);return{flags:t,addFlag:o,hasFlag:i,removeFlag:(0,P.useCallback)(e=>{n.current&&r(t=>t&~e)},[r,n]),toggleFlag:(0,P.useCallback)(e=>{n.current&&r(t=>t^e)},[r])}}(0),Y=L(e=>$(e,{enter:()=>{X.addFlag(V.Opening),N.current.beforeEnter()},leave:()=>{X.addFlag(V.Closing),N.current.beforeLeave()},idle:()=>{}})),J=L(e=>$(e,{enter:()=>{X.removeFlag(V.Opening),N.current.afterEnter()},leave:()=>{X.removeFlag(V.Closing),N.current.afterLeave()},idle:()=>{}})),ee=es(()=>{E("hidden"),_(g)},k),et=(0,P.useRef)(!1);!function({immediate:e,container:t,direction:r,classes:n,onStart:o,onStop:i}){let a=B(),u=M(),c=R(r);I(()=>{e&&(c.current="enter")},[e]),I(()=>{let e=A();u.add(e.dispose);let r=t.current;if(r&&"idle"!==c.current&&a.current){var l,s,f;let t,a,u,p,d,h,y;return e.dispose(),o.current(c.current),e.add((l=n.current,s="enter"===c.current,f=()=>{e.dispose(),i.current(c.current)},a=s?"enter":"leave",u=A(),p=void 0!==f?(t={called:!1},(...e)=>{if(!t.called)return t.called=!0,f(...e)}):()=>{},"enter"===a&&(r.removeAttribute("hidden"),r.style.display=""),d=$(a,{enter:()=>l.enter,leave:()=>l.leave}),h=$(a,{enter:()=>l.enterTo,leave:()=>l.leaveTo}),y=$(a,{enter:()=>l.enterFrom,leave:()=>l.leaveFrom}),q(r,...l.base,...l.enter,...l.enterTo,...l.enterFrom,...l.leave,...l.leaveFrom,...l.leaveTo,...l.entered),W(r,...l.base,...d,...y),u.nextFrame(()=>{q(r,...l.base,...d,...y),W(r,...l.base,...d,...h),function(e,t){let r=A();if(!e)return r.dispose;let{transitionDuration:n,transitionDelay:o}=getComputedStyle(e),[i,a]=[n,o].map(e=>{let[t=0]=e.split(",").filter(Boolean).map(e=>e.includes("ms")?parseFloat(e):1e3*parseFloat(e)).sort((e,t)=>t-e);return t}),u=i+a;if(0!==u){r.group(r=>{r.setTimeout(()=>{t(),r.dispose()},u),r.addEventListener(e,"transitionrun",e=>{e.target===e.currentTarget&&r.dispose()})});let n=r.addEventListener(e,"transitionend",e=>{e.target===e.currentTarget&&(t(),n())})}else t();r.add(()=>t()),r.dispose}(r,()=>(q(r,...l.base,...d),W(r,...l.base,...l.entered),p()))}),u.dispose)),e.dispose}},[r])}({immediate:U,container:g,classes:C,direction:H,onStart:R(e=>{et.current=!0,ee.onStart(g,e,Y)}),onStop:R(e=>{et.current=!1,ee.onStop(g,e,J),"leave"!==e||el(ee)||(E("hidden"),_(g))})});let er=m;return U?er={...er,className:G(m.className,...C.current.enter,...C.current.enterFrom)}:et.current&&(er.className=G(m.className,null==(n=g.current)?void 0:n.className),""===er.className&&delete er.className),P.createElement(ec.Provider,{value:ee},P.createElement(Z,{value:$(S,{visible:V.Open,hidden:V.Closed})|X.flags},Q({ourProps:{ref:b},theirProps:er,defaultTag:"div",features:eh,visible:"visible"===S,name:"Transition.Child"})))}),em=er(function(e,t){let r=null!==(0,P.useContext)(ea),n=null!==X();return P.createElement(P.Fragment,null,!r&&n?P.createElement(ey,{ref:t,...e}):P.createElement(ev,{ref:t,...e}))}),eg=Object.assign(ey,{Child:em,Root:ey});function eb(e,t,r,n){let o=R(r);(0,P.useEffect)(()=>{function r(e){o.current(e)}return(e=null!=e?e:window).addEventListener(t,r,n),()=>e.removeEventListener(t,r,n)},[e,t,n])}function ex(e){let t=L(e),r=(0,P.useRef)(!1);(0,P.useEffect)(()=>(r.current=!1,()=>{r.current=!0,k(()=>{r.current&&t()})}),[t])}function ew(e){return D.isServer?null:e instanceof Node?e.ownerDocument:null!=e&&e.hasOwnProperty("current")&&e.current instanceof Node?e.current.ownerDocument:document}function eO(...e){return(0,P.useMemo)(()=>ew(...e),[...e])}function ej(e,t,r){let n=R(t);(0,P.useEffect)(()=>{function t(e){n.current(e)}return window.addEventListener(e,t,r),()=>window.removeEventListener(e,t,r)},[e,r])}var eS=((u=eS||{})[u.Forwards=0]="Forwards",u[u.Backwards=1]="Backwards",u);function eP(e,t){let r=(0,P.useRef)([]),n=L(e);(0,P.useEffect)(()=>{let e=[...r.current];for(let[o,i]of t.entries())if(r.current[o]!==i){let o=n(t,e);return r.current=t,o}},[n,...t])}var eE=((c=eE||{})[c.None=1]="None",c[c.Focusable=2]="Focusable",c[c.Hidden=4]="Hidden",c);let ek=er(function(e,t){var r;let{features:n=1,...o}=e;return Q({ourProps:{ref:t,"aria-hidden":(2&n)==2||(null!=(r=o["aria-hidden"])?r:void 0),hidden:(4&n)==4||void 0,style:{position:"fixed",top:1,left:1,width:1,height:0,padding:0,margin:-1,overflow:"hidden",clip:"rect(0, 0, 0, 0)",whiteSpace:"nowrap",borderWidth:"0",...(4&n)==4&&(2&n)!=2&&{display:"none"}}},theirProps:o,slot:{},defaultTag:"div",name:"Hidden"})}),eA=[],eM=["[contentEditable=true]","[tabindex]","a[href]","area[href]","button:not([disabled])","iframe","input:not([disabled])","select:not([disabled])","textarea:not([disabled])"].map(e=>`${e}:not([tabindex='-1'])`).join(",");var eT=((l=eT||{})[l.First=1]="First",l[l.Previous=2]="Previous",l[l.Next=4]="Next",l[l.Last=8]="Last",l[l.WrapAround=16]="WrapAround",l[l.NoScroll=32]="NoScroll",l),e_=((s=e_||{})[s.Error=0]="Error",s[s.Overflow=1]="Overflow",s[s.Success=2]="Success",s[s.Underflow=3]="Underflow",s),eC=((f=eC||{})[f.Previous=-1]="Previous",f[f.Next=1]="Next",f);function eN(e=document.body){return null==e?[]:Array.from(e.querySelectorAll(eM)).sort((e,t)=>Math.sign((e.tabIndex||Number.MAX_SAFE_INTEGER)-(t.tabIndex||Number.MAX_SAFE_INTEGER)))}var eD=((p=eD||{})[p.Strict=0]="Strict",p[p.Loose=1]="Loose",p);function eI(e,t=0){var r;return e!==(null==(r=ew(e))?void 0:r.body)&&$(t,{0:()=>e.matches(eM),1(){let t=e;for(;null!==t;){if(t.matches(eM))return!0;t=t.parentElement}return!1}})}function eR(e){let t=ew(e);A().nextFrame(()=>{t&&!eI(t.activeElement,0)&&eB(e)})}var eL=((d=eL||{})[d.Keyboard=0]="Keyboard",d[d.Mouse=1]="Mouse",d);function eB(e){null==e||e.focus({preventScroll:!0})}function eF(e,t=e=>e){return e.slice().sort((e,r)=>{let n=t(e),o=t(r);if(null===n||null===o)return 0;let i=n.compareDocumentPosition(o);return i&Node.DOCUMENT_POSITION_FOLLOWING?-1:i&Node.DOCUMENT_POSITION_PRECEDING?1:0})}function eU(e,t,{sorted:r=!0,relativeTo:n=null,skipElements:o=[]}={}){var i,a,u;let c=Array.isArray(e)?e.length>0?e[0].ownerDocument:document:e.ownerDocument,l=Array.isArray(e)?r?eF(e):e:eN(e);o.length>0&&l.length>1&&(l=l.filter(e=>!o.includes(e))),n=null!=n?n:c.activeElement;let s=(()=>{if(5&t)return 1;if(10&t)return -1;throw Error("Missing Focus.First, Focus.Previous, Focus.Next or Focus.Last")})(),f=(()=>{if(1&t)return 0;if(2&t)return Math.max(0,l.indexOf(n))-1;if(4&t)return Math.max(0,l.indexOf(n))+1;if(8&t)return l.length-1;throw Error("Missing Focus.First, Focus.Previous, Focus.Next or Focus.Last")})(),p=32&t?{preventScroll:!0}:{},d=0,h=l.length,y;do{if(d>=h||d+h<=0)return 0;let e=f+d;if(16&t)e=(e+h)%h;else{if(e<0)return 3;if(e>=h)return 1}null==(y=l[e])||y.focus(p),d+=s}while(y!==c.activeElement);return 6&t&&null!=(u=null==(a=null==(i=y)?void 0:i.matches)?void 0:a.call(i,"textarea,input"))&&u&&y.select(),2}function ez(e){if(!e)return new Set;if("function"==typeof e)return new Set(e());let t=new Set;for(let r of e.current)r.current instanceof HTMLElement&&t.add(r.current);return t}var e$=((h=e$||{})[h.None=1]="None",h[h.InitialFocus=2]="InitialFocus",h[h.TabLock=4]="TabLock",h[h.FocusLock=8]="FocusLock",h[h.RestoreFocus=16]="RestoreFocus",h[h.All=30]="All",h);let eW=Object.assign(er(function(e,t){let r,n=(0,P.useRef)(null),o=z(n,t),{initialFocus:i,containers:a,features:u=30,...c}=e;F()||(u=1);let l=eO(n);(function({ownerDocument:e},t){let r=function(e=!0){let t=(0,P.useRef)(eA.slice());return eP(([e],[r])=>{!0===r&&!1===e&&k(()=>{t.current.splice(0)}),!1===r&&!0===e&&(t.current=eA.slice())},[e,eA,t]),L(()=>{var e;return null!=(e=t.current.find(e=>null!=e&&e.isConnected))?e:null})}(t);eP(()=>{t||(null==e?void 0:e.activeElement)===(null==e?void 0:e.body)&&eB(r())},[t]),ex(()=>{t&&eB(r())})})({ownerDocument:l},!!(16&u));let s=function({ownerDocument:e,container:t,initialFocus:r},n){let o=(0,P.useRef)(null),i=B();return eP(()=>{if(!n)return;let a=t.current;a&&k(()=>{if(!i.current)return;let t=null==e?void 0:e.activeElement;if(null!=r&&r.current){if((null==r?void 0:r.current)===t){o.current=t;return}}else if(a.contains(t)){o.current=t;return}null!=r&&r.current?eB(r.current):eU(a,eT.First)===e_.Error&&console.warn("There are no focusable elements inside the <FocusTrap />"),o.current=null==e?void 0:e.activeElement})},[n]),o}({ownerDocument:l,container:n,initialFocus:i},!!(2&u));(function({ownerDocument:e,container:t,containers:r,previousActiveElement:n},o){let i=B();eb(null==e?void 0:e.defaultView,"focus",e=>{if(!o||!i.current)return;let a=ez(r);t.current instanceof HTMLElement&&a.add(t.current);let u=n.current;if(!u)return;let c=e.target;c&&c instanceof HTMLElement?eq(a,c)?(n.current=c,eB(c)):(e.preventDefault(),e.stopPropagation(),eB(u)):eB(n.current)},!0)})({ownerDocument:l,container:n,containers:a,previousActiveElement:s},!!(8&u));let f=(r=(0,P.useRef)(0),ej("keydown",e=>{"Tab"===e.key&&(r.current=e.shiftKey?1:0)},!0),r),p=L(e=>{let t=n.current;t&&$(f.current,{[eS.Forwards]:()=>{eU(t,eT.First,{skipElements:[e.relatedTarget]})},[eS.Backwards]:()=>{eU(t,eT.Last,{skipElements:[e.relatedTarget]})}})}),d=M(),h=(0,P.useRef)(!1);return P.createElement(P.Fragment,null,!!(4&u)&&P.createElement(ek,{as:"button",type:"button","data-headlessui-focus-guard":!0,onFocus:p,features:eE.Focusable}),Q({ourProps:{ref:o,onKeyDown(e){"Tab"==e.key&&(h.current=!0,d.requestAnimationFrame(()=>{h.current=!1}))},onBlur(e){let t=ez(a);n.current instanceof HTMLElement&&t.add(n.current);let r=e.relatedTarget;r instanceof HTMLElement&&"true"!==r.dataset.headlessuiFocusGuard&&(eq(t,r)||(h.current?eU(n.current,$(f.current,{[eS.Forwards]:()=>eT.Next,[eS.Backwards]:()=>eT.Previous})|eT.WrapAround,{relativeTo:e.target}):e.target instanceof HTMLElement&&eB(e.target)))}},theirProps:c,defaultTag:"div",name:"FocusTrap"}),!!(4&u)&&P.createElement(ek,{as:"button",type:"button","data-headlessui-focus-guard":!0,onFocus:p,features:eE.Focusable}))}),{features:e$});function eq(e,t){for(let r of e)if(r.contains(t))return!0;return!1}var eH=r(3638);let eV=(0,P.createContext)(!1);function eX(e){return P.createElement(eV.Provider,{value:e.force},e.children)}let eZ=P.Fragment,eG=P.Fragment,eY=(0,P.createContext)(null),eK=(0,P.createContext)(null),eQ=Object.assign(er(function(e,t){let r=(0,P.useRef)(null),n=z(function(e,t=!0){return Object.assign(e,{[U]:t})}(e=>{r.current=e}),t),o=eO(r),i=function(e){let t=(0,P.useContext)(eV),r=(0,P.useContext)(eY),n=eO(e),[o,i]=(0,P.useState)(()=>{if(!t&&null!==r||D.isServer)return null;let e=null==n?void 0:n.getElementById("headlessui-portal-root");if(e)return e;if(null===n)return null;let o=n.createElement("div");return o.setAttribute("id","headlessui-portal-root"),n.body.appendChild(o)});return(0,P.useEffect)(()=>{null!==o&&(null!=n&&n.body.contains(o)||null==n||n.body.appendChild(o))},[o,n]),(0,P.useEffect)(()=>{t||null!==r&&i(r.current)},[r,i,t]),o}(r),[a]=(0,P.useState)(()=>{var e;return D.isServer?null:null!=(e=null==o?void 0:o.createElement("div"))?e:null}),u=(0,P.useContext)(eK),c=F();return I(()=>{!i||!a||i.contains(a)||(a.setAttribute("data-headlessui-portal",""),i.appendChild(a))},[i,a]),I(()=>{if(a&&u)return u.register(a)},[u,a]),ex(()=>{var e;i&&a&&(a instanceof Node&&i.contains(a)&&i.removeChild(a),i.childNodes.length<=0&&(null==(e=i.parentElement)||e.removeChild(i)))}),c&&i&&a?(0,eH.createPortal)(Q({ourProps:{ref:n},theirProps:e,defaultTag:eZ,name:"Portal"}),a):null}),{Group:er(function(e,t){let{target:r,...n}=e,o={ref:z(t)};return P.createElement(eY.Provider,{value:r},Q({ourProps:o,theirProps:n,defaultTag:eG,name:"Popover.Group"}))})});"function"==typeof Object.is&&Object.is;let{useState:eJ,useEffect:e0,useLayoutEffect:e1,useDebugValue:e2}=E,e5="useSyncExternalStore"in E?E.useSyncExternalStore:function(e,t,r){return t()};function e3(){return/iPhone/gi.test(window.navigator.platform)||/Mac/gi.test(window.navigator.platform)&&window.navigator.maxTouchPoints>0}let e4=function(e,t){let r=e(),n=new Set;return{getSnapshot:()=>r,subscribe:e=>(n.add(e),()=>n.delete(e)),dispatch(e,...o){let i=t[e].call(r,...o);i&&(r=i,n.forEach(e=>e()))}}}(()=>new Map,{PUSH(e,t){var r;let n=null!=(r=this.get(e))?r:{doc:e,count:0,d:A(),meta:new Set};return n.count++,n.meta.add(t),this.set(e,n),this},POP(e,t){let r=this.get(e);return r&&(r.count--,r.meta.delete(t)),this},SCROLL_PREVENT({doc:e,d:t,meta:r}){let n;let o={doc:e,d:t,meta:function(e){let t={};for(let r of e)Object.assign(t,r(t));return t}(r)},i=[e3()?{before({doc:e,d:t,meta:r}){function n(e){return r.containers.flatMap(e=>e()).some(t=>t.contains(e))}t.microTask(()=>{var r;if("auto"!==window.getComputedStyle(e.documentElement).scrollBehavior){let r=A();r.style(e.documentElement,"scrollBehavior","auto"),t.add(()=>t.microTask(()=>r.dispose()))}let o=null!=(r=window.scrollY)?r:window.pageYOffset,i=null;t.addEventListener(e,"click",t=>{if(t.target instanceof HTMLElement)try{let r=t.target.closest("a");if(!r)return;let{hash:o}=new URL(r.href),a=e.querySelector(o);a&&!n(a)&&(i=a)}catch{}},!0),t.addEventListener(e,"touchstart",e=>{if(e.target instanceof HTMLElement){if(n(e.target)){let r=e.target;for(;r.parentElement&&n(r.parentElement);)r=r.parentElement;t.style(r,"overscrollBehavior","contain")}else t.style(e.target,"touchAction","none")}}),t.addEventListener(e,"touchmove",e=>{if(e.target instanceof HTMLElement){if(n(e.target)){let t=e.target;for(;t.parentElement&&""!==t.dataset.headlessuiPortal&&!(t.scrollHeight>t.clientHeight||t.scrollWidth>t.clientWidth);)t=t.parentElement;""===t.dataset.headlessuiPortal&&e.preventDefault()}else e.preventDefault()}},{passive:!1}),t.add(()=>{var e;o!==(null!=(e=window.scrollY)?e:window.pageYOffset)&&window.scrollTo(0,o),i&&i.isConnected&&(i.scrollIntoView({block:"nearest"}),i=null)})})}}:{},{before({doc:e}){var t;let r=e.documentElement;n=(null!=(t=e.defaultView)?t:window).innerWidth-r.clientWidth},after({doc:e,d:t}){let r=e.documentElement,o=r.clientWidth-r.offsetWidth,i=n-o;t.style(r,"paddingRight",`${i}px`)}},{before({doc:e,d:t}){t.style(e.documentElement,"overflow","hidden")}}];i.forEach(({before:e})=>null==e?void 0:e(o)),i.forEach(({after:e})=>null==e?void 0:e(o))},SCROLL_ALLOW({d:e}){e.dispose()},TEARDOWN({doc:e}){this.delete(e)}});e4.subscribe(()=>{let e=e4.getSnapshot(),t=new Map;for(let[r]of e)t.set(r,r.documentElement.style.overflow);for(let r of e.values()){let e="hidden"===t.get(r.doc),n=0!==r.count;(n&&!e||!n&&e)&&e4.dispatch(r.count>0?"SCROLL_PREVENT":"SCROLL_ALLOW",r),0===r.count&&e4.dispatch("TEARDOWN",r)}});let e6=null!=(j=P.useId)?j:function(){let e=F(),[t,r]=P.useState(e?()=>D.nextId():null);return I(()=>{null===t&&r(D.nextId())},[t]),null!=t?""+t:void 0},e7=new Map,e8=new Map;function e9(e,t=!0){I(()=>{var r;if(!t)return;let n="function"==typeof e?e():e.current;if(!n)return;let o=null!=(r=e8.get(n))?r:0;return e8.set(n,o+1),0!==o||(e7.set(n,{"aria-hidden":n.getAttribute("aria-hidden"),inert:n.inert}),n.setAttribute("aria-hidden","true"),n.inert=!0),function(){var e;if(!n)return;let t=null!=(e=e8.get(n))?e:1;if(1===t?e8.delete(n):e8.set(n,t-1),1!==t)return;let r=e7.get(n);r&&(null===r["aria-hidden"]?n.removeAttribute("aria-hidden"):n.setAttribute("aria-hidden",r["aria-hidden"]),n.inert=r.inert,e7.delete(n))}},[e,t])}function te(e,t,r){let n=R(t);(0,P.useEffect)(()=>{function t(e){n.current(e)}return document.addEventListener(e,t,r),()=>document.removeEventListener(e,t,r)},[e,r])}function tt(e,t,r=!0){let n=(0,P.useRef)(!1);function o(r,o){if(!n.current||r.defaultPrevented)return;let i=o(r);if(null!==i&&i.getRootNode().contains(i)&&i.isConnected){for(let t of function e(t){return"function"==typeof t?e(t()):Array.isArray(t)||t instanceof Set?t:[t]}(e)){if(null===t)continue;let e=t instanceof HTMLElement?t:t.current;if(null!=e&&e.contains(i)||r.composed&&r.composedPath().includes(e))return}return eI(i,eD.Loose)||-1===i.tabIndex||r.preventDefault(),t(r,i)}}(0,P.useEffect)(()=>{requestAnimationFrame(()=>{n.current=r})},[r]);let i=(0,P.useRef)(null);te("pointerdown",e=>{var t,r;n.current&&(i.current=(null==(r=null==(t=e.composedPath)?void 0:t.call(e))?void 0:r[0])||e.target)},!0),te("mousedown",e=>{var t,r;n.current&&(i.current=(null==(r=null==(t=e.composedPath)?void 0:t.call(e))?void 0:r[0])||e.target)},!0),te("click",e=>{e3()||/Android/gi.test(window.navigator.userAgent)||i.current&&(o(e,()=>i.current),i.current=null)},!0),te("touchend",e=>o(e,()=>e.target instanceof HTMLElement?e.target:null),!0),ej("blur",e=>o(e,()=>window.document.activeElement instanceof HTMLIFrameElement?window.document.activeElement:null),!0)}let tr=(0,P.createContext)(()=>{});tr.displayName="StackContext";var tn=((y=tn||{})[y.Add=0]="Add",y[y.Remove=1]="Remove",y);function to({children:e,onUpdate:t,type:r,element:n,enabled:o}){let i=(0,P.useContext)(tr),a=L((...e)=>{null==t||t(...e),i(...e)});return I(()=>{let e=void 0===o||!0===o;return e&&a(0,r,n),()=>{e&&a(1,r,n)}},[a,r,n,o]),P.createElement(tr.Provider,{value:a},e)}function ti(e){let t=e.parentElement,r=null;for(;t&&!(t instanceof HTMLFieldSetElement);)t instanceof HTMLLegendElement&&(r=t),t=t.parentElement;let n=(null==t?void 0:t.getAttribute("disabled"))==="";return!(n&&function(e){if(!e)return!1;let t=e.previousElementSibling;for(;null!==t;){if(t instanceof HTMLLegendElement)return!1;t=t.previousElementSibling}return!0}(r))&&n}let ta=(0,P.createContext)(null),tu=Object.assign(er(function(e,t){let r=e6(),{id:n=`headlessui-description-${r}`,...o}=e,i=function e(){let t=(0,P.useContext)(ta);if(null===t){let t=Error("You used a <Description /> component, but it is not inside a relevant parent.");throw Error.captureStackTrace&&Error.captureStackTrace(t,e),t}return t}(),a=z(t);return I(()=>i.register(n),[n,i.register]),Q({ourProps:{ref:a,...i.props,id:n},theirProps:o,slot:i.slot||{},defaultTag:"p",name:i.name||"Description"})}),{});var tc=((v=tc||{}).Space=" ",v.Enter="Enter",v.Escape="Escape",v.Backspace="Backspace",v.Delete="Delete",v.ArrowLeft="ArrowLeft",v.ArrowUp="ArrowUp",v.ArrowRight="ArrowRight",v.ArrowDown="ArrowDown",v.Home="Home",v.End="End",v.PageUp="PageUp",v.PageDown="PageDown",v.Tab="Tab",v),tl=((m=tl||{})[m.Open=0]="Open",m[m.Closed=1]="Closed",m),ts=((g=ts||{})[g.SetTitleId=0]="SetTitleId",g);let tf={0:(e,t)=>e.titleId===t.id?e:{...e,titleId:t.id}},tp=(0,P.createContext)(null);function td(e){let t=(0,P.useContext)(tp);if(null===t){let t=Error(`<${e} /> is missing a parent <Dialog /> component.`);throw Error.captureStackTrace&&Error.captureStackTrace(t,td),t}return t}function th(e,t){return $(t.type,tf,e,t)}tp.displayName="DialogContext";let ty=Y.RenderStrategy|Y.Static,tv=Object.assign(er(function(e,t){let r,n,o,i,a,u=e6(),{id:c=`headlessui-dialog-${u}`,open:l,onClose:s,initialFocus:f,role:p="dialog",__demoMode:d=!1,...h}=e,[y,v]=(0,P.useState)(0),m=(0,P.useRef)(!1);p="dialog"===p||"alertdialog"===p?p:(m.current||(m.current=!0,console.warn(`Invalid role [${p}] passed to <Dialog />. Only \`dialog\` and and \`alertdialog\` are supported. Using \`dialog\` instead.`)),"dialog");let g=X();void 0===l&&null!==g&&(l=(g&V.Open)===V.Open);let b=(0,P.useRef)(null),x=z(b,t),w=eO(b),O=e.hasOwnProperty("open")||null!==g,j=e.hasOwnProperty("onClose");if(!O&&!j)throw Error("You have to provide an `open` and an `onClose` prop to the `Dialog` component.");if(!O)throw Error("You provided an `onClose` prop to the `Dialog`, but forgot an `open` prop.");if(!j)throw Error("You provided an `open` prop to the `Dialog`, but forgot an `onClose` prop.");if("boolean"!=typeof l)throw Error(`You provided an \`open\` prop to the \`Dialog\`, but the value is not a boolean. Received: ${l}`);if("function"!=typeof s)throw Error(`You provided an \`onClose\` prop to the \`Dialog\`, but the value is not a function. Received: ${s}`);let S=l?0:1,[E,k]=(0,P.useReducer)(th,{titleId:null,descriptionId:null,panelRef:(0,P.createRef)()}),A=L(()=>s(!1)),M=L(e=>k({type:0,id:e})),T=!!F()&&!d&&0===S,_=y>1,C=null!==(0,P.useContext)(tp),[N,D]=(r=(0,P.useContext)(eK),n=(0,P.useRef)([]),o=L(e=>(n.current.push(e),r&&r.register(e),()=>i(e))),i=L(e=>{let t=n.current.indexOf(e);-1!==t&&n.current.splice(t,1),r&&r.unregister(e)}),a=(0,P.useMemo)(()=>({register:o,unregister:i,portals:n}),[o,i,n]),[n,(0,P.useMemo)(()=>function({children:e}){return P.createElement(eK.Provider,{value:a},e)},[a])]),{resolveContainers:R,mainTreeNodeRef:B,MainTreeNode:U}=function({defaultContainers:e=[],portals:t,mainTreeNodeRef:r}={}){var n;let o=(0,P.useRef)(null!=(n=null==r?void 0:r.current)?n:null),i=eO(o),a=L(()=>{var r,n,a;let u=[];for(let t of e)null!==t&&(t instanceof HTMLElement?u.push(t):"current"in t&&t.current instanceof HTMLElement&&u.push(t.current));if(null!=t&&t.current)for(let e of t.current)u.push(e);for(let e of null!=(r=null==i?void 0:i.querySelectorAll("html > *, body > *"))?r:[])e!==document.body&&e!==document.head&&e instanceof HTMLElement&&"headlessui-portal-root"!==e.id&&(e.contains(o.current)||e.contains(null==(a=null==(n=o.current)?void 0:n.getRootNode())?void 0:a.host)||u.some(t=>e.contains(t))||u.push(e));return u});return{resolveContainers:a,contains:L(e=>a().some(t=>t.contains(e))),mainTreeNodeRef:o,MainTreeNode:(0,P.useMemo)(()=>function(){return null!=r?null:P.createElement(ek,{features:eE.Hidden,ref:o})},[o,r])}}({portals:N,defaultContainers:[{get current(){var W;return null!=(W=E.panelRef.current)?W:b.current}}]}),q=null!==g&&(g&V.Closing)===V.Closing,H=!C&&!q&&T;e9((0,P.useCallback)(()=>{var e,t;return null!=(t=Array.from(null!=(e=null==w?void 0:w.querySelectorAll("body > *"))?e:[]).find(e=>"headlessui-portal-root"!==e.id&&e.contains(B.current)&&e instanceof HTMLElement))?t:null},[B]),H);let Z=!!_||T;e9((0,P.useCallback)(()=>{var e,t;return null!=(t=Array.from(null!=(e=null==w?void 0:w.querySelectorAll("[data-headlessui-portal]"))?e:[]).find(e=>e.contains(B.current)&&e instanceof HTMLElement))?t:null},[B]),Z),tt(R,e=>{e.preventDefault(),A()},!(!T||_));let G=!(_||0!==S);eb(null==w?void 0:w.defaultView,"keydown",e=>{G&&(e.defaultPrevented||e.key===tc.Escape&&(e.preventDefault(),e.stopPropagation(),A()))}),function(e,t,r=()=>[document.body]){var n;let o,i;n=e=>{var t;return{containers:[...null!=(t=e.containers)?t:[],r]}},o=e5(e4.subscribe,e4.getSnapshot,e4.getSnapshot),(i=e?o.get(e):void 0)&&i.count,I(()=>{if(!(!e||!t))return e4.dispatch("PUSH",e,n),()=>e4.dispatch("POP",e,n)},[t,e])}(w,!(q||0!==S||C),R),(0,P.useEffect)(()=>{if(0!==S||!b.current)return;let e=new ResizeObserver(e=>{for(let t of e){let e=t.target.getBoundingClientRect();0===e.x&&0===e.y&&0===e.width&&0===e.height&&A()}});return e.observe(b.current),()=>e.disconnect()},[S,b,A]);let[Y,K]=function(){let[e,t]=(0,P.useState)([]);return[e.length>0?e.join(" "):void 0,(0,P.useMemo)(()=>function(e){let r=L(e=>(t(t=>[...t,e]),()=>t(t=>{let r=t.slice(),n=r.indexOf(e);return -1!==n&&r.splice(n,1),r}))),n=(0,P.useMemo)(()=>({register:r,slot:e.slot,name:e.name,props:e.props}),[r,e.slot,e.name,e.props]);return P.createElement(ta.Provider,{value:n},e.children)},[t])]}(),J=(0,P.useMemo)(()=>[{dialogState:S,close:A,setTitleId:M},E],[S,E,A,M]),ee=(0,P.useMemo)(()=>({open:0===S}),[S]),et={ref:x,id:c,role:p,"aria-modal":0===S||void 0,"aria-labelledby":E.titleId,"aria-describedby":Y};return P.createElement(to,{type:"Dialog",enabled:0===S,element:b,onUpdate:L((e,t)=>{"Dialog"===t&&$(e,{[tn.Add]:()=>v(e=>e+1),[tn.Remove]:()=>v(e=>e-1)})})},P.createElement(eX,{force:!0},P.createElement(eQ,null,P.createElement(tp.Provider,{value:J},P.createElement(eQ.Group,{target:b},P.createElement(eX,{force:!1},P.createElement(K,{slot:ee,name:"Dialog.Description"},P.createElement(eW,{initialFocus:f,containers:R,features:T?$(_?"parent":"leaf",{parent:eW.features.RestoreFocus,leaf:eW.features.All&~eW.features.FocusLock}):eW.features.None},P.createElement(D,null,Q({ourProps:et,theirProps:h,slot:ee,defaultTag:"div",features:ty,visible:0===S,name:"Dialog"}))))))))),P.createElement(U,null))}),{Backdrop:er(function(e,t){let r=e6(),{id:n=`headlessui-dialog-backdrop-${r}`,...o}=e,[{dialogState:i},a]=td("Dialog.Backdrop"),u=z(t);(0,P.useEffect)(()=>{if(null===a.panelRef.current)throw Error("A <Dialog.Backdrop /> component is being used, but a <Dialog.Panel /> component is missing.")},[a.panelRef]);let c=(0,P.useMemo)(()=>({open:0===i}),[i]);return P.createElement(eX,{force:!0},P.createElement(eQ,null,Q({ourProps:{ref:u,id:n,"aria-hidden":!0},theirProps:o,slot:c,defaultTag:"div",name:"Dialog.Backdrop"})))}),Panel:er(function(e,t){let r=e6(),{id:n=`headlessui-dialog-panel-${r}`,...o}=e,[{dialogState:i},a]=td("Dialog.Panel"),u=z(t,a.panelRef),c=(0,P.useMemo)(()=>({open:0===i}),[i]);return Q({ourProps:{ref:u,id:n,onClick:L(e=>{e.stopPropagation()})},theirProps:o,slot:c,defaultTag:"div",name:"Dialog.Panel"})}),Overlay:er(function(e,t){let r=e6(),{id:n=`headlessui-dialog-overlay-${r}`,...o}=e,[{dialogState:i,close:a}]=td("Dialog.Overlay");return Q({ourProps:{ref:z(t),id:n,"aria-hidden":!0,onClick:L(e=>{if(e.target===e.currentTarget){if(ti(e.currentTarget))return e.preventDefault();e.preventDefault(),e.stopPropagation(),a()}})},theirProps:o,slot:(0,P.useMemo)(()=>({open:0===i}),[i]),defaultTag:"div",name:"Dialog.Overlay"})}),Title:er(function(e,t){let r=e6(),{id:n=`headlessui-dialog-title-${r}`,...o}=e,[{dialogState:i,setTitleId:a}]=td("Dialog.Title"),u=z(t);return(0,P.useEffect)(()=>(a(n),()=>a(null)),[n,a]),Q({ourProps:{ref:u,id:n},theirProps:o,slot:(0,P.useMemo)(()=>({open:0===i}),[i]),defaultTag:"h2",name:"Dialog.Title"})}),Description:tu}),tm=P.forwardRef(function({title:e,titleId:t,...r},n){return P.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:n,"aria-labelledby":t},r),e?P.createElement("title",{id:t},e):null,P.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M6 18 18 6M6 6l12 12"}))}),tg=P.forwardRef(function({title:e,titleId:t,...r},n){return P.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:n,"aria-labelledby":t},r),e?P.createElement("title",{id:t},e):null,P.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"m2.25 12 8.954-8.955c.44-.439 1.152-.439 1.591 0L21.75 12M4.5 9.75v10.125c0 .621.504 1.125 1.125 1.125H9.75v-4.875c0-.621.504-1.125 1.125-1.125h2.25c.621 0 1.125.504 1.125 1.125V21h4.125c.621 0 1.125-.504 1.125-1.125V9.75M8.25 21h8.25"}))});var tb=r(717),tx=r(7607),tw=r(4700);let tO=P.forwardRef(function({title:e,titleId:t,...r},n){return P.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:n,"aria-labelledby":t},r),e?P.createElement("title",{id:t},e):null,P.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M6.75 3v2.25M17.25 3v2.25M3 18.75V7.5a2.25 2.25 0 0 1 2.25-2.25h13.5A2.25 2.25 0 0 1 21 7.5v11.25m-18 0A2.25 2.25 0 0 0 5.25 21h13.5A2.25 2.25 0 0 0 21 18.75m-18 0v-7.5A2.25 2.25 0 0 1 5.25 9h13.5A2.25 2.25 0 0 1 21 11.25v7.5"}))}),tj=P.forwardRef(function({title:e,titleId:t,...r},n){return P.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:n,"aria-labelledby":t},r),e?P.createElement("title",{id:t},e):null,P.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M2.25 8.25h19.5M2.25 9h19.5m-16.5 5.25h6m-6 2.25h3m-3.75 3h15a2.25 2.25 0 0 0 2.25-2.25V6.75A2.25 2.25 0 0 0 19.5 4.5h-15a2.25 2.25 0 0 0-2.25 2.25v10.5A2.25 2.25 0 0 0 4.5 19.5Z"}))}),tS=P.forwardRef(function({title:e,titleId:t,...r},n){return P.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:n,"aria-labelledby":t},r),e?P.createElement("title",{id:t},e):null,P.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M3 13.125C3 12.504 3.504 12 4.125 12h2.25c.621 0 1.125.504 1.125 1.125v6.75C7.5 20.496 6.996 21 6.375 21h-2.25A1.125 1.125 0 0 1 3 19.875v-6.75ZM9.75 8.625c0-.621.504-1.125 1.125-1.125h2.25c.621 0 1.125.504 1.125 1.125v11.25c0 .621-.504 1.125-1.125 1.125h-2.25a1.125 1.125 0 0 1-1.125-1.125V8.625ZM16.5 4.125c0-.621.504-1.125 1.125-1.125h2.25C20.496 3 21 3.504 21 4.125v15.75c0 .621-.504 1.125-1.125 1.125h-2.25a1.125 1.125 0 0 1-1.125-1.125V4.125Z"}))}),tP=P.forwardRef(function({title:e,titleId:t,...r},n){return P.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:n,"aria-labelledby":t},r),e?P.createElement("title",{id:t},e):null,P.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M4.5 12a7.5 7.5 0 0 0 15 0m-15 0a7.5 7.5 0 1 1 15 0m-15 0H3m16.5 0H21m-1.5 0H12m-8.457 3.077 1.41-.513m14.095-5.13 1.41-.513M5.106 17.785l1.15-.964m11.49-9.642 1.149-.964M7.501 19.795l.75-1.3m7.5-12.99.75-1.3m-6.063 16.658.26-1.477m2.605-14.772.26-1.477m0 17.726-.26-1.477M10.698 4.614l-.26-1.477M16.5 19.794l-.75-1.299M7.5 4.205 12 12m6.894 5.785-1.149-.964M6.256 7.178l-1.15-.964m15.352 8.864-1.41-.513M4.954 9.435l-1.41-.514M12.002 12l-3.75 6.495"}))});var tE=r(5548),tk=r.n(tE),tA=r(1018),tM=r(2597);let tT=[{name:"الرئيسية",href:"/dashboard",icon:tg},{name:"المستخدمين",href:"/dashboard/users",icon:tb.Z},{name:"الخبراء",href:"/dashboard/experts",icon:tx.Z},{name:"الخدمات",href:"/dashboard/services",icon:tw.Z},{name:"الحجوزات",href:"/dashboard/bookings",icon:tO},{name:"المدفوعات",href:"/dashboard/payments",icon:tj},{name:"التقارير",href:"/dashboard/analytics",icon:tS},{name:"الإعدادات",href:"/dashboard/settings",icon:tP}];function t_({open:e,setOpen:t}){let r=(0,tA.usePathname)();return(0,S.jsxs)(S.Fragment,{children:[S.jsx(eg.Root,{show:e,as:P.Fragment,children:(0,S.jsxs)(tv,{as:"div",className:"relative z-50 lg:hidden",onClose:t,children:[S.jsx(eg.Child,{as:P.Fragment,enter:"transition-opacity ease-linear duration-300",enterFrom:"opacity-0",enterTo:"opacity-100",leave:"transition-opacity ease-linear duration-300",leaveFrom:"opacity-100",leaveTo:"opacity-0",children:S.jsx("div",{className:"fixed inset-0 bg-gray-900/80"})}),S.jsx("div",{className:"fixed inset-0 flex",children:S.jsx(eg.Child,{as:P.Fragment,enter:"transition ease-in-out duration-300 transform",enterFrom:"-translate-x-full rtl:translate-x-full",enterTo:"translate-x-0",leave:"transition ease-in-out duration-300 transform",leaveFrom:"translate-x-0",leaveTo:"-translate-x-full rtl:translate-x-full",children:(0,S.jsxs)(tv.Panel,{className:"relative mr-16 rtl:mr-0 rtl:ml-16 flex w-full max-w-xs flex-1",children:[S.jsx("div",{className:"absolute left-full rtl:left-auto rtl:right-full top-0 flex w-16 justify-center pt-5",children:(0,S.jsxs)("button",{type:"button",className:"-m-2.5 p-2.5",onClick:()=>t(!1),children:[S.jsx("span",{className:"sr-only",children:"إغلاق الشريط الجانبي"}),S.jsx(tm,{className:"h-6 w-6 text-white","aria-hidden":"true"})]})}),S.jsx(tC,{pathname:r})]})})})]})}),S.jsx("div",{className:"hidden lg:fixed lg:inset-y-0 lg:z-50 lg:flex lg:w-64 lg:flex-col lg:right-0 rtl:lg:right-auto rtl:lg:left-0",children:S.jsx(tC,{pathname:r})})]})}function tC({pathname:e}){return(0,S.jsxs)("div",{className:"flex grow flex-col gap-y-5 overflow-y-auto bg-white dark:bg-gray-900 px-6 pb-4 border-l rtl:border-l-0 rtl:border-r border-gray-200 dark:border-gray-700",children:[S.jsx("div",{className:"flex h-16 shrink-0 items-center",children:S.jsx("h1",{className:"text-xl font-bold text-gray-900 dark:text-white",children:"فريلا سوريا"})}),S.jsx("nav",{className:"flex flex-1 flex-col",children:S.jsx("ul",{role:"list",className:"flex flex-1 flex-col gap-y-7",children:S.jsx("li",{children:S.jsx("ul",{role:"list",className:"-mx-2 space-y-1",children:tT.map(t=>S.jsx("li",{children:(0,S.jsxs)(tk(),{href:t.href,className:(0,tM.W)(e===t.href?"bg-gray-50 dark:bg-gray-800 text-primary-600 dark:text-primary-400":"text-gray-700 dark:text-gray-300 hover:text-primary-600 dark:hover:text-primary-400 hover:bg-gray-50 dark:hover:bg-gray-800","group flex gap-x-3 rounded-md p-2 text-sm leading-6 font-semibold"),children:[S.jsx(t.icon,{className:(0,tM.W)(e===t.href?"text-primary-600 dark:text-primary-400":"text-gray-400 group-hover:text-primary-600 dark:group-hover:text-primary-400","h-6 w-6 shrink-0"),"aria-hidden":"true"}),t.name]})},t.name))})})})})]})}let tN=P.forwardRef(function({title:e,titleId:t,...r},n){return P.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:n,"aria-labelledby":t},r),e?P.createElement("title",{id:t},e):null,P.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M3.75 6.75h16.5M3.75 12h16.5m-16.5 5.25h16.5"}))}),tD=P.forwardRef(function({title:e,titleId:t,...r},n){return P.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:n,"aria-labelledby":t},r),e?P.createElement("title",{id:t},e):null,P.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M12 3v2.25m6.364.386-1.591 1.591M21 12h-2.25m-.386 6.364-1.591-1.591M12 18.75V21m-4.773-4.227-1.591 1.591M5.25 12H3m4.227-4.773L5.636 5.636M15.75 12a3.75 3.75 0 1 1-7.5 0 3.75 3.75 0 0 1 7.5 0Z"}))}),tI=P.forwardRef(function({title:e,titleId:t,...r},n){return P.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:n,"aria-labelledby":t},r),e?P.createElement("title",{id:t},e):null,P.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M21.752 15.002A9.72 9.72 0 0 1 18 15.75c-5.385 0-9.75-4.365-9.75-9.75 0-1.33.266-2.597.748-3.752A9.753 9.753 0 0 0 3 11.25C3 16.635 7.365 21 12.75 21a9.753 9.753 0 0 0 9.002-5.998Z"}))}),tR=P.forwardRef(function({title:e,titleId:t,...r},n){return P.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:n,"aria-labelledby":t},r),e?P.createElement("title",{id:t},e):null,P.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M14.857 17.082a23.848 23.848 0 0 0 5.454-1.31A8.967 8.967 0 0 1 18 9.75V9A6 6 0 0 0 6 9v.75a8.967 8.967 0 0 1-2.312 6.022c1.733.64 3.56 1.085 5.455 1.31m5.714 0a24.255 24.255 0 0 1-5.714 0m5.714 0a3 3 0 1 1-5.714 0"}))});var tL=r(253);function tB(e){var t;if(e.type)return e.type;let r=null!=(t=e.as)?t:"button";if("string"==typeof r&&"button"===r.toLowerCase())return"button"}let tF=/([\u2700-\u27BF]|[\uE000-\uF8FF]|\uD83C[\uDC00-\uDFFF]|\uD83D[\uDC00-\uDFFF]|[\u2011-\u26FF]|\uD83E[\uDD10-\uDDFF])/g;function tU(e){var t,r;let n=null!=(t=e.innerText)?t:"",o=e.cloneNode(!0);if(!(o instanceof HTMLElement))return n;let i=!1;for(let e of o.querySelectorAll('[hidden],[aria-hidden],[role="img"]'))e.remove(),i=!0;let a=i?null!=(r=o.innerText)?r:"":n;return tF.test(a)&&(a=a.replace(tF,"")),a}function tz(e){return[e.screenX,e.screenY]}var t$=((b=t$||{})[b.First=0]="First",b[b.Previous=1]="Previous",b[b.Next=2]="Next",b[b.Last=3]="Last",b[b.Specific=4]="Specific",b[b.Nothing=5]="Nothing",b),tW=((x=tW||{})[x.Open=0]="Open",x[x.Closed=1]="Closed",x),tq=((w=tq||{})[w.Pointer=0]="Pointer",w[w.Other=1]="Other",w),tH=((O=tH||{})[O.OpenMenu=0]="OpenMenu",O[O.CloseMenu=1]="CloseMenu",O[O.GoToItem=2]="GoToItem",O[O.Search=3]="Search",O[O.ClearSearch=4]="ClearSearch",O[O.RegisterItem=5]="RegisterItem",O[O.UnregisterItem=6]="UnregisterItem",O);function tV(e,t=e=>e){let r=null!==e.activeItemIndex?e.items[e.activeItemIndex]:null,n=eF(t(e.items.slice()),e=>e.dataRef.current.domRef.current),o=r?n.indexOf(r):null;return -1===o&&(o=null),{items:n,activeItemIndex:o}}let tX={1:e=>1===e.menuState?e:{...e,activeItemIndex:null,menuState:1},0:e=>0===e.menuState?e:{...e,__demoMode:!1,menuState:0},2:(e,t)=>{var r;let n=tV(e),o=function(e,t){let r=t.resolveItems();if(r.length<=0)return null;let n=t.resolveActiveIndex(),o=null!=n?n:-1;switch(e.focus){case 0:for(let e=0;e<r.length;++e)if(!t.resolveDisabled(r[e],e,r))return e;return n;case 1:for(let e=o-1;e>=0;--e)if(!t.resolveDisabled(r[e],e,r))return e;return n;case 2:for(let e=o+1;e<r.length;++e)if(!t.resolveDisabled(r[e],e,r))return e;return n;case 3:for(let e=r.length-1;e>=0;--e)if(!t.resolveDisabled(r[e],e,r))return e;return n;case 4:for(let n=0;n<r.length;++n)if(t.resolveId(r[n],n,r)===e.id)return n;return n;case 5:return null;default:!function(e){throw Error("Unexpected object: "+e)}(e)}}(t,{resolveItems:()=>n.items,resolveActiveIndex:()=>n.activeItemIndex,resolveId:e=>e.id,resolveDisabled:e=>e.dataRef.current.disabled});return{...e,...n,searchQuery:"",activeItemIndex:o,activationTrigger:null!=(r=t.trigger)?r:1}},3:(e,t)=>{let r=""!==e.searchQuery?0:1,n=e.searchQuery+t.value.toLowerCase(),o=(null!==e.activeItemIndex?e.items.slice(e.activeItemIndex+r).concat(e.items.slice(0,e.activeItemIndex+r)):e.items).find(e=>{var t;return(null==(t=e.dataRef.current.textValue)?void 0:t.startsWith(n))&&!e.dataRef.current.disabled}),i=o?e.items.indexOf(o):-1;return -1===i||i===e.activeItemIndex?{...e,searchQuery:n}:{...e,searchQuery:n,activeItemIndex:i,activationTrigger:1}},4:e=>""===e.searchQuery?e:{...e,searchQuery:"",searchActiveItemIndex:null},5:(e,t)=>{let r=tV(e,e=>[...e,{id:t.id,dataRef:t.dataRef}]);return{...e,...r}},6:(e,t)=>{let r=tV(e,e=>{let r=e.findIndex(e=>e.id===t.id);return -1!==r&&e.splice(r,1),e});return{...e,...r,activationTrigger:1}}},tZ=(0,P.createContext)(null);function tG(e){let t=(0,P.useContext)(tZ);if(null===t){let t=Error(`<${e} /> is missing a parent <Menu /> component.`);throw Error.captureStackTrace&&Error.captureStackTrace(t,tG),t}return t}function tY(e,t){return $(t.type,tX,e,t)}tZ.displayName="MenuContext";let tK=P.Fragment,tQ=Y.RenderStrategy|Y.Static,tJ=P.Fragment,t0=Object.assign(er(function(e,t){let{__demoMode:r=!1,...n}=e,o=(0,P.useReducer)(tY,{__demoMode:r,menuState:r?0:1,buttonRef:(0,P.createRef)(),itemsRef:(0,P.createRef)(),items:[],searchQuery:"",activeItemIndex:null,activationTrigger:1}),[{menuState:i,itemsRef:a,buttonRef:u},c]=o,l=z(t);tt([u,a],(e,t)=>{var r;c({type:1}),eI(t,eD.Loose)||(e.preventDefault(),null==(r=u.current)||r.focus())},0===i);let s=L(()=>{c({type:1})}),f=(0,P.useMemo)(()=>({open:0===i,close:s}),[i,s]);return P.createElement(tZ.Provider,{value:o},P.createElement(Z,{value:$(i,{0:V.Open,1:V.Closed})},Q({ourProps:{ref:l},theirProps:n,slot:f,defaultTag:tK,name:"Menu"})))}),{Button:er(function(e,t){var r;let n=e6(),{id:o=`headlessui-menu-button-${n}`,...i}=e,[a,u]=tG("Menu.Button"),c=z(a.buttonRef,t),l=M(),s=L(e=>{switch(e.key){case tc.Space:case tc.Enter:case tc.ArrowDown:e.preventDefault(),e.stopPropagation(),u({type:0}),l.nextFrame(()=>u({type:2,focus:t$.First}));break;case tc.ArrowUp:e.preventDefault(),e.stopPropagation(),u({type:0}),l.nextFrame(()=>u({type:2,focus:t$.Last}))}}),f=L(e=>{e.key===tc.Space&&e.preventDefault()}),p=L(t=>{if(ti(t.currentTarget))return t.preventDefault();e.disabled||(0===a.menuState?(u({type:1}),l.nextFrame(()=>{var e;return null==(e=a.buttonRef.current)?void 0:e.focus({preventScroll:!0})})):(t.preventDefault(),u({type:0})))}),d=(0,P.useMemo)(()=>({open:0===a.menuState}),[a]);return Q({ourProps:{ref:c,id:o,type:function(e,t){let[r,n]=(0,P.useState)(()=>tB(e));return I(()=>{n(tB(e))},[e.type,e.as]),I(()=>{r||t.current&&t.current instanceof HTMLButtonElement&&!t.current.hasAttribute("type")&&n("button")},[r,t]),r}(e,a.buttonRef),"aria-haspopup":"menu","aria-controls":null==(r=a.itemsRef.current)?void 0:r.id,"aria-expanded":0===a.menuState,onKeyDown:s,onKeyUp:f,onClick:p},theirProps:i,slot:d,defaultTag:"button",name:"Menu.Button"})}),Items:er(function(e,t){var r,n;let o=e6(),{id:i=`headlessui-menu-items-${o}`,...a}=e,[u,c]=tG("Menu.Items"),l=z(u.itemsRef,t),s=eO(u.itemsRef),f=M(),p=X(),d=null!==p?(p&V.Open)===V.Open:0===u.menuState;(0,P.useEffect)(()=>{let e=u.itemsRef.current;e&&0===u.menuState&&e!==(null==s?void 0:s.activeElement)&&e.focus({preventScroll:!0})},[u.menuState,u.itemsRef,s]),function({container:e,accept:t,walk:r,enabled:n=!0}){let o=(0,P.useRef)(t),i=(0,P.useRef)(r);(0,P.useEffect)(()=>{o.current=t,i.current=r},[t,r]),I(()=>{if(!e||!n)return;let t=ew(e);if(!t)return;let r=o.current,a=i.current,u=Object.assign(e=>r(e),{acceptNode:r}),c=t.createTreeWalker(e,NodeFilter.SHOW_ELEMENT,u,!1);for(;c.nextNode();)a(c.currentNode)},[e,n,o,i])}({container:u.itemsRef.current,enabled:0===u.menuState,accept:e=>"menuitem"===e.getAttribute("role")?NodeFilter.FILTER_REJECT:e.hasAttribute("role")?NodeFilter.FILTER_SKIP:NodeFilter.FILTER_ACCEPT,walk(e){e.setAttribute("role","none")}});let h=L(e=>{var t,r;switch(f.dispose(),e.key){case tc.Space:if(""!==u.searchQuery)return e.preventDefault(),e.stopPropagation(),c({type:3,value:e.key});case tc.Enter:if(e.preventDefault(),e.stopPropagation(),c({type:1}),null!==u.activeItemIndex){let{dataRef:e}=u.items[u.activeItemIndex];null==(r=null==(t=e.current)?void 0:t.domRef.current)||r.click()}eR(u.buttonRef.current);break;case tc.ArrowDown:return e.preventDefault(),e.stopPropagation(),c({type:2,focus:t$.Next});case tc.ArrowUp:return e.preventDefault(),e.stopPropagation(),c({type:2,focus:t$.Previous});case tc.Home:case tc.PageUp:return e.preventDefault(),e.stopPropagation(),c({type:2,focus:t$.First});case tc.End:case tc.PageDown:return e.preventDefault(),e.stopPropagation(),c({type:2,focus:t$.Last});case tc.Escape:e.preventDefault(),e.stopPropagation(),c({type:1}),A().nextFrame(()=>{var e;return null==(e=u.buttonRef.current)?void 0:e.focus({preventScroll:!0})});break;case tc.Tab:e.preventDefault(),e.stopPropagation(),c({type:1}),A().nextFrame(()=>{var t,r;t=u.buttonRef.current,r=e.shiftKey?eT.Previous:eT.Next,eU(eN(),r,{relativeTo:t})});break;default:1===e.key.length&&(c({type:3,value:e.key}),f.setTimeout(()=>c({type:4}),350))}}),y=L(e=>{e.key===tc.Space&&e.preventDefault()}),v=(0,P.useMemo)(()=>({open:0===u.menuState}),[u]);return Q({ourProps:{"aria-activedescendant":null===u.activeItemIndex||null==(r=u.items[u.activeItemIndex])?void 0:r.id,"aria-labelledby":null==(n=u.buttonRef.current)?void 0:n.id,id:i,onKeyDown:h,onKeyUp:y,role:"menu",tabIndex:0,ref:l},theirProps:a,slot:v,defaultTag:"div",features:tQ,visible:d,name:"Menu.Items"})}),Item:er(function(e,t){let r,n,o,i=e6(),{id:a=`headlessui-menu-item-${i}`,disabled:u=!1,...c}=e,[l,s]=tG("Menu.Item"),f=null!==l.activeItemIndex&&l.items[l.activeItemIndex].id===a,p=(0,P.useRef)(null),d=z(t,p);I(()=>{if(l.__demoMode||0!==l.menuState||!f||0===l.activationTrigger)return;let e=A();return e.requestAnimationFrame(()=>{var e,t;null==(t=null==(e=p.current)?void 0:e.scrollIntoView)||t.call(e,{block:"nearest"})}),e.dispose},[l.__demoMode,p,f,l.menuState,l.activationTrigger,l.activeItemIndex]);let h=(r=(0,P.useRef)(""),n=(0,P.useRef)(""),L(()=>{let e=p.current;if(!e)return"";let t=e.innerText;if(r.current===t)return n.current;let o=(function(e){let t=e.getAttribute("aria-label");if("string"==typeof t)return t.trim();let r=e.getAttribute("aria-labelledby");if(r){let e=r.split(" ").map(e=>{let t=document.getElementById(e);if(t){let e=t.getAttribute("aria-label");return"string"==typeof e?e.trim():tU(t).trim()}return null}).filter(Boolean);if(e.length>0)return e.join(", ")}return tU(e).trim()})(e).trim().toLowerCase();return r.current=t,n.current=o,o})),y=(0,P.useRef)({disabled:u,domRef:p,get textValue(){return h()}});I(()=>{y.current.disabled=u},[y,u]),I(()=>(s({type:5,id:a,dataRef:y}),()=>s({type:6,id:a})),[y,a]);let v=L(()=>{s({type:1})}),m=L(e=>{if(u)return e.preventDefault();s({type:1}),eR(l.buttonRef.current)}),g=L(()=>{if(u)return s({type:2,focus:t$.Nothing});s({type:2,focus:t$.Specific,id:a})}),b=(o=(0,P.useRef)([-1,-1]),{wasMoved(e){let t=tz(e);return(o.current[0]!==t[0]||o.current[1]!==t[1])&&(o.current=t,!0)},update(e){o.current=tz(e)}}),x=L(e=>b.update(e)),w=L(e=>{b.wasMoved(e)&&(u||f||s({type:2,focus:t$.Specific,id:a,trigger:0}))}),O=L(e=>{b.wasMoved(e)&&(u||f&&s({type:2,focus:t$.Nothing}))}),j=(0,P.useMemo)(()=>({active:f,disabled:u,close:v}),[f,u,v]);return Q({ourProps:{id:a,ref:d,role:"menuitem",tabIndex:!0===u?void 0:-1,"aria-disabled":!0===u||void 0,disabled:void 0,onClick:m,onFocus:g,onPointerEnter:x,onMouseEnter:x,onPointerMove:w,onMouseMove:w,onPointerLeave:O,onMouseLeave:O},theirProps:c,slot:j,defaultTag:tJ,name:"Menu.Item"})})});function t1({onMenuClick:e}){let{theme:t,setTheme:r}=(0,tL.F)();return(0,S.jsxs)("div",{className:"sticky top-0 z-40 flex h-16 shrink-0 items-center gap-x-4 border-b border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-900 px-4 shadow-sm sm:gap-x-6 sm:px-6 lg:px-8",children:[(0,S.jsxs)("button",{type:"button",className:"-m-2.5 p-2.5 text-gray-700 dark:text-gray-300 lg:hidden",onClick:e,children:[S.jsx("span",{className:"sr-only",children:"فتح الشريط الجانبي"}),S.jsx(tN,{className:"h-6 w-6","aria-hidden":"true"})]}),S.jsx("div",{className:"h-6 w-px bg-gray-200 dark:bg-gray-700 lg:hidden","aria-hidden":"true"}),(0,S.jsxs)("div",{className:"flex flex-1 gap-x-4 self-stretch lg:gap-x-6",children:[S.jsx("div",{className:"relative flex flex-1"}),(0,S.jsxs)("div",{className:"flex items-center gap-x-4 lg:gap-x-6",children:[(0,S.jsxs)("button",{type:"button",className:"-m-2.5 p-2.5 text-gray-400 hover:text-gray-500 dark:hover:text-gray-300",onClick:()=>r("dark"===t?"light":"dark"),children:[S.jsx("span",{className:"sr-only",children:"تبديل المظهر"}),"dark"===t?S.jsx(tD,{className:"h-6 w-6","aria-hidden":"true"}):S.jsx(tI,{className:"h-6 w-6","aria-hidden":"true"})]}),(0,S.jsxs)("button",{type:"button",className:"-m-2.5 p-2.5 text-gray-400 hover:text-gray-500 dark:hover:text-gray-300",children:[S.jsx("span",{className:"sr-only",children:"عرض الإشعارات"}),S.jsx(tR,{className:"h-6 w-6","aria-hidden":"true"})]}),S.jsx("div",{className:"hidden lg:block lg:h-6 lg:w-px lg:bg-gray-200 dark:lg:bg-gray-700","aria-hidden":"true"}),(0,S.jsxs)(t0,{as:"div",className:"relative",children:[(0,S.jsxs)(t0.Button,{className:"-m-1.5 flex items-center p-1.5",children:[S.jsx("span",{className:"sr-only",children:"فتح قائمة المستخدم"}),S.jsx("div",{className:"h-8 w-8 rounded-full bg-gray-50 dark:bg-gray-800 flex items-center justify-center",children:S.jsx("span",{className:"text-sm font-medium text-gray-700 dark:text-gray-300",children:"أ"})}),S.jsx("span",{className:"hidden lg:flex lg:items-center",children:S.jsx("span",{className:"mr-4 rtl:mr-0 rtl:ml-4 text-sm font-semibold leading-6 text-gray-900 dark:text-white","aria-hidden":"true",children:"المدير العام"})})]}),S.jsx(eg,{as:P.Fragment,enter:"transition ease-out duration-100",enterFrom:"transform opacity-0 scale-95",enterTo:"transform opacity-100 scale-100",leave:"transition ease-in duration-75",leaveFrom:"transform opacity-100 scale-100",leaveTo:"transform opacity-0 scale-95",children:(0,S.jsxs)(t0.Items,{className:"absolute left-0 rtl:left-auto rtl:right-0 z-10 mt-2.5 w-32 origin-top-right rounded-md bg-white dark:bg-gray-800 py-2 shadow-lg ring-1 ring-gray-900/5 dark:ring-gray-700 focus:outline-none",children:[S.jsx(t0.Item,{children:({active:e})=>S.jsx("a",{href:"#",className:(0,tM.W)(e?"bg-gray-50 dark:bg-gray-700":"","block px-3 py-1 text-sm leading-6 text-gray-900 dark:text-white"),children:"الملف الشخصي"})}),S.jsx(t0.Item,{children:({active:e})=>S.jsx("a",{href:"#",className:(0,tM.W)(e?"bg-gray-50 dark:bg-gray-700":"","block px-3 py-1 text-sm leading-6 text-gray-900 dark:text-white"),children:"تسجيل الخروج"})})]})})]})]})]})]})}function t2({children:e}){let[t,r]=(0,P.useState)(!1);return(0,S.jsxs)("div",{className:"min-h-screen bg-gray-50 dark:bg-gray-900",children:[S.jsx(t_,{open:t,setOpen:r}),(0,S.jsxs)("div",{className:"lg:pr-64 rtl:lg:pr-0 rtl:lg:pl-64",children:[S.jsx(t1,{onMenuClick:()=>r(!0)}),S.jsx("main",{className:"py-6",children:S.jsx("div",{className:"mx-auto max-w-7xl px-4 sm:px-6 lg:px-8",children:e})})]})]})}},4779:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>yr});var n={};r.r(n),r.d(n,{scaleBand:()=>nC,scaleDiverging:()=>function e(){var t=im(uL()(o7));return t.copy=function(){return uD(t,e())},nE.apply(t,arguments)},scaleDivergingLog:()=>function e(){var t=iE(uL()).domain([.1,1,10]);return t.copy=function(){return uD(t,e()).base(t.base())},nE.apply(t,arguments)},scaleDivergingPow:()=>uB,scaleDivergingSqrt:()=>uF,scaleDivergingSymlog:()=>function e(){var t=iM(uL());return t.copy=function(){return uD(t,e()).constant(t.constant())},nE.apply(t,arguments)},scaleIdentity:()=>function e(t){var r;function n(e){return null==e||isNaN(e=+e)?r:e}return n.invert=n,n.domain=n.range=function(e){return arguments.length?(t=Array.from(e,o4),n):t.slice()},n.unknown=function(e){return arguments.length?(r=e,n):r},n.copy=function(){return e(t).unknown(r)},t=arguments.length?Array.from(t,o4):[0,1],im(n)},scaleImplicit:()=>nT,scaleLinear:()=>ig,scaleLog:()=>function e(){let t=iE(ir()).domain([1,10]);return t.copy=()=>it(t,e()).base(t.base()),nP.apply(t,arguments),t},scaleOrdinal:()=>n_,scalePoint:()=>nN,scalePow:()=>iD,scaleQuantile:()=>function e(){var t,r=[],n=[],o=[];function i(){var e=0,t=Math.max(1,n.length);for(o=Array(t-1);++e<t;)o[e-1]=function(e,t,r=oh){if(!(!(n=e.length)||isNaN(t=+t))){if(t<=0||n<2)return+r(e[0],0,e);if(t>=1)return+r(e[n-1],n-1,e);var n,o=(n-1)*t,i=Math.floor(o),a=+r(e[i],i,e);return a+(+r(e[i+1],i+1,e)-a)*(o-i)}}(r,e/t);return a}function a(e){return null==e||isNaN(e=+e)?t:n[ov(o,e)]}return a.invertExtent=function(e){var t=n.indexOf(e);return t<0?[NaN,NaN]:[t>0?o[t-1]:r[0],t<o.length?o[t]:r[r.length-1]]},a.domain=function(e){if(!arguments.length)return r.slice();for(let t of(r=[],e))null==t||isNaN(t=+t)||r.push(t);return r.sort(os),i()},a.range=function(e){return arguments.length?(n=Array.from(e),i()):n.slice()},a.unknown=function(e){return arguments.length?(t=e,a):t},a.quantiles=function(){return o.slice()},a.copy=function(){return e().domain(r).range(n).unknown(t)},nP.apply(a,arguments)},scaleQuantize:()=>function e(){var t,r=0,n=1,o=1,i=[.5],a=[0,1];function u(e){return null!=e&&e<=e?a[ov(i,e,0,o)]:t}function c(){var e=-1;for(i=Array(o);++e<o;)i[e]=((e+1)*n-(e-o)*r)/(o+1);return u}return u.domain=function(e){return arguments.length?([r,n]=e,r=+r,n=+n,c()):[r,n]},u.range=function(e){return arguments.length?(o=(a=Array.from(e)).length-1,c()):a.slice()},u.invertExtent=function(e){var t=a.indexOf(e);return t<0?[NaN,NaN]:t<1?[r,i[0]]:t>=o?[i[o-1],n]:[i[t-1],i[t]]},u.unknown=function(e){return arguments.length&&(t=e),u},u.thresholds=function(){return i.slice()},u.copy=function(){return e().domain([r,n]).range(a).unknown(t)},nP.apply(im(u),arguments)},scaleRadial:()=>function e(){var t,r=io(),n=[0,1],o=!1;function i(e){var n,i=Math.sign(n=r(e))*Math.sqrt(Math.abs(n));return isNaN(i)?t:o?Math.round(i):i}return i.invert=function(e){return r.invert(iR(e))},i.domain=function(e){return arguments.length?(r.domain(e),i):r.domain()},i.range=function(e){return arguments.length?(r.range((n=Array.from(e,o4)).map(iR)),i):n.slice()},i.rangeRound=function(e){return i.range(e).round(!0)},i.round=function(e){return arguments.length?(o=!!e,i):o},i.clamp=function(e){return arguments.length?(r.clamp(e),i):r.clamp()},i.unknown=function(e){return arguments.length?(t=e,i):t},i.copy=function(){return e(r.domain(),n).round(o).clamp(r.clamp()).unknown(t)},nP.apply(i,arguments),im(i)},scaleSequential:()=>function e(){var t=im(uN()(o7));return t.copy=function(){return uD(t,e())},nE.apply(t,arguments)},scaleSequentialLog:()=>function e(){var t=iE(uN()).domain([1,10]);return t.copy=function(){return uD(t,e()).base(t.base())},nE.apply(t,arguments)},scaleSequentialPow:()=>uI,scaleSequentialQuantile:()=>function e(){var t=[],r=o7;function n(e){if(null!=e&&!isNaN(e=+e))return r((ov(t,e,1)-1)/(t.length-1))}return n.domain=function(e){if(!arguments.length)return t.slice();for(let r of(t=[],e))null==r||isNaN(r=+r)||t.push(r);return t.sort(os),n},n.interpolator=function(e){return arguments.length?(r=e,n):r},n.range=function(){return t.map((e,n)=>r(n/(t.length-1)))},n.quantiles=function(e){return Array.from({length:e+1},(r,n)=>(function(e,t,r){if(!(!(n=(e=Float64Array.from(function*(e,t){if(void 0===t)for(let t of e)null!=t&&(t=+t)>=t&&(yield t);else{let r=-1;for(let n of e)null!=(n=t(n,++r,e))&&(n=+n)>=n&&(yield n)}}(e,void 0))).length)||isNaN(t=+t))){if(t<=0||n<2)return iB(e);if(t>=1)return iL(e);var n,o=(n-1)*t,i=Math.floor(o),a=iL((function e(t,r,n=0,o=1/0,i){if(r=Math.floor(r),n=Math.floor(Math.max(0,n)),o=Math.floor(Math.min(t.length-1,o)),!(n<=r&&r<=o))return t;for(i=void 0===i?iF:function(e=os){if(e===os)return iF;if("function"!=typeof e)throw TypeError("compare is not a function");return(t,r)=>{let n=e(t,r);return n||0===n?n:(0===e(r,r))-(0===e(t,t))}}(i);o>n;){if(o-n>600){let a=o-n+1,u=r-n+1,c=Math.log(a),l=.5*Math.exp(2*c/3),s=.5*Math.sqrt(c*l*(a-l)/a)*(u-a/2<0?-1:1),f=Math.max(n,Math.floor(r-u*l/a+s)),p=Math.min(o,Math.floor(r+(a-u)*l/a+s));e(t,r,f,p,i)}let a=t[r],u=n,c=o;for(iU(t,n,r),i(t[o],a)>0&&iU(t,n,o);u<c;){for(iU(t,u,c),++u,--c;0>i(t[u],a);)++u;for(;i(t[c],a)>0;)--c}0===i(t[n],a)?iU(t,n,c):iU(t,++c,o),c<=r&&(n=c+1),r<=c&&(o=c-1)}return t})(e,i).subarray(0,i+1));return a+(iB(e.subarray(i+1))-a)*(o-i)}})(t,n/e))},n.copy=function(){return e(r).domain(t)},nE.apply(n,arguments)},scaleSequentialSqrt:()=>uR,scaleSequentialSymlog:()=>function e(){var t=iM(uN());return t.copy=function(){return uD(t,e()).constant(t.constant())},nE.apply(t,arguments)},scaleSqrt:()=>iI,scaleSymlog:()=>function e(){var t=iM(ir());return t.copy=function(){return it(t,e()).constant(t.constant())},nP.apply(t,arguments)},scaleThreshold:()=>function e(){var t,r=[.5],n=[0,1],o=1;function i(e){return null!=e&&e<=e?n[ov(r,e,0,o)]:t}return i.domain=function(e){return arguments.length?(o=Math.min((r=Array.from(e)).length,n.length-1),i):r.slice()},i.range=function(e){return arguments.length?(n=Array.from(e),o=Math.min(r.length,n.length-1),i):n.slice()},i.invertExtent=function(e){var t=n.indexOf(e);return[r[t-1],r[t]]},i.unknown=function(e){return arguments.length?(t=e,i):t},i.copy=function(){return e().domain(r).range(n).unknown(t)},nP.apply(i,arguments)},scaleTime:()=>u_,scaleUtc:()=>uC,tickFormat:()=>iv});var o=r(3854),i=r(717),a=r(7607),u=r(4700),c=r(4218),l=r.n(c);let s=c.forwardRef(function({title:e,titleId:t,...r},n){return c.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:n,"aria-labelledby":t},r),e?c.createElement("title",{id:t},e):null,c.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M12 6v12m-3-2.818.879.659c1.171.879 3.07.879 4.242 0 1.172-.879 1.172-2.303 0-3.182C13.536 12.219 12.768 12 12 12c-.725 0-1.45-.22-2.003-.659-1.106-.879-1.106-2.303 0-3.182s2.9-.879 4.006 0l.415.33M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z"}))}),f=[{name:"إجمالي المستخدمين",value:"2,847",change:"+12%",changeType:"increase",icon:i.Z},{name:"الخبراء النشطين",value:"1,234",change:"+8%",changeType:"increase",icon:a.Z},{name:"الخدمات المنشورة",value:"5,678",change:"+15%",changeType:"increase",icon:u.Z},{name:"الإيرادات الشهرية",value:"$45,231",change:"+23%",changeType:"increase",icon:s}];function p(){return o.jsx("div",{className:"grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4",children:f.map(e=>(0,o.jsxs)("div",{className:"relative overflow-hidden rounded-lg bg-white dark:bg-gray-800 px-4 py-5 shadow sm:px-6 sm:py-6",children:[(0,o.jsxs)("dt",{children:[o.jsx("div",{className:"absolute rounded-md bg-primary-500 p-3",children:o.jsx(e.icon,{className:"h-6 w-6 text-white","aria-hidden":"true"})}),o.jsx("p",{className:"mr-16 rtl:mr-0 rtl:ml-16 truncate text-sm font-medium text-gray-500 dark:text-gray-400",children:e.name})]}),(0,o.jsxs)("dd",{className:"mr-16 rtl:mr-0 rtl:ml-16 flex items-baseline pb-6 sm:pb-7",children:[o.jsx("p",{className:"text-2xl font-semibold text-gray-900 dark:text-white",children:e.value}),o.jsx("p",{className:`mr-2 rtl:mr-0 rtl:ml-2 flex items-baseline text-sm font-semibold ${"increase"===e.changeType?"text-green-600 dark:text-green-400":"text-red-600 dark:text-red-400"}`,children:e.change}),o.jsx("div",{className:"absolute inset-x-0 bottom-0 bg-gray-50 dark:bg-gray-700 px-4 py-4 sm:px-6",children:o.jsx("div",{className:"text-sm",children:o.jsx("span",{className:"font-medium text-primary-600 dark:text-primary-400",children:"مقارنة بالشهر الماضي"})})})]})]},e.name))})}let d=c.forwardRef(function({title:e,titleId:t,...r},n){return c.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:n,"aria-labelledby":t},r),e?c.createElement("title",{id:t},e):null,c.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M9 12.75 11.25 15 15 9.75M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z"}))}),h=c.forwardRef(function({title:e,titleId:t,...r},n){return c.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:n,"aria-labelledby":t},r),e?c.createElement("title",{id:t},e):null,c.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"m9.75 9.75 4.5 4.5m0-4.5-4.5 4.5M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z"}))}),y=c.forwardRef(function({title:e,titleId:t,...r},n){return c.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:n,"aria-labelledby":t},r),e?c.createElement("title",{id:t},e):null,c.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M12 9v3.75m-9.303 3.376c-.866 1.5.217 3.374 1.948 3.374h14.71c1.73 0 2.813-1.874 1.948-3.374L13.949 3.378c-.866-1.5-3.032-1.5-3.898 0L2.697 16.126ZM12 15.75h.007v.008H12v-.008Z"}))}),v=[{id:1,type:"user_registration",message:"مستخدم جديد سجل في المنصة",user:"أحمد محمد",time:"منذ 5 دقائق",status:"success"},{id:2,type:"service_published",message:"خدمة جديدة تم نشرها",user:"فاطمة أحمد",time:"منذ 15 دقيقة",status:"success"},{id:3,type:"payment_failed",message:"فشل في عملية دفع",user:"محمد علي",time:"منذ 30 دقيقة",status:"error"},{id:4,type:"booking_pending",message:"حجز جديد في انتظار الموافقة",user:"سارة خالد",time:"منذ ساعة",status:"warning"},{id:5,type:"expert_verified",message:"تم التحقق من خبير جديد",user:"عمر حسن",time:"منذ ساعتين",status:"success"}],m={success:d,error:h,warning:y},g={success:"text-green-500",error:"text-red-500",warning:"text-yellow-500"};function b(){return(0,o.jsxs)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow",children:[(0,o.jsxs)("div",{className:"px-6 py-4 border-b border-gray-200 dark:border-gray-700",children:[o.jsx("h3",{className:"text-lg font-medium text-gray-900 dark:text-white",children:"النشاطات الأخيرة"}),o.jsx("p",{className:"text-sm text-gray-500 dark:text-gray-400",children:"آخر الأحداث والنشاطات على المنصة"})]}),o.jsx("div",{className:"divide-y divide-gray-200 dark:divide-gray-700",children:v.map(e=>{let t=m[e.status];return o.jsx("div",{className:"px-6 py-4",children:(0,o.jsxs)("div",{className:"flex items-center space-x-3 rtl:space-x-reverse",children:[o.jsx(t,{className:`h-5 w-5 ${g[e.status]}`}),(0,o.jsxs)("div",{className:"flex-1 min-w-0",children:[o.jsx("p",{className:"text-sm font-medium text-gray-900 dark:text-white",children:e.message}),(0,o.jsxs)("p",{className:"text-sm text-gray-500 dark:text-gray-400",children:["بواسطة ",e.user," • ",e.time]})]})]})},e.id)})}),o.jsx("div",{className:"px-6 py-3 bg-gray-50 dark:bg-gray-700 text-center",children:o.jsx("button",{type:"button",className:"text-sm font-medium text-primary-600 dark:text-primary-400 hover:text-primary-500",children:"عرض جميع النشاطات"})})]})}var x=r(2597),w=r(8892),O=r.n(w),j=r(631),S=r.n(j),P=r(4829),E=r.n(P),k=r(7880),A=r.n(k),M=r(5208),T=r.n(M),_=function(e){return 0===e?0:e>0?1:-1},C=function(e){return S()(e)&&e.indexOf("%")===e.length-1},N=function(e){return T()(e)&&!E()(e)},D=function(e){return N(e)||S()(e)},I=0,R=function(e){var t=++I;return"".concat(e||"").concat(t)},L=function(e,t){var r,n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:0,o=arguments.length>3&&void 0!==arguments[3]&&arguments[3];if(!N(e)&&!S()(e))return n;if(C(e)){var i=e.indexOf("%");r=t*parseFloat(e.slice(0,i))/100}else r=+e;return E()(r)&&(r=n),o&&r>t&&(r=t),r},B=function(e){if(!e)return null;var t=Object.keys(e);return t&&t.length?e[t[0]]:null},F=function(e){if(!Array.isArray(e))return!1;for(var t=e.length,r={},n=0;n<t;n++){if(r[e[n]])return!0;r[e[n]]=!0}return!1},U=function(e,t){return N(e)&&N(t)?function(r){return e+r*(t-e)}:function(){return t}};function z(e,t,r){return e&&e.length?e.find(function(e){return e&&("function"==typeof t?t(e):A()(e,t))===r}):null}var $=function(e,t){return N(e)&&N(t)?e-t:S()(e)&&S()(t)?e.localeCompare(t):e instanceof Date&&t instanceof Date?e.getTime()-t.getTime():String(e).localeCompare(String(t))},W=function(e,t){for(var r=arguments.length,n=Array(r>2?r-2:0),o=2;o<r;o++)n[o-2]=arguments[o]},q=r(1556),H=r.n(q),V=r(1488),X=r.n(V),Z=r(2581),G=r.n(Z),Y=r(5812);function K(e,t){for(var r in e)if(({}).hasOwnProperty.call(e,r)&&(!({}).hasOwnProperty.call(t,r)||e[r]!==t[r]))return!1;for(var n in t)if(({}).hasOwnProperty.call(t,n)&&!({}).hasOwnProperty.call(e,n))return!1;return!0}function Q(e){return(Q="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}var J=["aria-activedescendant","aria-atomic","aria-autocomplete","aria-busy","aria-checked","aria-colcount","aria-colindex","aria-colspan","aria-controls","aria-current","aria-describedby","aria-details","aria-disabled","aria-errormessage","aria-expanded","aria-flowto","aria-haspopup","aria-hidden","aria-invalid","aria-keyshortcuts","aria-label","aria-labelledby","aria-level","aria-live","aria-modal","aria-multiline","aria-multiselectable","aria-orientation","aria-owns","aria-placeholder","aria-posinset","aria-pressed","aria-readonly","aria-relevant","aria-required","aria-roledescription","aria-rowcount","aria-rowindex","aria-rowspan","aria-selected","aria-setsize","aria-sort","aria-valuemax","aria-valuemin","aria-valuenow","aria-valuetext","className","color","height","id","lang","max","media","method","min","name","style","target","width","role","tabIndex","accentHeight","accumulate","additive","alignmentBaseline","allowReorder","alphabetic","amplitude","arabicForm","ascent","attributeName","attributeType","autoReverse","azimuth","baseFrequency","baselineShift","baseProfile","bbox","begin","bias","by","calcMode","capHeight","clip","clipPath","clipPathUnits","clipRule","colorInterpolation","colorInterpolationFilters","colorProfile","colorRendering","contentScriptType","contentStyleType","cursor","cx","cy","d","decelerate","descent","diffuseConstant","direction","display","divisor","dominantBaseline","dur","dx","dy","edgeMode","elevation","enableBackground","end","exponent","externalResourcesRequired","fill","fillOpacity","fillRule","filter","filterRes","filterUnits","floodColor","floodOpacity","focusable","fontFamily","fontSize","fontSizeAdjust","fontStretch","fontStyle","fontVariant","fontWeight","format","from","fx","fy","g1","g2","glyphName","glyphOrientationHorizontal","glyphOrientationVertical","glyphRef","gradientTransform","gradientUnits","hanging","horizAdvX","horizOriginX","href","ideographic","imageRendering","in2","in","intercept","k1","k2","k3","k4","k","kernelMatrix","kernelUnitLength","kerning","keyPoints","keySplines","keyTimes","lengthAdjust","letterSpacing","lightingColor","limitingConeAngle","local","markerEnd","markerHeight","markerMid","markerStart","markerUnits","markerWidth","mask","maskContentUnits","maskUnits","mathematical","mode","numOctaves","offset","opacity","operator","order","orient","orientation","origin","overflow","overlinePosition","overlineThickness","paintOrder","panose1","pathLength","patternContentUnits","patternTransform","patternUnits","pointerEvents","pointsAtX","pointsAtY","pointsAtZ","preserveAlpha","preserveAspectRatio","primitiveUnits","r","radius","refX","refY","renderingIntent","repeatCount","repeatDur","requiredExtensions","requiredFeatures","restart","result","rotate","rx","ry","seed","shapeRendering","slope","spacing","specularConstant","specularExponent","speed","spreadMethod","startOffset","stdDeviation","stemh","stemv","stitchTiles","stopColor","stopOpacity","strikethroughPosition","strikethroughThickness","string","stroke","strokeDasharray","strokeDashoffset","strokeLinecap","strokeLinejoin","strokeMiterlimit","strokeOpacity","strokeWidth","surfaceScale","systemLanguage","tableValues","targetX","targetY","textAnchor","textDecoration","textLength","textRendering","to","transform","u1","u2","underlinePosition","underlineThickness","unicode","unicodeBidi","unicodeRange","unitsPerEm","vAlphabetic","values","vectorEffect","version","vertAdvY","vertOriginX","vertOriginY","vHanging","vIdeographic","viewTarget","visibility","vMathematical","widths","wordSpacing","writingMode","x1","x2","x","xChannelSelector","xHeight","xlinkActuate","xlinkArcrole","xlinkHref","xlinkRole","xlinkShow","xlinkTitle","xlinkType","xmlBase","xmlLang","xmlns","xmlnsXlink","xmlSpace","y1","y2","y","yChannelSelector","z","zoomAndPan","ref","key","angle"],ee=["points","pathLength"],et={svg:["viewBox","children"],polygon:ee,polyline:ee},er=["dangerouslySetInnerHTML","onCopy","onCopyCapture","onCut","onCutCapture","onPaste","onPasteCapture","onCompositionEnd","onCompositionEndCapture","onCompositionStart","onCompositionStartCapture","onCompositionUpdate","onCompositionUpdateCapture","onFocus","onFocusCapture","onBlur","onBlurCapture","onChange","onChangeCapture","onBeforeInput","onBeforeInputCapture","onInput","onInputCapture","onReset","onResetCapture","onSubmit","onSubmitCapture","onInvalid","onInvalidCapture","onLoad","onLoadCapture","onError","onErrorCapture","onKeyDown","onKeyDownCapture","onKeyPress","onKeyPressCapture","onKeyUp","onKeyUpCapture","onAbort","onAbortCapture","onCanPlay","onCanPlayCapture","onCanPlayThrough","onCanPlayThroughCapture","onDurationChange","onDurationChangeCapture","onEmptied","onEmptiedCapture","onEncrypted","onEncryptedCapture","onEnded","onEndedCapture","onLoadedData","onLoadedDataCapture","onLoadedMetadata","onLoadedMetadataCapture","onLoadStart","onLoadStartCapture","onPause","onPauseCapture","onPlay","onPlayCapture","onPlaying","onPlayingCapture","onProgress","onProgressCapture","onRateChange","onRateChangeCapture","onSeeked","onSeekedCapture","onSeeking","onSeekingCapture","onStalled","onStalledCapture","onSuspend","onSuspendCapture","onTimeUpdate","onTimeUpdateCapture","onVolumeChange","onVolumeChangeCapture","onWaiting","onWaitingCapture","onAuxClick","onAuxClickCapture","onClick","onClickCapture","onContextMenu","onContextMenuCapture","onDoubleClick","onDoubleClickCapture","onDrag","onDragCapture","onDragEnd","onDragEndCapture","onDragEnter","onDragEnterCapture","onDragExit","onDragExitCapture","onDragLeave","onDragLeaveCapture","onDragOver","onDragOverCapture","onDragStart","onDragStartCapture","onDrop","onDropCapture","onMouseDown","onMouseDownCapture","onMouseEnter","onMouseLeave","onMouseMove","onMouseMoveCapture","onMouseOut","onMouseOutCapture","onMouseOver","onMouseOverCapture","onMouseUp","onMouseUpCapture","onSelect","onSelectCapture","onTouchCancel","onTouchCancelCapture","onTouchEnd","onTouchEndCapture","onTouchMove","onTouchMoveCapture","onTouchStart","onTouchStartCapture","onPointerDown","onPointerDownCapture","onPointerMove","onPointerMoveCapture","onPointerUp","onPointerUpCapture","onPointerCancel","onPointerCancelCapture","onPointerEnter","onPointerEnterCapture","onPointerLeave","onPointerLeaveCapture","onPointerOver","onPointerOverCapture","onPointerOut","onPointerOutCapture","onGotPointerCapture","onGotPointerCaptureCapture","onLostPointerCapture","onLostPointerCaptureCapture","onScroll","onScrollCapture","onWheel","onWheelCapture","onAnimationStart","onAnimationStartCapture","onAnimationEnd","onAnimationEndCapture","onAnimationIteration","onAnimationIterationCapture","onTransitionEnd","onTransitionEndCapture"],en=function(e,t){if(!e||"function"==typeof e||"boolean"==typeof e)return null;var r=e;if((0,c.isValidElement)(e)&&(r=e.props),!G()(r))return null;var n={};return Object.keys(r).forEach(function(e){er.includes(e)&&(n[e]=t||function(t){return r[e](r,t)})}),n},eo=function(e,t,r){if(!G()(e)||"object"!==Q(e))return null;var n=null;return Object.keys(e).forEach(function(o){var i=e[o];er.includes(o)&&"function"==typeof i&&(n||(n={}),n[o]=function(e){return i(t,r,e),null})}),n},ei=["children"],ea=["children"];function eu(e,t){if(null==e)return{};var r,n,o=function(e,t){if(null==e)return{};var r={};for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){if(t.indexOf(n)>=0)continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(n=0;n<i.length;n++)r=i[n],!(t.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(e,r)&&(o[r]=e[r])}return o}function ec(e){return(ec="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}var el={click:"onClick",mousedown:"onMouseDown",mouseup:"onMouseUp",mouseover:"onMouseOver",mousemove:"onMouseMove",mouseout:"onMouseOut",mouseenter:"onMouseEnter",mouseleave:"onMouseLeave",touchcancel:"onTouchCancel",touchend:"onTouchEnd",touchmove:"onTouchMove",touchstart:"onTouchStart",contextmenu:"onContextMenu",dblclick:"onDoubleClick"},es=function(e){return"string"==typeof e?e:e?e.displayName||e.name||"Component":""},ef=null,ep=null,ed=function e(t){if(t===ef&&Array.isArray(ep))return ep;var r=[];return c.Children.forEach(t,function(t){H()(t)||((0,Y.isFragment)(t)?r=r.concat(e(t.props.children)):r.push(t))}),ep=r,ef=t,r};function eh(e,t){var r=[],n=[];return n=Array.isArray(t)?t.map(function(e){return es(e)}):[es(t)],ed(e).forEach(function(e){var t=A()(e,"type.displayName")||A()(e,"type.name");-1!==n.indexOf(t)&&r.push(e)}),r}function ey(e,t){var r=eh(e,t);return r&&r[0]}var ev=function(e){if(!e||!e.props)return!1;var t=e.props,r=t.width,n=t.height;return!!N(r)&&!(r<=0)&&!!N(n)&&!(n<=0)},em=["a","altGlyph","altGlyphDef","altGlyphItem","animate","animateColor","animateMotion","animateTransform","circle","clipPath","color-profile","cursor","defs","desc","ellipse","feBlend","feColormatrix","feComponentTransfer","feComposite","feConvolveMatrix","feDiffuseLighting","feDisplacementMap","feDistantLight","feFlood","feFuncA","feFuncB","feFuncG","feFuncR","feGaussianBlur","feImage","feMerge","feMergeNode","feMorphology","feOffset","fePointLight","feSpecularLighting","feSpotLight","feTile","feTurbulence","filter","font","font-face","font-face-format","font-face-name","font-face-url","foreignObject","g","glyph","glyphRef","hkern","image","line","lineGradient","marker","mask","metadata","missing-glyph","mpath","path","pattern","polygon","polyline","radialGradient","rect","script","set","stop","style","svg","switch","symbol","text","textPath","title","tref","tspan","use","view","vkern"],eg=function(e,t,r,n){var o,i=null!==(o=null==et?void 0:et[n])&&void 0!==o?o:[];return t.startsWith("data-")||!X()(e)&&(n&&i.includes(t)||J.includes(t))||r&&er.includes(t)},eb=function(e,t,r){if(!e||"function"==typeof e||"boolean"==typeof e)return null;var n=e;if((0,c.isValidElement)(e)&&(n=e.props),!G()(n))return null;var o={};return Object.keys(n).forEach(function(e){var i;eg(null===(i=n)||void 0===i?void 0:i[e],e,t,r)&&(o[e]=n[e])}),o},ex=function e(t,r){if(t===r)return!0;var n=c.Children.count(t);if(n!==c.Children.count(r))return!1;if(0===n)return!0;if(1===n)return ew(Array.isArray(t)?t[0]:t,Array.isArray(r)?r[0]:r);for(var o=0;o<n;o++){var i=t[o],a=r[o];if(Array.isArray(i)||Array.isArray(a)){if(!e(i,a))return!1}else if(!ew(i,a))return!1}return!0},ew=function(e,t){if(H()(e)&&H()(t))return!0;if(!H()(e)&&!H()(t)){var r=e.props||{},n=r.children,o=eu(r,ei),i=t.props||{},a=i.children,u=eu(i,ea);if(n&&a)return K(o,u)&&ex(n,a);if(!n&&!a)return K(o,u)}return!1},eO=function(e,t){var r=[],n={};return ed(e).forEach(function(e,o){if(e&&e.type&&S()(e.type)&&em.indexOf(e.type)>=0)r.push(e);else if(e){var i=es(e.type),a=t[i]||{},u=a.handler,c=a.once;if(u&&(!c||!n[i])){var l=u(e,i,o);r.push(l),n[i]=!0}}}),r},ej=function(e){var t=e&&e.type;return t&&el[t]?el[t]:null};function eS(e){return(eS="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function eP(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function eE(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?eP(Object(r),!0).forEach(function(t){var n,o;n=t,o=r[t],(n=function(e){var t=function(e,t){if("object"!=eS(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=eS(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==eS(t)?t:t+""}(n))in e?Object.defineProperty(e,n,{value:o,enumerable:!0,configurable:!0,writable:!0}):e[n]=o}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):eP(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function ek(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=Array(t);r<t;r++)n[r]=e[r];return n}var eA=(0,c.forwardRef)(function(e,t){var r,n=e.aspect,o=e.initialDimension,i=void 0===o?{width:-1,height:-1}:o,a=e.width,u=void 0===a?"100%":a,s=e.height,f=void 0===s?"100%":s,p=e.minWidth,d=void 0===p?0:p,h=e.minHeight,y=e.maxHeight,v=e.children,m=e.debounce,g=void 0===m?0:m,b=e.id,w=e.className,j=e.onResize,S=e.style,P=(0,c.useRef)(null),E=(0,c.useRef)();E.current=j,(0,c.useImperativeHandle)(t,function(){return Object.defineProperty(P.current,"current",{get:function(){return console.warn("The usage of ref.current.current is deprecated and will no longer be supported."),P.current},configurable:!0})});var k=function(e){if(Array.isArray(e))return e}(r=(0,c.useState)({containerWidth:i.width,containerHeight:i.height}))||function(e,t){var r=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=r){var n,o,i,a,u=[],c=!0,l=!1;try{if(i=(r=r.call(e)).next,0===t){if(Object(r)!==r)return;c=!1}else for(;!(c=(n=i.call(r)).done)&&(u.push(n.value),u.length!==t);c=!0);}catch(e){l=!0,o=e}finally{try{if(!c&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(l)throw o}}return u}}(r,2)||function(e,t){if(e){if("string"==typeof e)return ek(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);if("Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r)return Array.from(e);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return ek(e,t)}}(r,2)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}(),A=k[0],M=k[1],T=(0,c.useCallback)(function(e,t){M(function(r){var n=Math.round(e),o=Math.round(t);return r.containerWidth===n&&r.containerHeight===o?r:{containerWidth:n,containerHeight:o}})},[]);(0,c.useEffect)(function(){var e=function(e){var t,r=e[0].contentRect,n=r.width,o=r.height;T(n,o),null===(t=E.current)||void 0===t||t.call(E,n,o)};g>0&&(e=O()(e,g,{trailing:!0,leading:!1}));var t=new ResizeObserver(e),r=P.current.getBoundingClientRect();return T(r.width,r.height),t.observe(P.current),function(){t.disconnect()}},[T,g]);var _=(0,c.useMemo)(function(){var e=A.containerWidth,t=A.containerHeight;if(e<0||t<0)return null;W(C(u)||C(f),"The width(%s) and height(%s) are both fixed numbers,\n       maybe you don't need to use a ResponsiveContainer.",u,f),W(!n||n>0,"The aspect(%s) must be greater than zero.",n);var r=C(u)?e:u,o=C(f)?t:f;n&&n>0&&(r?o=r/n:o&&(r=o*n),y&&o>y&&(o=y)),W(r>0||o>0,"The width(%s) and height(%s) of chart should be greater than 0,\n       please check the style of container, or the props width(%s) and height(%s),\n       or add a minWidth(%s) or minHeight(%s) or use aspect(%s) to control the\n       height and width.",r,o,u,f,d,h,n);var i=!Array.isArray(v)&&es(v.type).endsWith("Chart");return l().Children.map(v,function(e){return l().isValidElement(e)?(0,c.cloneElement)(e,eE({width:r,height:o},i?{style:eE({height:"100%",width:"100%",maxHeight:o,maxWidth:r},e.props.style)}:{})):e})},[n,v,f,y,h,d,A,u]);return l().createElement("div",{id:b?"".concat(b):void 0,className:(0,x.Z)("recharts-responsive-container",w),style:eE(eE({},void 0===S?{}:S),{},{width:u,height:f,minWidth:d,minHeight:h,maxHeight:y}),ref:P},_)}),eM=r(4579),eT=r.n(eM),e_=r(4560),eC=r.n(e_);function eN(e,t){if(!e)throw Error("Invariant failed")}var eD=["children","width","height","viewBox","className","style","title","desc"];function eI(){return(eI=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(this,arguments)}function eR(e){var t=e.children,r=e.width,n=e.height,o=e.viewBox,i=e.className,a=e.style,u=e.title,c=e.desc,s=function(e,t){if(null==e)return{};var r,n,o=function(e,t){if(null==e)return{};var r={};for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){if(t.indexOf(n)>=0)continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(n=0;n<i.length;n++)r=i[n],!(t.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(e,r)&&(o[r]=e[r])}return o}(e,eD),f=o||{width:r,height:n,x:0,y:0},p=(0,x.Z)("recharts-surface",i);return l().createElement("svg",eI({},eb(s,!0,"svg"),{className:p,width:r,height:n,style:a,viewBox:"".concat(f.x," ").concat(f.y," ").concat(f.width," ").concat(f.height)}),l().createElement("title",null,u),l().createElement("desc",null,c),t)}var eL=["children","className"];function eB(){return(eB=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(this,arguments)}var eF=l().forwardRef(function(e,t){var r=e.children,n=e.className,o=function(e,t){if(null==e)return{};var r,n,o=function(e,t){if(null==e)return{};var r={};for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){if(t.indexOf(n)>=0)continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(n=0;n<i.length;n++)r=i[n],!(t.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(e,r)&&(o[r]=e[r])}return o}(e,eL),i=(0,x.Z)("recharts-layer",n);return l().createElement("g",eB({className:i},eb(o,!0),{ref:t}),r)});function eU(e){return(eU="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function ez(){return(ez=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(this,arguments)}function e$(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=Array(t);r<t;r++)n[r]=e[r];return n}function eW(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function eq(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?eW(Object(r),!0).forEach(function(t){var n,o;n=t,o=r[t],(n=function(e){var t=function(e,t){if("object"!=eU(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=eU(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==eU(t)?t:t+""}(n))in e?Object.defineProperty(e,n,{value:o,enumerable:!0,configurable:!0,writable:!0}):e[n]=o}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):eW(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function eH(e){return Array.isArray(e)&&D(e[0])&&D(e[1])?e.join(" ~ "):e}var eV=function(e){var t=e.separator,r=void 0===t?" : ":t,n=e.contentStyle,o=e.itemStyle,i=void 0===o?{}:o,a=e.labelStyle,u=e.payload,c=e.formatter,s=e.itemSorter,f=e.wrapperClassName,p=e.labelClassName,d=e.label,h=e.labelFormatter,y=e.accessibilityLayer,v=eq({margin:0,padding:10,backgroundColor:"#fff",border:"1px solid #ccc",whiteSpace:"nowrap"},void 0===n?{}:n),m=eq({margin:0},void 0===a?{}:a),g=!H()(d),b=g?d:"",w=(0,x.Z)("recharts-default-tooltip",f),O=(0,x.Z)("recharts-tooltip-label",p);return g&&h&&null!=u&&(b=h(d,u)),l().createElement("div",ez({className:w,style:v},void 0!==y&&y?{role:"status","aria-live":"assertive"}:{}),l().createElement("p",{className:O,style:m},l().isValidElement(b)?b:"".concat(b)),function(){if(u&&u.length){var e=(s?eC()(u,s):u).map(function(e,t){if("none"===e.type)return null;var n=eq({display:"block",paddingTop:4,paddingBottom:4,color:e.color||"#000"},i),o=e.formatter||c||eH,a=e.value,s=e.name,f=a,p=s;if(o&&null!=f&&null!=p){var d=o(a,s,e,t,u);if(Array.isArray(d)){var h=function(e){if(Array.isArray(e))return e}(d)||function(e,t){var r=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=r){var n,o,i,a,u=[],c=!0,l=!1;try{if(i=(r=r.call(e)).next,0===t){if(Object(r)!==r)return;c=!1}else for(;!(c=(n=i.call(r)).done)&&(u.push(n.value),u.length!==t);c=!0);}catch(e){l=!0,o=e}finally{try{if(!c&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(l)throw o}}return u}}(d,2)||function(e,t){if(e){if("string"==typeof e)return e$(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);if("Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r)return Array.from(e);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return e$(e,t)}}(d,2)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}();f=h[0],p=h[1]}else f=d}return l().createElement("li",{className:"recharts-tooltip-item",key:"tooltip-item-".concat(t),style:n},D(p)?l().createElement("span",{className:"recharts-tooltip-item-name"},p):null,D(p)?l().createElement("span",{className:"recharts-tooltip-item-separator"},r):null,l().createElement("span",{className:"recharts-tooltip-item-value"},f),l().createElement("span",{className:"recharts-tooltip-item-unit"},e.unit||""))});return l().createElement("ul",{className:"recharts-tooltip-item-list",style:{padding:0,margin:0}},e)}return null}())};function eX(e){return(eX="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function eZ(e,t,r){var n;return(n=function(e,t){if("object"!=eX(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=eX(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(t,"string"),(t="symbol"==eX(n)?n:n+"")in e)?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}var eG="recharts-tooltip-wrapper",eY={visibility:"hidden"};function eK(e){var t=e.allowEscapeViewBox,r=e.coordinate,n=e.key,o=e.offsetTopLeft,i=e.position,a=e.reverseDirection,u=e.tooltipDimension,c=e.viewBox,l=e.viewBoxDimension;if(i&&N(i[n]))return i[n];var s=r[n]-u-o,f=r[n]+o;return t[n]?a[n]?s:f:a[n]?s<c[n]?Math.max(f,c[n]):Math.max(s,c[n]):f+u>c[n]+l?Math.max(s,c[n]):Math.max(f,c[n])}function eQ(e){return(eQ="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function eJ(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function e0(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?eJ(Object(r),!0).forEach(function(t){e4(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):eJ(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function e1(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,e6(n.key),n)}}function e2(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(e){}return(e2=function(){return!!e})()}function e5(e){return(e5=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)})(e)}function e3(e,t){return(e3=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e})(e,t)}function e4(e,t,r){return(t=e6(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function e6(e){var t=function(e,t){if("object"!=eQ(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=eQ(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==eQ(t)?t:t+""}var e7=function(e){var t,r;function n(){!function(e,t){if(!(e instanceof t))throw TypeError("Cannot call a class as a function")}(this,n);for(var e,t,r,o=arguments.length,i=Array(o),a=0;a<o;a++)i[a]=arguments[a];return t=n,r=[].concat(i),t=e5(t),e4(e=function(e,t){if(t&&("object"===eQ(t)||"function"==typeof t))return t;if(void 0!==t)throw TypeError("Derived constructors may only return object or undefined");return function(e){if(void 0===e)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return e}(e)}(this,e2()?Reflect.construct(t,r||[],e5(this).constructor):t.apply(this,r)),"state",{dismissed:!1,dismissedAtCoordinate:{x:0,y:0},lastBoundingBox:{width:-1,height:-1}}),e4(e,"handleKeyDown",function(t){if("Escape"===t.key){var r,n,o,i;e.setState({dismissed:!0,dismissedAtCoordinate:{x:null!==(r=null===(n=e.props.coordinate)||void 0===n?void 0:n.x)&&void 0!==r?r:0,y:null!==(o=null===(i=e.props.coordinate)||void 0===i?void 0:i.y)&&void 0!==o?o:0}})}}),e}return function(e,t){if("function"!=typeof t&&null!==t)throw TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&e3(e,t)}(n,e),t=[{key:"updateBBox",value:function(){if(this.wrapperNode&&this.wrapperNode.getBoundingClientRect){var e=this.wrapperNode.getBoundingClientRect();(Math.abs(e.width-this.state.lastBoundingBox.width)>1||Math.abs(e.height-this.state.lastBoundingBox.height)>1)&&this.setState({lastBoundingBox:{width:e.width,height:e.height}})}else(-1!==this.state.lastBoundingBox.width||-1!==this.state.lastBoundingBox.height)&&this.setState({lastBoundingBox:{width:-1,height:-1}})}},{key:"componentDidMount",value:function(){document.addEventListener("keydown",this.handleKeyDown),this.updateBBox()}},{key:"componentWillUnmount",value:function(){document.removeEventListener("keydown",this.handleKeyDown)}},{key:"componentDidUpdate",value:function(){var e,t;this.props.active&&this.updateBBox(),this.state.dismissed&&((null===(e=this.props.coordinate)||void 0===e?void 0:e.x)!==this.state.dismissedAtCoordinate.x||(null===(t=this.props.coordinate)||void 0===t?void 0:t.y)!==this.state.dismissedAtCoordinate.y)&&(this.state.dismissed=!1)}},{key:"render",value:function(){var e,t,r,n,o,i,a,u,c,s,f,p,d,h,y,v,m,g,b,w=this,O=this.props,j=O.active,S=O.allowEscapeViewBox,P=O.animationDuration,E=O.animationEasing,k=O.children,A=O.coordinate,M=O.hasPayload,T=O.isAnimationActive,_=O.offset,C=O.position,D=O.reverseDirection,I=O.useTranslate3d,R=O.viewBox,L=O.wrapperStyle,B=(p=(e={allowEscapeViewBox:S,coordinate:A,offsetTopLeft:_,position:C,reverseDirection:D,tooltipBox:this.state.lastBoundingBox,useTranslate3d:I,viewBox:R}).allowEscapeViewBox,d=e.coordinate,h=e.offsetTopLeft,y=e.position,v=e.reverseDirection,m=e.tooltipBox,g=e.useTranslate3d,b=e.viewBox,m.height>0&&m.width>0&&d?(r=(t={translateX:s=eK({allowEscapeViewBox:p,coordinate:d,key:"x",offsetTopLeft:h,position:y,reverseDirection:v,tooltipDimension:m.width,viewBox:b,viewBoxDimension:b.width}),translateY:f=eK({allowEscapeViewBox:p,coordinate:d,key:"y",offsetTopLeft:h,position:y,reverseDirection:v,tooltipDimension:m.height,viewBox:b,viewBoxDimension:b.height}),useTranslate3d:g}).translateX,n=t.translateY,c={transform:t.useTranslate3d?"translate3d(".concat(r,"px, ").concat(n,"px, 0)"):"translate(".concat(r,"px, ").concat(n,"px)")}):c=eY,{cssProperties:c,cssClasses:(i=(o={translateX:s,translateY:f,coordinate:d}).coordinate,a=o.translateX,u=o.translateY,(0,x.Z)(eG,eZ(eZ(eZ(eZ({},"".concat(eG,"-right"),N(a)&&i&&N(i.x)&&a>=i.x),"".concat(eG,"-left"),N(a)&&i&&N(i.x)&&a<i.x),"".concat(eG,"-bottom"),N(u)&&i&&N(i.y)&&u>=i.y),"".concat(eG,"-top"),N(u)&&i&&N(i.y)&&u<i.y)))}),F=B.cssClasses,U=B.cssProperties,z=e0(e0({transition:T&&j?"transform ".concat(P,"ms ").concat(E):void 0},U),{},{pointerEvents:"none",visibility:!this.state.dismissed&&j&&M?"visible":"hidden",position:"absolute",top:0,left:0},L);return l().createElement("div",{tabIndex:-1,className:F,style:z,ref:function(e){w.wrapperNode=e}},k)}}],e1(n.prototype,t),r&&e1(n,r),Object.defineProperty(n,"prototype",{writable:!1}),n}(c.PureComponent),e8={isSsr:!0,get:function(e){return e8[e]},set:function(e,t){if("string"==typeof e)e8[e]=t;else{var r=Object.keys(e);r&&r.length&&r.forEach(function(t){e8[t]=e[t]})}}},e9=r(2873),te=r.n(e9);function tt(e,t,r){return!0===t?te()(e,r):X()(t)?te()(e,t):e}function tr(e){return(tr="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function tn(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function to(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?tn(Object(r),!0).forEach(function(t){tl(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):tn(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function ti(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,ts(n.key),n)}}function ta(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(e){}return(ta=function(){return!!e})()}function tu(e){return(tu=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)})(e)}function tc(e,t){return(tc=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e})(e,t)}function tl(e,t,r){return(t=ts(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function ts(e){var t=function(e,t){if("object"!=tr(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=tr(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==tr(t)?t:t+""}function tf(e){return e.dataKey}var tp=function(e){var t,r;function n(){var e,t;return function(e,t){if(!(e instanceof t))throw TypeError("Cannot call a class as a function")}(this,n),e=n,t=arguments,e=tu(e),function(e,t){if(t&&("object"===tr(t)||"function"==typeof t))return t;if(void 0!==t)throw TypeError("Derived constructors may only return object or undefined");return function(e){if(void 0===e)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return e}(e)}(this,ta()?Reflect.construct(e,t||[],tu(this).constructor):e.apply(this,t))}return function(e,t){if("function"!=typeof t&&null!==t)throw TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&tc(e,t)}(n,e),t=[{key:"render",value:function(){var e,t=this,r=this.props,n=r.active,o=r.allowEscapeViewBox,i=r.animationDuration,a=r.animationEasing,u=r.content,c=r.coordinate,s=r.filterNull,f=r.isAnimationActive,p=r.offset,d=r.payload,h=r.payloadUniqBy,y=r.position,v=r.reverseDirection,m=r.useTranslate3d,g=r.viewBox,b=r.wrapperStyle,x=null!=d?d:[];s&&x.length&&(x=tt(d.filter(function(e){return null!=e.value&&(!0!==e.hide||t.props.includeHidden)}),h,tf));var w=x.length>0;return l().createElement(e7,{allowEscapeViewBox:o,animationDuration:i,animationEasing:a,isAnimationActive:f,active:n,coordinate:c,hasPayload:w,offset:p,position:y,reverseDirection:v,useTranslate3d:m,viewBox:g,wrapperStyle:b},(e=to(to({},this.props),{},{payload:x}),l().isValidElement(u)?l().cloneElement(u,e):"function"==typeof u?l().createElement(u,e):l().createElement(eV,e)))}}],ti(n.prototype,t),r&&ti(n,r),Object.defineProperty(n,"prototype",{writable:!1}),n}(c.PureComponent);tl(tp,"displayName","Tooltip"),tl(tp,"defaultProps",{accessibilityLayer:!1,allowEscapeViewBox:{x:!1,y:!1},animationDuration:400,animationEasing:"ease",contentStyle:{},coordinate:{x:0,y:0},cursor:!0,cursorStyle:{},filterNull:!0,isAnimationActive:!e8.isSsr,itemStyle:{},labelStyle:{},offset:10,reverseDirection:{x:!1,y:!1},separator:" : ",trigger:"hover",useTranslate3d:!1,viewBox:{x:0,y:0,height:0,width:0},wrapperStyle:{}});var td=r(1801),th=r.n(td);let ty=Math.cos,tv=Math.sin,tm=Math.sqrt,tg=Math.PI,tb=2*tg,tx={draw(e,t){let r=tm(t/tg);e.moveTo(r,0),e.arc(0,0,r,0,tb)}},tw=tm(1/3),tO=2*tw,tj=tv(tg/10)/tv(7*tg/10),tS=tv(tb/10)*tj,tP=-ty(tb/10)*tj,tE=tm(3),tk=tm(3)/2,tA=1/tm(12),tM=(tA/2+1)*3;function tT(e){return function(){return e}}let t_=Math.PI,tC=2*t_,tN=tC-1e-6;function tD(e){this._+=e[0];for(let t=1,r=e.length;t<r;++t)this._+=arguments[t]+e[t]}class tI{constructor(e){this._x0=this._y0=this._x1=this._y1=null,this._="",this._append=null==e?tD:function(e){let t=Math.floor(e);if(!(t>=0))throw Error(`invalid digits: ${e}`);if(t>15)return tD;let r=10**t;return function(e){this._+=e[0];for(let t=1,n=e.length;t<n;++t)this._+=Math.round(arguments[t]*r)/r+e[t]}}(e)}moveTo(e,t){this._append`M${this._x0=this._x1=+e},${this._y0=this._y1=+t}`}closePath(){null!==this._x1&&(this._x1=this._x0,this._y1=this._y0,this._append`Z`)}lineTo(e,t){this._append`L${this._x1=+e},${this._y1=+t}`}quadraticCurveTo(e,t,r,n){this._append`Q${+e},${+t},${this._x1=+r},${this._y1=+n}`}bezierCurveTo(e,t,r,n,o,i){this._append`C${+e},${+t},${+r},${+n},${this._x1=+o},${this._y1=+i}`}arcTo(e,t,r,n,o){if(e=+e,t=+t,r=+r,n=+n,(o=+o)<0)throw Error(`negative radius: ${o}`);let i=this._x1,a=this._y1,u=r-e,c=n-t,l=i-e,s=a-t,f=l*l+s*s;if(null===this._x1)this._append`M${this._x1=e},${this._y1=t}`;else if(f>1e-6){if(Math.abs(s*u-c*l)>1e-6&&o){let p=r-i,d=n-a,h=u*u+c*c,y=Math.sqrt(h),v=Math.sqrt(f),m=o*Math.tan((t_-Math.acos((h+f-(p*p+d*d))/(2*y*v)))/2),g=m/v,b=m/y;Math.abs(g-1)>1e-6&&this._append`L${e+g*l},${t+g*s}`,this._append`A${o},${o},0,0,${+(s*p>l*d)},${this._x1=e+b*u},${this._y1=t+b*c}`}else this._append`L${this._x1=e},${this._y1=t}`}}arc(e,t,r,n,o,i){if(e=+e,t=+t,i=!!i,(r=+r)<0)throw Error(`negative radius: ${r}`);let a=r*Math.cos(n),u=r*Math.sin(n),c=e+a,l=t+u,s=1^i,f=i?n-o:o-n;null===this._x1?this._append`M${c},${l}`:(Math.abs(this._x1-c)>1e-6||Math.abs(this._y1-l)>1e-6)&&this._append`L${c},${l}`,r&&(f<0&&(f=f%tC+tC),f>tN?this._append`A${r},${r},0,1,${s},${e-a},${t-u}A${r},${r},0,1,${s},${this._x1=c},${this._y1=l}`:f>1e-6&&this._append`A${r},${r},0,${+(f>=t_)},${s},${this._x1=e+r*Math.cos(o)},${this._y1=t+r*Math.sin(o)}`)}rect(e,t,r,n){this._append`M${this._x0=this._x1=+e},${this._y0=this._y1=+t}h${r=+r}v${+n}h${-r}Z`}toString(){return this._}}function tR(e){let t=3;return e.digits=function(r){if(!arguments.length)return t;if(null==r)t=null;else{let e=Math.floor(r);if(!(e>=0))throw RangeError(`invalid digits: ${r}`);t=e}return e},()=>new tI(t)}function tL(e){return(tL="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}tI.prototype,tm(3),tm(3);var tB=["type","size","sizeType"];function tF(){return(tF=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(this,arguments)}function tU(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function tz(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?tU(Object(r),!0).forEach(function(t){var n,o;n=t,o=r[t],(n=function(e){var t=function(e,t){if("object"!=tL(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=tL(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==tL(t)?t:t+""}(n))in e?Object.defineProperty(e,n,{value:o,enumerable:!0,configurable:!0,writable:!0}):e[n]=o}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):tU(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}var t$={symbolCircle:tx,symbolCross:{draw(e,t){let r=tm(t/5)/2;e.moveTo(-3*r,-r),e.lineTo(-r,-r),e.lineTo(-r,-3*r),e.lineTo(r,-3*r),e.lineTo(r,-r),e.lineTo(3*r,-r),e.lineTo(3*r,r),e.lineTo(r,r),e.lineTo(r,3*r),e.lineTo(-r,3*r),e.lineTo(-r,r),e.lineTo(-3*r,r),e.closePath()}},symbolDiamond:{draw(e,t){let r=tm(t/tO),n=r*tw;e.moveTo(0,-r),e.lineTo(n,0),e.lineTo(0,r),e.lineTo(-n,0),e.closePath()}},symbolSquare:{draw(e,t){let r=tm(t),n=-r/2;e.rect(n,n,r,r)}},symbolStar:{draw(e,t){let r=tm(.8908130915292852*t),n=tS*r,o=tP*r;e.moveTo(0,-r),e.lineTo(n,o);for(let t=1;t<5;++t){let i=tb*t/5,a=ty(i),u=tv(i);e.lineTo(u*r,-a*r),e.lineTo(a*n-u*o,u*n+a*o)}e.closePath()}},symbolTriangle:{draw(e,t){let r=-tm(t/(3*tE));e.moveTo(0,2*r),e.lineTo(-tE*r,-r),e.lineTo(tE*r,-r),e.closePath()}},symbolWye:{draw(e,t){let r=tm(t/tM),n=r/2,o=r*tA,i=r*tA+r,a=-n;e.moveTo(n,o),e.lineTo(n,i),e.lineTo(a,i),e.lineTo(-.5*n-tk*o,tk*n+-.5*o),e.lineTo(-.5*n-tk*i,tk*n+-.5*i),e.lineTo(-.5*a-tk*i,tk*a+-.5*i),e.lineTo(-.5*n+tk*o,-.5*o-tk*n),e.lineTo(-.5*n+tk*i,-.5*i-tk*n),e.lineTo(-.5*a+tk*i,-.5*i-tk*a),e.closePath()}}},tW=Math.PI/180,tq=function(e,t,r){if("area"===t)return e;switch(r){case"cross":return 5*e*e/9;case"diamond":return .5*e*e/Math.sqrt(3);case"square":return e*e;case"star":var n=18*tW;return 1.25*e*e*(Math.tan(n)-Math.tan(2*n)*Math.pow(Math.tan(n),2));case"triangle":return Math.sqrt(3)*e*e/4;case"wye":return(21-10*Math.sqrt(3))*e*e/8;default:return Math.PI*e*e/4}},tH=function(e){var t,r=e.type,n=void 0===r?"circle":r,o=e.size,i=void 0===o?64:o,a=e.sizeType,u=void 0===a?"area":a,c=tz(tz({},function(e,t){if(null==e)return{};var r,n,o=function(e,t){if(null==e)return{};var r={};for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){if(t.indexOf(n)>=0)continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(n=0;n<i.length;n++)r=i[n],!(t.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(e,r)&&(o[r]=e[r])}return o}(e,tB)),{},{type:n,size:i,sizeType:u}),s=c.className,f=c.cx,p=c.cy,d=eb(c,!0);return f===+f&&p===+p&&i===+i?l().createElement("path",tF({},d,{className:(0,x.Z)("recharts-symbols",s),transform:"translate(".concat(f,", ").concat(p,")"),d:(t=t$["symbol".concat(th()(n))]||tx,(function(e,t){let r=null,n=tR(o);function o(){let o;if(r||(r=o=n()),e.apply(this,arguments).draw(r,+t.apply(this,arguments)),o)return r=null,o+""||null}return e="function"==typeof e?e:tT(e||tx),t="function"==typeof t?t:tT(void 0===t?64:+t),o.type=function(t){return arguments.length?(e="function"==typeof t?t:tT(t),o):e},o.size=function(e){return arguments.length?(t="function"==typeof e?e:tT(+e),o):t},o.context=function(e){return arguments.length?(r=null==e?null:e,o):r},o})().type(t).size(tq(i,u,n))())})):null};function tV(e){return(tV="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function tX(){return(tX=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(this,arguments)}function tZ(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function tG(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,t0(n.key),n)}}function tY(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(e){}return(tY=function(){return!!e})()}function tK(e){return(tK=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)})(e)}function tQ(e,t){return(tQ=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e})(e,t)}function tJ(e,t,r){return(t=t0(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function t0(e){var t=function(e,t){if("object"!=tV(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=tV(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==tV(t)?t:t+""}tH.registerSymbol=function(e,t){t$["symbol".concat(th()(e))]=t};var t1=function(e){var t,r;function n(){var e,t;return function(e,t){if(!(e instanceof t))throw TypeError("Cannot call a class as a function")}(this,n),e=n,t=arguments,e=tK(e),function(e,t){if(t&&("object"===tV(t)||"function"==typeof t))return t;if(void 0!==t)throw TypeError("Derived constructors may only return object or undefined");return function(e){if(void 0===e)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return e}(e)}(this,tY()?Reflect.construct(e,t||[],tK(this).constructor):e.apply(this,t))}return function(e,t){if("function"!=typeof t&&null!==t)throw TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&tQ(e,t)}(n,e),t=[{key:"renderIcon",value:function(e){var t=this.props.inactiveColor,r=32/6,n=32/3,o=e.inactive?t:e.color;if("plainline"===e.type)return l().createElement("line",{strokeWidth:4,fill:"none",stroke:o,strokeDasharray:e.payload.strokeDasharray,x1:0,y1:16,x2:32,y2:16,className:"recharts-legend-icon"});if("line"===e.type)return l().createElement("path",{strokeWidth:4,fill:"none",stroke:o,d:"M0,".concat(16,"h").concat(n,"\n            A").concat(r,",").concat(r,",0,1,1,").concat(2*n,",").concat(16,"\n            H").concat(32,"M").concat(2*n,",").concat(16,"\n            A").concat(r,",").concat(r,",0,1,1,").concat(n,",").concat(16),className:"recharts-legend-icon"});if("rect"===e.type)return l().createElement("path",{stroke:"none",fill:o,d:"M0,".concat(4,"h").concat(32,"v").concat(24,"h").concat(-32,"z"),className:"recharts-legend-icon"});if(l().isValidElement(e.legendIcon)){var i=function(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?tZ(Object(r),!0).forEach(function(t){tJ(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):tZ(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}({},e);return delete i.legendIcon,l().cloneElement(e.legendIcon,i)}return l().createElement(tH,{fill:o,cx:16,cy:16,size:32,sizeType:"diameter",type:e.type})}},{key:"renderItems",value:function(){var e=this,t=this.props,r=t.payload,n=t.iconSize,o=t.layout,i=t.formatter,a=t.inactiveColor,u={x:0,y:0,width:32,height:32},c={display:"horizontal"===o?"inline-block":"block",marginRight:10},s={display:"inline-block",verticalAlign:"middle",marginRight:4};return r.map(function(t,r){var o=t.formatter||i,f=(0,x.Z)(tJ(tJ({"recharts-legend-item":!0},"legend-item-".concat(r),!0),"inactive",t.inactive));if("none"===t.type)return null;var p=X()(t.value)?null:t.value;W(!X()(t.value),'The name property is also required when using a function for the dataKey of a chart\'s cartesian components. Ex: <Bar name="Name of my Data"/>');var d=t.inactive?a:t.color;return l().createElement("li",tX({className:f,style:c,key:"legend-item-".concat(r)},eo(e.props,t,r)),l().createElement(eR,{width:n,height:n,viewBox:u,style:s},e.renderIcon(t)),l().createElement("span",{className:"recharts-legend-item-text",style:{color:d}},o?o(p,t,r):p))})}},{key:"render",value:function(){var e=this.props,t=e.payload,r=e.layout,n=e.align;return t&&t.length?l().createElement("ul",{className:"recharts-default-legend",style:{padding:0,margin:0,textAlign:"horizontal"===r?n:"left"}},this.renderItems()):null}}],tG(n.prototype,t),r&&tG(n,r),Object.defineProperty(n,"prototype",{writable:!1}),n}(c.PureComponent);function t2(e){return(t2="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}tJ(t1,"displayName","Legend"),tJ(t1,"defaultProps",{iconSize:14,layout:"horizontal",align:"center",verticalAlign:"middle",inactiveColor:"#ccc"});var t5=["ref"];function t3(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function t4(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?t3(Object(r),!0).forEach(function(t){re(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):t3(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function t6(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,rt(n.key),n)}}function t7(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(e){}return(t7=function(){return!!e})()}function t8(e){return(t8=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)})(e)}function t9(e,t){return(t9=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e})(e,t)}function re(e,t,r){return(t=rt(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function rt(e){var t=function(e,t){if("object"!=t2(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=t2(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==t2(t)?t:t+""}function rr(e){return e.value}var rn=function(e){var t,r;function n(){!function(e,t){if(!(e instanceof t))throw TypeError("Cannot call a class as a function")}(this,n);for(var e,t,r,o=arguments.length,i=Array(o),a=0;a<o;a++)i[a]=arguments[a];return t=n,r=[].concat(i),t=t8(t),re(e=function(e,t){if(t&&("object"===t2(t)||"function"==typeof t))return t;if(void 0!==t)throw TypeError("Derived constructors may only return object or undefined");return function(e){if(void 0===e)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return e}(e)}(this,t7()?Reflect.construct(t,r||[],t8(this).constructor):t.apply(this,r)),"lastBoundingBox",{width:-1,height:-1}),e}return function(e,t){if("function"!=typeof t&&null!==t)throw TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&t9(e,t)}(n,e),t=[{key:"componentDidMount",value:function(){this.updateBBox()}},{key:"componentDidUpdate",value:function(){this.updateBBox()}},{key:"getBBox",value:function(){if(this.wrapperNode&&this.wrapperNode.getBoundingClientRect){var e=this.wrapperNode.getBoundingClientRect();return e.height=this.wrapperNode.offsetHeight,e.width=this.wrapperNode.offsetWidth,e}return null}},{key:"updateBBox",value:function(){var e=this.props.onBBoxUpdate,t=this.getBBox();t?(Math.abs(t.width-this.lastBoundingBox.width)>1||Math.abs(t.height-this.lastBoundingBox.height)>1)&&(this.lastBoundingBox.width=t.width,this.lastBoundingBox.height=t.height,e&&e(t)):(-1!==this.lastBoundingBox.width||-1!==this.lastBoundingBox.height)&&(this.lastBoundingBox.width=-1,this.lastBoundingBox.height=-1,e&&e(null))}},{key:"getBBoxSnapshot",value:function(){return this.lastBoundingBox.width>=0&&this.lastBoundingBox.height>=0?t4({},this.lastBoundingBox):{width:0,height:0}}},{key:"getDefaultPosition",value:function(e){var t,r,n=this.props,o=n.layout,i=n.align,a=n.verticalAlign,u=n.margin,c=n.chartWidth,l=n.chartHeight;return e&&(void 0!==e.left&&null!==e.left||void 0!==e.right&&null!==e.right)||(t="center"===i&&"vertical"===o?{left:((c||0)-this.getBBoxSnapshot().width)/2}:"right"===i?{right:u&&u.right||0}:{left:u&&u.left||0}),e&&(void 0!==e.top&&null!==e.top||void 0!==e.bottom&&null!==e.bottom)||(r="middle"===a?{top:((l||0)-this.getBBoxSnapshot().height)/2}:"bottom"===a?{bottom:u&&u.bottom||0}:{top:u&&u.top||0}),t4(t4({},t),r)}},{key:"render",value:function(){var e=this,t=this.props,r=t.content,n=t.width,o=t.height,i=t.wrapperStyle,a=t.payloadUniqBy,u=t.payload,c=t4(t4({position:"absolute",width:n||"auto",height:o||"auto"},this.getDefaultPosition(i)),i);return l().createElement("div",{className:"recharts-legend-wrapper",style:c,ref:function(t){e.wrapperNode=t}},function(e,t){if(l().isValidElement(e))return l().cloneElement(e,t);if("function"==typeof e)return l().createElement(e,t);t.ref;var r=function(e,t){if(null==e)return{};var r,n,o=function(e,t){if(null==e)return{};var r={};for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){if(t.indexOf(n)>=0)continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(n=0;n<i.length;n++)r=i[n],!(t.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(e,r)&&(o[r]=e[r])}return o}(t,t5);return l().createElement(t1,r)}(r,t4(t4({},this.props),{},{payload:tt(u,a,rr)})))}}],r=[{key:"getWithHeight",value:function(e,t){var r=t4(t4({},this.defaultProps),e.props).layout;return"vertical"===r&&N(e.props.height)?{height:e.props.height}:"horizontal"===r?{width:e.props.width||t}:null}}],t&&t6(n.prototype,t),r&&t6(n,r),Object.defineProperty(n,"prototype",{writable:!1}),n}(c.PureComponent);function ro(){return(ro=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(this,arguments)}re(rn,"displayName","Legend"),re(rn,"defaultProps",{iconSize:14,layout:"horizontal",align:"center",verticalAlign:"bottom"});var ri=function(e){var t=e.cx,r=e.cy,n=e.r,o=e.className,i=(0,x.Z)("recharts-dot",o);return t===+t&&r===+r&&n===+n?l().createElement("circle",ro({},eb(e,!1),en(e),{className:i,cx:t,cy:r,r:n})):null},ra=r(9069),ru=r.n(ra),rc=Object.getOwnPropertyNames,rl=Object.getOwnPropertySymbols,rs=Object.prototype.hasOwnProperty;function rf(e,t){return function(r,n,o){return e(r,n,o)&&t(r,n,o)}}function rp(e){return function(t,r,n){if(!t||!r||"object"!=typeof t||"object"!=typeof r)return e(t,r,n);var o=n.cache,i=o.get(t),a=o.get(r);if(i&&a)return i===r&&a===t;o.set(t,r),o.set(r,t);var u=e(t,r,n);return o.delete(t),o.delete(r),u}}function rd(e){return rc(e).concat(rl(e))}var rh=Object.hasOwn||function(e,t){return rs.call(e,t)};function ry(e,t){return e===t||!e&&!t&&e!=e&&t!=t}var rv=Object.getOwnPropertyDescriptor,rm=Object.keys;function rg(e,t,r){var n=e.length;if(t.length!==n)return!1;for(;n-- >0;)if(!r.equals(e[n],t[n],n,n,e,t,r))return!1;return!0}function rb(e,t){return ry(e.getTime(),t.getTime())}function rx(e,t){return e.name===t.name&&e.message===t.message&&e.cause===t.cause&&e.stack===t.stack}function rw(e,t){return e===t}function rO(e,t,r){var n,o,i=e.size;if(i!==t.size)return!1;if(!i)return!0;for(var a=Array(i),u=e.entries(),c=0;(n=u.next())&&!n.done;){for(var l=t.entries(),s=!1,f=0;(o=l.next())&&!o.done;){if(a[f]){f++;continue}var p=n.value,d=o.value;if(r.equals(p[0],d[0],c,f,e,t,r)&&r.equals(p[1],d[1],p[0],d[0],e,t,r)){s=a[f]=!0;break}f++}if(!s)return!1;c++}return!0}function rj(e,t,r){var n=rm(e),o=n.length;if(rm(t).length!==o)return!1;for(;o-- >0;)if(!rT(e,t,r,n[o]))return!1;return!0}function rS(e,t,r){var n,o,i,a=rd(e),u=a.length;if(rd(t).length!==u)return!1;for(;u-- >0;)if(!rT(e,t,r,n=a[u])||(o=rv(e,n),i=rv(t,n),(o||i)&&(!o||!i||o.configurable!==i.configurable||o.enumerable!==i.enumerable||o.writable!==i.writable)))return!1;return!0}function rP(e,t){return ry(e.valueOf(),t.valueOf())}function rE(e,t){return e.source===t.source&&e.flags===t.flags}function rk(e,t,r){var n,o,i=e.size;if(i!==t.size)return!1;if(!i)return!0;for(var a=Array(i),u=e.values();(n=u.next())&&!n.done;){for(var c=t.values(),l=!1,s=0;(o=c.next())&&!o.done;){if(!a[s]&&r.equals(n.value,o.value,n.value,o.value,e,t,r)){l=a[s]=!0;break}s++}if(!l)return!1}return!0}function rA(e,t){var r=e.length;if(t.length!==r)return!1;for(;r-- >0;)if(e[r]!==t[r])return!1;return!0}function rM(e,t){return e.hostname===t.hostname&&e.pathname===t.pathname&&e.protocol===t.protocol&&e.port===t.port&&e.hash===t.hash&&e.username===t.username&&e.password===t.password}function rT(e,t,r,n){return("_owner"===n||"__o"===n||"__v"===n)&&(!!e.$$typeof||!!t.$$typeof)||rh(t,n)&&r.equals(e[n],t[n],n,n,e,t,r)}var r_=Array.isArray,rC="function"==typeof ArrayBuffer&&ArrayBuffer.isView?ArrayBuffer.isView:null,rN=Object.assign,rD=Object.prototype.toString.call.bind(Object.prototype.toString),rI=rR();function rR(e){void 0===e&&(e={});var t,r,n,o,i,a,u,c,l,s,f,p,d,h=e.circular,y=e.createInternalComparator,v=e.createState,m=e.strict,g=(r=(t=function(e){var t=e.circular,r=e.createCustomConfig,n=e.strict,o={areArraysEqual:n?rS:rg,areDatesEqual:rb,areErrorsEqual:rx,areFunctionsEqual:rw,areMapsEqual:n?rf(rO,rS):rO,areNumbersEqual:ry,areObjectsEqual:n?rS:rj,arePrimitiveWrappersEqual:rP,areRegExpsEqual:rE,areSetsEqual:n?rf(rk,rS):rk,areTypedArraysEqual:n?rS:rA,areUrlsEqual:rM};if(r&&(o=rN({},o,r(o))),t){var i=rp(o.areArraysEqual),a=rp(o.areMapsEqual),u=rp(o.areObjectsEqual),c=rp(o.areSetsEqual);o=rN({},o,{areArraysEqual:i,areMapsEqual:a,areObjectsEqual:u,areSetsEqual:c})}return o}(e)).areArraysEqual,n=t.areDatesEqual,o=t.areErrorsEqual,i=t.areFunctionsEqual,a=t.areMapsEqual,u=t.areNumbersEqual,c=t.areObjectsEqual,l=t.arePrimitiveWrappersEqual,s=t.areRegExpsEqual,f=t.areSetsEqual,p=t.areTypedArraysEqual,d=t.areUrlsEqual,function(e,t,h){if(e===t)return!0;if(null==e||null==t)return!1;var y=typeof e;if(y!==typeof t)return!1;if("object"!==y)return"number"===y?u(e,t,h):"function"===y&&i(e,t,h);var v=e.constructor;if(v!==t.constructor)return!1;if(v===Object)return c(e,t,h);if(r_(e))return r(e,t,h);if(null!=rC&&rC(e))return p(e,t,h);if(v===Date)return n(e,t,h);if(v===RegExp)return s(e,t,h);if(v===Map)return a(e,t,h);if(v===Set)return f(e,t,h);var m=rD(e);return"[object Date]"===m?n(e,t,h):"[object RegExp]"===m?s(e,t,h):"[object Map]"===m?a(e,t,h):"[object Set]"===m?f(e,t,h):"[object Object]"===m?"function"!=typeof e.then&&"function"!=typeof t.then&&c(e,t,h):"[object URL]"===m?d(e,t,h):"[object Error]"===m?o(e,t,h):"[object Arguments]"===m?c(e,t,h):("[object Boolean]"===m||"[object Number]"===m||"[object String]"===m)&&l(e,t,h)}),b=y?y(g):function(e,t,r,n,o,i,a){return g(e,t,a)};return function(e){var t=e.circular,r=e.comparator,n=e.createState,o=e.equals,i=e.strict;if(n)return function(e,a){var u=n(),c=u.cache;return r(e,a,{cache:void 0===c?t?new WeakMap:void 0:c,equals:o,meta:u.meta,strict:i})};if(t)return function(e,t){return r(e,t,{cache:new WeakMap,equals:o,meta:void 0,strict:i})};var a={cache:void 0,equals:o,meta:void 0,strict:i};return function(e,t){return r(e,t,a)}}({circular:void 0!==h&&h,comparator:g,createState:v,equals:b,strict:void 0!==m&&m})}function rL(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,r=-1;requestAnimationFrame(function n(o){if(r<0&&(r=o),o-r>t)e(o),r=-1;else{var i;i=n,"undefined"!=typeof requestAnimationFrame&&requestAnimationFrame(i)}})}function rB(e){return(rB="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function rF(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=Array(t);r<t;r++)n[r]=e[r];return n}function rU(e){return(rU="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function rz(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function r$(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?rz(Object(r),!0).forEach(function(t){rW(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):rz(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function rW(e,t,r){var n;return(n=function(e,t){if("object"!==rU(e)||null===e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!==rU(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(t,"string"),(t="symbol"===rU(n)?n:String(n))in e)?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}rR({strict:!0}),rR({circular:!0}),rR({circular:!0,strict:!0}),rR({createInternalComparator:function(){return ry}}),rR({strict:!0,createInternalComparator:function(){return ry}}),rR({circular:!0,createInternalComparator:function(){return ry}}),rR({circular:!0,createInternalComparator:function(){return ry},strict:!0});var rq=function(e){return e},rH=function(e,t){return Object.keys(t).reduce(function(r,n){return r$(r$({},r),{},rW({},n,e(n,t[n])))},{})},rV=function(e,t,r){return e.map(function(e){return"".concat(e.replace(/([A-Z])/g,function(e){return"-".concat(e.toLowerCase())})," ").concat(t,"ms ").concat(r)}).join(",")},rX=function(e,t,r,n,o,i,a,u){};function rZ(e,t){if(e){if("string"==typeof e)return rG(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);if("Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r)return Array.from(e);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return rG(e,t)}}function rG(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=Array(t);r<t;r++)n[r]=e[r];return n}var rY=function(e,t){return[0,3*e,3*t-6*e,3*e-3*t+1]},rK=function(e,t){return e.map(function(e,r){return e*Math.pow(t,r)}).reduce(function(e,t){return e+t})},rQ=function(e,t){return function(r){return rK(rY(e,t),r)}},rJ=function(){for(var e,t,r=arguments.length,n=Array(r),o=0;o<r;o++)n[o]=arguments[o];var i=n[0],a=n[1],u=n[2],c=n[3];if(1===n.length)switch(n[0]){case"linear":i=0,a=0,u=1,c=1;break;case"ease":i=.25,a=.1,u=.25,c=1;break;case"ease-in":i=.42,a=0,u=1,c=1;break;case"ease-out":i=.42,a=0,u=.58,c=1;break;case"ease-in-out":i=0,a=0,u=.58,c=1;break;default:var l=n[0].split("(");if("cubic-bezier"===l[0]&&4===l[1].split(")")[0].split(",").length){var s,f=function(e){if(Array.isArray(e))return e}(s=l[1].split(")")[0].split(",").map(function(e){return parseFloat(e)}))||function(e,t){var r=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=r){var n,o,i,a,u=[],c=!0,l=!1;try{if(i=(r=r.call(e)).next,0===t){if(Object(r)!==r)return;c=!1}else for(;!(c=(n=i.call(r)).done)&&(u.push(n.value),u.length!==t);c=!0);}catch(e){l=!0,o=e}finally{try{if(!c&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(l)throw o}}return u}}(s,4)||rZ(s,4)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}();i=f[0],a=f[1],u=f[2],c=f[3]}else rX(!1,"[configBezier]: arguments should be one of oneOf 'linear', 'ease', 'ease-in', 'ease-out', 'ease-in-out','cubic-bezier(x1,y1,x2,y2)', instead received %s",n)}rX([i,u,a,c].every(function(e){return"number"==typeof e&&e>=0&&e<=1}),"[configBezier]: arguments should be x1, y1, x2, y2 of [0, 1] instead received %s",n);var p=rQ(i,u),d=rQ(a,c),h=(e=i,t=u,function(r){var n;return rK([].concat(function(e){if(Array.isArray(e))return rG(e)}(n=rY(e,t).map(function(e,t){return e*t}).slice(1))||function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(n)||rZ(n)||function(){throw TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}(),[0]),r)}),y=function(e){for(var t=e>1?1:e,r=t,n=0;n<8;++n){var o,i=p(r)-t,a=h(r);if(1e-4>Math.abs(i-t)||a<1e-4)break;r=(o=r-i/a)>1?1:o<0?0:o}return d(r)};return y.isStepper=!1,y},r0=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=e.stiff,r=void 0===t?100:t,n=e.damping,o=void 0===n?8:n,i=e.dt,a=void 0===i?17:i,u=function(e,t,n){var i=n+(-(e-t)*r-n*o)*a/1e3,u=n*a/1e3+e;return 1e-4>Math.abs(u-t)&&1e-4>Math.abs(i)?[t,0]:[u,i]};return u.isStepper=!0,u.dt=a,u},r1=function(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];var n=t[0];if("string"==typeof n)switch(n){case"ease":case"ease-in-out":case"ease-out":case"ease-in":case"linear":return rJ(n);case"spring":return r0();default:if("cubic-bezier"===n.split("(")[0])return rJ(n);rX(!1,"[configEasing]: first argument should be one of 'ease', 'ease-in', 'ease-out', 'ease-in-out','cubic-bezier(x1,y1,x2,y2)', 'linear' and 'spring', instead  received %s",t)}return"function"==typeof n?n:(rX(!1,"[configEasing]: first argument type should be function or string, instead received %s",t),null)};function r2(e){return(r2="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function r5(e){return function(e){if(Array.isArray(e))return r8(e)}(e)||function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(e)||r7(e)||function(){throw TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function r3(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function r4(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?r3(Object(r),!0).forEach(function(t){r6(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):r3(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function r6(e,t,r){var n;return(n=function(e,t){if("object"!==r2(e)||null===e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!==r2(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(t,"string"),(t="symbol"===r2(n)?n:String(n))in e)?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function r7(e,t){if(e){if("string"==typeof e)return r8(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);if("Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r)return Array.from(e);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return r8(e,t)}}function r8(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=Array(t);r<t;r++)n[r]=e[r];return n}var r9=function(e,t,r){return e+(t-e)*r},ne=function(e){return e.from!==e.to},nt=function e(t,r,n){var o=rH(function(e,r){if(ne(r)){var n,o=function(e){if(Array.isArray(e))return e}(n=t(r.from,r.to,r.velocity))||function(e,t){var r=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=r){var n,o,i,a,u=[],c=!0,l=!1;try{if(i=(r=r.call(e)).next,0===t){if(Object(r)!==r)return;c=!1}else for(;!(c=(n=i.call(r)).done)&&(u.push(n.value),u.length!==t);c=!0);}catch(e){l=!0,o=e}finally{try{if(!c&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(l)throw o}}return u}}(n,2)||r7(n,2)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}(),i=o[0],a=o[1];return r4(r4({},r),{},{from:i,velocity:a})}return r},r);return n<1?rH(function(e,t){return ne(t)?r4(r4({},t),{},{velocity:r9(t.velocity,o[e].velocity,n),from:r9(t.from,o[e].from,n)}):t},r):e(t,o,n-1)};function nr(e){return(nr="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}var nn=["children","begin","duration","attributeName","easing","isActive","steps","from","to","canBegin","onAnimationEnd","shouldReAnimate","onAnimationReStart"];function no(e){return function(e){if(Array.isArray(e))return ni(e)}(e)||function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(e)||function(e,t){if(e){if("string"==typeof e)return ni(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);if("Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r)return Array.from(e);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return ni(e,t)}}(e)||function(){throw TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function ni(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=Array(t);r<t;r++)n[r]=e[r];return n}function na(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function nu(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?na(Object(r),!0).forEach(function(t){nc(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):na(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function nc(e,t,r){return(t=ns(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function nl(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,ns(n.key),n)}}function ns(e){var t=function(e,t){if("object"!==nr(e)||null===e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!==nr(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"===nr(t)?t:String(t)}function nf(e,t){return(nf=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e})(e,t)}function np(e,t){if(t&&("object"===nr(t)||"function"==typeof t))return t;if(void 0!==t)throw TypeError("Derived constructors may only return object or undefined");return nd(e)}function nd(e){if(void 0===e)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function nh(e){return(nh=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)})(e)}var ny=function(e){!function(e,t){if("function"!=typeof t&&null!==t)throw TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&nf(e,t)}(i,e);var t,r,n,o=(t=function(){if("undefined"==typeof Reflect||!Reflect.construct||Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){})),!0}catch(e){return!1}}(),function(){var e,r=nh(i);if(t){var n=nh(this).constructor;e=Reflect.construct(r,arguments,n)}else e=r.apply(this,arguments);return np(this,e)});function i(e,t){!function(e,t){if(!(e instanceof t))throw TypeError("Cannot call a class as a function")}(this,i);var r,n=(r=o.call(this,e,t)).props,a=n.isActive,u=n.attributeName,c=n.from,l=n.to,s=n.steps,f=n.children,p=n.duration;if(r.handleStyleChange=r.handleStyleChange.bind(nd(r)),r.changeStyle=r.changeStyle.bind(nd(r)),!a||p<=0)return r.state={style:{}},"function"==typeof f&&(r.state={style:l}),np(r);if(s&&s.length)r.state={style:s[0].style};else if(c){if("function"==typeof f)return r.state={style:c},np(r);r.state={style:u?nc({},u,c):c}}else r.state={style:{}};return r}return r=[{key:"componentDidMount",value:function(){var e=this.props,t=e.isActive,r=e.canBegin;this.mounted=!0,t&&r&&this.runAnimation(this.props)}},{key:"componentDidUpdate",value:function(e){var t=this.props,r=t.isActive,n=t.canBegin,o=t.attributeName,i=t.shouldReAnimate,a=t.to,u=t.from,c=this.state.style;if(n){if(!r){var l={style:o?nc({},o,a):a};this.state&&c&&(o&&c[o]!==a||!o&&c!==a)&&this.setState(l);return}if(!rI(e.to,a)||!e.canBegin||!e.isActive){var s=!e.canBegin||!e.isActive;this.manager&&this.manager.stop(),this.stopJSAnimation&&this.stopJSAnimation();var f=s||i?u:e.to;if(this.state&&c){var p={style:o?nc({},o,f):f};(o&&c[o]!==f||!o&&c!==f)&&this.setState(p)}this.runAnimation(nu(nu({},this.props),{},{from:f,begin:0}))}}}},{key:"componentWillUnmount",value:function(){this.mounted=!1;var e=this.props.onAnimationEnd;this.unSubscribe&&this.unSubscribe(),this.manager&&(this.manager.stop(),this.manager=null),this.stopJSAnimation&&this.stopJSAnimation(),e&&e()}},{key:"handleStyleChange",value:function(e){this.changeStyle(e)}},{key:"changeStyle",value:function(e){this.mounted&&this.setState({style:e})}},{key:"runJSAnimation",value:function(e){var t,r,n,o,i,a,u,c,l,s=this,f=e.from,p=e.to,d=e.duration,h=e.easing,y=e.begin,v=e.onAnimationEnd,m=e.onAnimationStart,g=(t=r1(h),r=this.changeStyle,a=(i=[Object.keys(f),Object.keys(p)].reduce(function(e,t){return e.filter(function(e){return t.includes(e)})})).reduce(function(e,t){return r4(r4({},e),{},r6({},t,[f[t],p[t]]))},{}),u=i.reduce(function(e,t){return r4(r4({},e),{},r6({},t,{from:f[t],velocity:0,to:p[t]}))},{}),c=-1,l=function(){return null},l=t.isStepper?function(e){n||(n=e);var o=(e-n)/t.dt;u=nt(t,u,o),r(r4(r4(r4({},f),p),rH(function(e,t){return t.from},u))),n=e,Object.values(u).filter(ne).length&&(c=requestAnimationFrame(l))}:function(e){o||(o=e);var n=(e-o)/d,i=rH(function(e,r){return r9.apply(void 0,r5(r).concat([t(n)]))},a);if(r(r4(r4(r4({},f),p),i)),n<1)c=requestAnimationFrame(l);else{var u=rH(function(e,r){return r9.apply(void 0,r5(r).concat([t(1)]))},a);r(r4(r4(r4({},f),p),u))}},function(){return requestAnimationFrame(l),function(){cancelAnimationFrame(c)}});this.manager.start([m,y,function(){s.stopJSAnimation=g()},d,v])}},{key:"runStepAnimation",value:function(e){var t=this,r=e.steps,n=e.begin,o=e.onAnimationStart,i=r[0],a=i.style,u=i.duration;return this.manager.start([o].concat(no(r.reduce(function(e,n,o){if(0===o)return e;var i=n.duration,a=n.easing,u=void 0===a?"ease":a,c=n.style,l=n.properties,s=n.onAnimationEnd,f=o>0?r[o-1]:n,p=l||Object.keys(c);if("function"==typeof u||"spring"===u)return[].concat(no(e),[t.runJSAnimation.bind(t,{from:f.style,to:c,duration:i,easing:u}),i]);var d=rV(p,i,u),h=nu(nu(nu({},f.style),c),{},{transition:d});return[].concat(no(e),[h,i,s]).filter(rq)},[a,Math.max(void 0===u?0:u,n)])),[e.onAnimationEnd]))}},{key:"runAnimation",value:function(e){if(!this.manager){var t,r,n;this.manager=(t=function(){return null},r=!1,n=function e(n){if(!r){if(Array.isArray(n)){if(!n.length)return;var o=function(e){if(Array.isArray(e))return e}(n)||function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(n)||function(e,t){if(e){if("string"==typeof e)return rF(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);if("Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r)return Array.from(e);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return rF(e,t)}}(n)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}(),i=o[0],a=o.slice(1);if("number"==typeof i){rL(e.bind(null,a),i);return}e(i),rL(e.bind(null,a));return}"object"===rB(n)&&t(n),"function"==typeof n&&n()}},{stop:function(){r=!0},start:function(e){r=!1,n(e)},subscribe:function(e){return t=e,function(){t=function(){return null}}}})}var o=e.begin,i=e.duration,a=e.attributeName,u=e.to,c=e.easing,l=e.onAnimationStart,s=e.onAnimationEnd,f=e.steps,p=e.children,d=this.manager;if(this.unSubscribe=d.subscribe(this.handleStyleChange),"function"==typeof c||"function"==typeof p||"spring"===c){this.runJSAnimation(e);return}if(f.length>1){this.runStepAnimation(e);return}var h=a?nc({},a,u):u,y=rV(Object.keys(h),i,c);d.start([l,o,nu(nu({},h),{},{transition:y}),i,s])}},{key:"render",value:function(){var e=this.props,t=e.children,r=(e.begin,e.duration),n=(e.attributeName,e.easing,e.isActive),o=(e.steps,e.from,e.to,e.canBegin,e.onAnimationEnd,e.shouldReAnimate,e.onAnimationReStart,function(e,t){if(null==e)return{};var r,n,o=function(e,t){if(null==e)return{};var r,n,o={},i=Object.keys(e);for(n=0;n<i.length;n++)r=i[n],t.indexOf(r)>=0||(o[r]=e[r]);return o}(e,t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(n=0;n<i.length;n++)r=i[n],!(t.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(e,r)&&(o[r]=e[r])}return o}(e,nn)),i=c.Children.count(t),a=this.state.style;if("function"==typeof t)return t(a);if(!n||0===i||r<=0)return t;var u=function(e){var t=e.props,r=t.style,n=t.className;return(0,c.cloneElement)(e,nu(nu({},o),{},{style:nu(nu({},void 0===r?{}:r),a),className:n}))};return 1===i?u(c.Children.only(t)):l().createElement("div",null,c.Children.map(t,function(e){return u(e)}))}}],nl(i.prototype,r),n&&nl(i,n),Object.defineProperty(i,"prototype",{writable:!1}),i}(c.PureComponent);function nv(e){return(nv="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function nm(){return(nm=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(this,arguments)}function ng(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=Array(t);r<t;r++)n[r]=e[r];return n}function nb(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function nx(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?nb(Object(r),!0).forEach(function(t){var n,o;n=t,o=r[t],(n=function(e){var t=function(e,t){if("object"!=nv(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=nv(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==nv(t)?t:t+""}(n))in e?Object.defineProperty(e,n,{value:o,enumerable:!0,configurable:!0,writable:!0}):e[n]=o}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):nb(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}ny.displayName="Animate",ny.defaultProps={begin:0,duration:1e3,from:"",to:"",attributeName:"",easing:"ease",isActive:!0,canBegin:!0,steps:[],onAnimationEnd:function(){},onAnimationStart:function(){}},ny.propTypes={from:ru().oneOfType([ru().object,ru().string]),to:ru().oneOfType([ru().object,ru().string]),attributeName:ru().string,duration:ru().number,begin:ru().number,easing:ru().oneOfType([ru().string,ru().func]),steps:ru().arrayOf(ru().shape({duration:ru().number.isRequired,style:ru().object.isRequired,easing:ru().oneOfType([ru().oneOf(["ease","ease-in","ease-out","ease-in-out","linear"]),ru().func]),properties:ru().arrayOf("string"),onAnimationEnd:ru().func})),children:ru().oneOfType([ru().node,ru().func]),isActive:ru().bool,canBegin:ru().bool,onAnimationEnd:ru().func,shouldReAnimate:ru().bool,onAnimationStart:ru().func,onAnimationReStart:ru().func};var nw=function(e,t,r,n,o){var i,a=Math.min(Math.abs(r)/2,Math.abs(n)/2),u=n>=0?1:-1,c=r>=0?1:-1,l=n>=0&&r>=0||n<0&&r<0?1:0;if(a>0&&o instanceof Array){for(var s=[0,0,0,0],f=0;f<4;f++)s[f]=o[f]>a?a:o[f];i="M".concat(e,",").concat(t+u*s[0]),s[0]>0&&(i+="A ".concat(s[0],",").concat(s[0],",0,0,").concat(l,",").concat(e+c*s[0],",").concat(t)),i+="L ".concat(e+r-c*s[1],",").concat(t),s[1]>0&&(i+="A ".concat(s[1],",").concat(s[1],",0,0,").concat(l,",\n        ").concat(e+r,",").concat(t+u*s[1])),i+="L ".concat(e+r,",").concat(t+n-u*s[2]),s[2]>0&&(i+="A ".concat(s[2],",").concat(s[2],",0,0,").concat(l,",\n        ").concat(e+r-c*s[2],",").concat(t+n)),i+="L ".concat(e+c*s[3],",").concat(t+n),s[3]>0&&(i+="A ".concat(s[3],",").concat(s[3],",0,0,").concat(l,",\n        ").concat(e,",").concat(t+n-u*s[3])),i+="Z"}else if(a>0&&o===+o&&o>0){var p=Math.min(a,o);i="M ".concat(e,",").concat(t+u*p,"\n            A ").concat(p,",").concat(p,",0,0,").concat(l,",").concat(e+c*p,",").concat(t,"\n            L ").concat(e+r-c*p,",").concat(t,"\n            A ").concat(p,",").concat(p,",0,0,").concat(l,",").concat(e+r,",").concat(t+u*p,"\n            L ").concat(e+r,",").concat(t+n-u*p,"\n            A ").concat(p,",").concat(p,",0,0,").concat(l,",").concat(e+r-c*p,",").concat(t+n,"\n            L ").concat(e+c*p,",").concat(t+n,"\n            A ").concat(p,",").concat(p,",0,0,").concat(l,",").concat(e,",").concat(t+n-u*p," Z")}else i="M ".concat(e,",").concat(t," h ").concat(r," v ").concat(n," h ").concat(-r," Z");return i},nO=function(e,t){if(!e||!t)return!1;var r=e.x,n=e.y,o=t.x,i=t.y,a=t.width,u=t.height;return!!(Math.abs(a)>0&&Math.abs(u)>0)&&r>=Math.min(o,o+a)&&r<=Math.max(o,o+a)&&n>=Math.min(i,i+u)&&n<=Math.max(i,i+u)},nj={x:0,y:0,width:0,height:0,radius:0,isAnimationActive:!1,isUpdateAnimationActive:!1,animationBegin:0,animationDuration:1500,animationEasing:"ease"},nS=function(e){var t,r=nx(nx({},nj),e),n=(0,c.useRef)(),o=function(e){if(Array.isArray(e))return e}(t=(0,c.useState)(-1))||function(e,t){var r=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=r){var n,o,i,a,u=[],c=!0,l=!1;try{if(i=(r=r.call(e)).next,0===t){if(Object(r)!==r)return;c=!1}else for(;!(c=(n=i.call(r)).done)&&(u.push(n.value),u.length!==t);c=!0);}catch(e){l=!0,o=e}finally{try{if(!c&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(l)throw o}}return u}}(t,2)||function(e,t){if(e){if("string"==typeof e)return ng(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);if("Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r)return Array.from(e);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return ng(e,t)}}(t,2)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}(),i=o[0],a=o[1];(0,c.useEffect)(function(){if(n.current&&n.current.getTotalLength)try{var e=n.current.getTotalLength();e&&a(e)}catch(e){}},[]);var u=r.x,s=r.y,f=r.width,p=r.height,d=r.radius,h=r.className,y=r.animationEasing,v=r.animationDuration,m=r.animationBegin,g=r.isAnimationActive,b=r.isUpdateAnimationActive;if(u!==+u||s!==+s||f!==+f||p!==+p||0===f||0===p)return null;var w=(0,x.Z)("recharts-rectangle",h);return b?l().createElement(ny,{canBegin:i>0,from:{width:f,height:p,x:u,y:s},to:{width:f,height:p,x:u,y:s},duration:v,animationEasing:y,isActive:b},function(e){var t=e.width,o=e.height,a=e.x,u=e.y;return l().createElement(ny,{canBegin:i>0,from:"0px ".concat(-1===i?1:i,"px"),to:"".concat(i,"px 0px"),attributeName:"strokeDasharray",begin:m,duration:v,isActive:g,easing:y},l().createElement("path",nm({},eb(r,!0),{className:w,d:nw(a,u,t,o,d),ref:n})))}):l().createElement("path",nm({},eb(r,!0),{className:w,d:nw(u,s,f,p,d)}))};function nP(e,t){switch(arguments.length){case 0:break;case 1:this.range(e);break;default:this.range(t).domain(e)}return this}function nE(e,t){switch(arguments.length){case 0:break;case 1:"function"==typeof e?this.interpolator(e):this.range(e);break;default:this.domain(e),"function"==typeof t?this.interpolator(t):this.range(t)}return this}class nk extends Map{constructor(e,t=nM){if(super(),Object.defineProperties(this,{_intern:{value:new Map},_key:{value:t}}),null!=e)for(let[t,r]of e)this.set(t,r)}get(e){return super.get(nA(this,e))}has(e){return super.has(nA(this,e))}set(e,t){return super.set(function({_intern:e,_key:t},r){let n=t(r);return e.has(n)?e.get(n):(e.set(n,r),r)}(this,e),t)}delete(e){return super.delete(function({_intern:e,_key:t},r){let n=t(r);return e.has(n)&&(r=e.get(n),e.delete(n)),r}(this,e))}}function nA({_intern:e,_key:t},r){let n=t(r);return e.has(n)?e.get(n):r}function nM(e){return null!==e&&"object"==typeof e?e.valueOf():e}let nT=Symbol("implicit");function n_(){var e=new nk,t=[],r=[],n=nT;function o(o){let i=e.get(o);if(void 0===i){if(n!==nT)return n;e.set(o,i=t.push(o)-1)}return r[i%r.length]}return o.domain=function(r){if(!arguments.length)return t.slice();for(let n of(t=[],e=new nk,r))e.has(n)||e.set(n,t.push(n)-1);return o},o.range=function(e){return arguments.length?(r=Array.from(e),o):r.slice()},o.unknown=function(e){return arguments.length?(n=e,o):n},o.copy=function(){return n_(t,r).unknown(n)},nP.apply(o,arguments),o}function nC(){var e,t,r=n_().unknown(void 0),n=r.domain,o=r.range,i=0,a=1,u=!1,c=0,l=0,s=.5;function f(){var r=n().length,f=a<i,p=f?a:i,d=f?i:a;e=(d-p)/Math.max(1,r-c+2*l),u&&(e=Math.floor(e)),p+=(d-p-e*(r-c))*s,t=e*(1-c),u&&(p=Math.round(p),t=Math.round(t));var h=(function(e,t,r){e=+e,t=+t,r=(o=arguments.length)<2?(t=e,e=0,1):o<3?1:+r;for(var n=-1,o=0|Math.max(0,Math.ceil((t-e)/r)),i=Array(o);++n<o;)i[n]=e+n*r;return i})(r).map(function(t){return p+e*t});return o(f?h.reverse():h)}return delete r.unknown,r.domain=function(e){return arguments.length?(n(e),f()):n()},r.range=function(e){return arguments.length?([i,a]=e,i=+i,a=+a,f()):[i,a]},r.rangeRound=function(e){return[i,a]=e,i=+i,a=+a,u=!0,f()},r.bandwidth=function(){return t},r.step=function(){return e},r.round=function(e){return arguments.length?(u=!!e,f()):u},r.padding=function(e){return arguments.length?(c=Math.min(1,l=+e),f()):c},r.paddingInner=function(e){return arguments.length?(c=Math.min(1,e),f()):c},r.paddingOuter=function(e){return arguments.length?(l=+e,f()):l},r.align=function(e){return arguments.length?(s=Math.max(0,Math.min(1,e)),f()):s},r.copy=function(){return nC(n(),[i,a]).round(u).paddingInner(c).paddingOuter(l).align(s)},nP.apply(f(),arguments)}function nN(){return function e(t){var r=t.copy;return t.padding=t.paddingOuter,delete t.paddingInner,delete t.paddingOuter,t.copy=function(){return e(r())},t}(nC.apply(null,arguments).paddingInner(1))}function nD(e){return(nD="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function nI(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function nR(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?nI(Object(r),!0).forEach(function(t){var n,o;n=t,o=r[t],(n=function(e){var t=function(e,t){if("object"!=nD(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=nD(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==nD(t)?t:t+""}(n))in e?Object.defineProperty(e,n,{value:o,enumerable:!0,configurable:!0,writable:!0}):e[n]=o}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):nI(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}var nL={widthCache:{},cacheCount:0},nB={position:"absolute",top:"-20000px",left:0,padding:0,margin:0,border:"none",whiteSpace:"pre"},nF="recharts_measurement_span",nU=function(e){var t,r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(null==e||e8.isSsr)return{width:0,height:0};var n=(Object.keys(t=nR({},r)).forEach(function(e){t[e]||delete t[e]}),t),o=JSON.stringify({text:e,copyStyle:n});if(nL.widthCache[o])return nL.widthCache[o];try{var i=document.getElementById(nF);i||((i=document.createElement("span")).setAttribute("id",nF),i.setAttribute("aria-hidden","true"),document.body.appendChild(i));var a=nR(nR({},nB),n);Object.assign(i.style,a),i.textContent="".concat(e);var u=i.getBoundingClientRect(),c={width:u.width,height:u.height};return nL.widthCache[o]=c,++nL.cacheCount>2e3&&(nL.cacheCount=0,nL.widthCache={}),c}catch(e){return{width:0,height:0}}};function nz(e){return(nz="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function n$(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var r=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=r){var n,o,i,a,u=[],c=!0,l=!1;try{if(i=(r=r.call(e)).next,0===t){if(Object(r)!==r)return;c=!1}else for(;!(c=(n=i.call(r)).done)&&(u.push(n.value),u.length!==t);c=!0);}catch(e){l=!0,o=e}finally{try{if(!c&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(l)throw o}}return u}}(e,t)||function(e,t){if(e){if("string"==typeof e)return nW(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);if("Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r)return Array.from(e);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return nW(e,t)}}(e,t)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function nW(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=Array(t);r<t;r++)n[r]=e[r];return n}function nq(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,function(e){var t=function(e,t){if("object"!=nz(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=nz(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==nz(t)?t:t+""}(n.key),n)}}var nH=/(-?\d+(?:\.\d+)?[a-zA-Z%]*)([*/])(-?\d+(?:\.\d+)?[a-zA-Z%]*)/,nV=/(-?\d+(?:\.\d+)?[a-zA-Z%]*)([+-])(-?\d+(?:\.\d+)?[a-zA-Z%]*)/,nX=/^px|cm|vh|vw|em|rem|%|mm|in|pt|pc|ex|ch|vmin|vmax|Q$/,nZ=/(-?\d+(?:\.\d+)?)([a-zA-Z%]+)?/,nG={cm:96/2.54,mm:96/25.4,pt:96/72,pc:16,in:96,Q:96/101.6,px:1},nY=Object.keys(nG),nK=function(){var e,t;function r(e,t){(function(e,t){if(!(e instanceof t))throw TypeError("Cannot call a class as a function")})(this,r),this.num=e,this.unit=t,this.num=e,this.unit=t,Number.isNaN(e)&&(this.unit=""),""===t||nX.test(t)||(this.num=NaN,this.unit=""),nY.includes(t)&&(this.num=e*nG[t],this.unit="px")}return e=[{key:"add",value:function(e){return this.unit!==e.unit?new r(NaN,""):new r(this.num+e.num,this.unit)}},{key:"subtract",value:function(e){return this.unit!==e.unit?new r(NaN,""):new r(this.num-e.num,this.unit)}},{key:"multiply",value:function(e){return""!==this.unit&&""!==e.unit&&this.unit!==e.unit?new r(NaN,""):new r(this.num*e.num,this.unit||e.unit)}},{key:"divide",value:function(e){return""!==this.unit&&""!==e.unit&&this.unit!==e.unit?new r(NaN,""):new r(this.num/e.num,this.unit||e.unit)}},{key:"toString",value:function(){return"".concat(this.num).concat(this.unit)}},{key:"isNaN",value:function(){return Number.isNaN(this.num)}}],t=[{key:"parse",value:function(e){var t,n=n$(null!==(t=nZ.exec(e))&&void 0!==t?t:[],3),o=n[1],i=n[2];return new r(parseFloat(o),null!=i?i:"")}}],e&&nq(r.prototype,e),t&&nq(r,t),Object.defineProperty(r,"prototype",{writable:!1}),r}();function nQ(e){if(e.includes("NaN"))return"NaN";for(var t=e;t.includes("*")||t.includes("/");){var r,n=n$(null!==(r=nH.exec(t))&&void 0!==r?r:[],4),o=n[1],i=n[2],a=n[3],u=nK.parse(null!=o?o:""),c=nK.parse(null!=a?a:""),l="*"===i?u.multiply(c):u.divide(c);if(l.isNaN())return"NaN";t=t.replace(nH,l.toString())}for(;t.includes("+")||/.-\d+(?:\.\d+)?/.test(t);){var s,f=n$(null!==(s=nV.exec(t))&&void 0!==s?s:[],4),p=f[1],d=f[2],h=f[3],y=nK.parse(null!=p?p:""),v=nK.parse(null!=h?h:""),m="+"===d?y.add(v):y.subtract(v);if(m.isNaN())return"NaN";t=t.replace(nV,m.toString())}return t}var nJ=/\(([^()]*)\)/;function n0(e){var t=function(e){try{var t;return t=e.replace(/\s+/g,""),t=function(e){for(var t=e;t.includes("(");){var r=n$(nJ.exec(t),2)[1];t=t.replace(nJ,nQ(r))}return t}(t),t=nQ(t)}catch(e){return"NaN"}}(e.slice(5,-1));return"NaN"===t?"":t}var n1=["x","y","lineHeight","capHeight","scaleToFit","textAnchor","verticalAnchor","fill"],n2=["dx","dy","angle","className","breakAll"];function n5(){return(n5=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(this,arguments)}function n3(e,t){if(null==e)return{};var r,n,o=function(e,t){if(null==e)return{};var r={};for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){if(t.indexOf(n)>=0)continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(n=0;n<i.length;n++)r=i[n],!(t.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(e,r)&&(o[r]=e[r])}return o}function n4(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var r=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=r){var n,o,i,a,u=[],c=!0,l=!1;try{if(i=(r=r.call(e)).next,0===t){if(Object(r)!==r)return;c=!1}else for(;!(c=(n=i.call(r)).done)&&(u.push(n.value),u.length!==t);c=!0);}catch(e){l=!0,o=e}finally{try{if(!c&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(l)throw o}}return u}}(e,t)||function(e,t){if(e){if("string"==typeof e)return n6(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);if("Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r)return Array.from(e);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return n6(e,t)}}(e,t)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function n6(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=Array(t);r<t;r++)n[r]=e[r];return n}var n7=/[ \f\n\r\t\v\u2028\u2029]+/,n8=function(e){var t=e.children,r=e.breakAll,n=e.style;try{var o=[];H()(t)||(o=r?t.toString().split(""):t.toString().split(n7));var i=o.map(function(e){return{word:e,width:nU(e,n).width}}),a=r?0:nU("\xa0",n).width;return{wordsWithComputedWidth:i,spaceWidth:a}}catch(e){return null}},n9=function(e,t,r,n,o){var i,a=e.maxLines,u=e.children,c=e.style,l=e.breakAll,s=N(a),f=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[];return e.reduce(function(e,t){var i=t.word,a=t.width,u=e[e.length-1];return u&&(null==n||o||u.width+a+r<Number(n))?(u.words.push(i),u.width+=a+r):e.push({words:[i],width:a}),e},[])},p=f(t);if(!s)return p;for(var d=function(e){var t=f(n8({breakAll:l,style:c,children:u.slice(0,e)+"…"}).wordsWithComputedWidth);return[t.length>a||t.reduce(function(e,t){return e.width>t.width?e:t}).width>Number(n),t]},h=0,y=u.length-1,v=0;h<=y&&v<=u.length-1;){var m=Math.floor((h+y)/2),g=n4(d(m-1),2),b=g[0],x=g[1],w=n4(d(m),1)[0];if(b||w||(h=m+1),b&&w&&(y=m-1),!b&&w){i=x;break}v++}return i||p},oe=function(e){return[{words:H()(e)?[]:e.toString().split(n7)}]},ot=function(e){var t=e.width,r=e.scaleToFit,n=e.children,o=e.style,i=e.breakAll,a=e.maxLines;if((t||r)&&!e8.isSsr){var u=n8({breakAll:i,children:n,style:o});return u?n9({breakAll:i,children:n,maxLines:a,style:o},u.wordsWithComputedWidth,u.spaceWidth,t,r):oe(n)}return oe(n)},or="#808080",Text=function(e){var t,r=e.x,n=void 0===r?0:r,o=e.y,i=void 0===o?0:o,a=e.lineHeight,u=void 0===a?"1em":a,s=e.capHeight,f=void 0===s?"0.71em":s,p=e.scaleToFit,d=void 0!==p&&p,h=e.textAnchor,y=e.verticalAnchor,v=e.fill,m=void 0===v?or:v,g=n3(e,n1),b=(0,c.useMemo)(function(){return ot({breakAll:g.breakAll,children:g.children,maxLines:g.maxLines,scaleToFit:d,style:g.style,width:g.width})},[g.breakAll,g.children,g.maxLines,d,g.style,g.width]),w=g.dx,O=g.dy,j=g.angle,S=g.className,P=g.breakAll,E=n3(g,n2);if(!D(n)||!D(i))return null;var k=n+(N(w)?w:0),A=i+(N(O)?O:0);switch(void 0===y?"end":y){case"start":t=n0("calc(".concat(f,")"));break;case"middle":t=n0("calc(".concat((b.length-1)/2," * -").concat(u," + (").concat(f," / 2))"));break;default:t=n0("calc(".concat(b.length-1," * -").concat(u,")"))}var M=[];if(d){var T=b[0].width,_=g.width;M.push("scale(".concat((N(_)?_/T:1)/T,")"))}return j&&M.push("rotate(".concat(j,", ").concat(k,", ").concat(A,")")),M.length&&(E.transform=M.join(" ")),l().createElement("text",n5({},eb(E,!0),{x:k,y:A,className:(0,x.Z)("recharts-text",S),textAnchor:void 0===h?"start":h,fill:m.includes("url")?or:m}),b.map(function(e,r){var n=e.words.join(P?"":" ");return l().createElement("tspan",{x:k,dy:0===r?t:u,key:"".concat(n,"-").concat(r)},n)}))};let on=Math.sqrt(50),oo=Math.sqrt(10),oi=Math.sqrt(2);function oa(e,t,r){let n,o,i;let a=(t-e)/Math.max(0,r),u=Math.floor(Math.log10(a)),c=a/Math.pow(10,u),l=c>=on?10:c>=oo?5:c>=oi?2:1;return(u<0?(n=Math.round(e*(i=Math.pow(10,-u)/l)),o=Math.round(t*i),n/i<e&&++n,o/i>t&&--o,i=-i):(n=Math.round(e/(i=Math.pow(10,u)*l)),o=Math.round(t/i),n*i<e&&++n,o*i>t&&--o),o<n&&.5<=r&&r<2)?oa(e,t,2*r):[n,o,i]}function ou(e,t,r){if(t=+t,e=+e,!((r=+r)>0))return[];if(e===t)return[e];let n=t<e,[o,i,a]=n?oa(t,e,r):oa(e,t,r);if(!(i>=o))return[];let u=i-o+1,c=Array(u);if(n){if(a<0)for(let e=0;e<u;++e)c[e]=-((i-e)/a);else for(let e=0;e<u;++e)c[e]=(i-e)*a}else if(a<0)for(let e=0;e<u;++e)c[e]=-((o+e)/a);else for(let e=0;e<u;++e)c[e]=(o+e)*a;return c}function oc(e,t,r){return oa(e=+e,t=+t,r=+r)[2]}function ol(e,t,r){t=+t,e=+e,r=+r;let n=t<e,o=n?oc(t,e,r):oc(e,t,r);return(n?-1:1)*(o<0?-(1/o):o)}function os(e,t){return null==e||null==t?NaN:e<t?-1:e>t?1:e>=t?0:NaN}function of(e,t){return null==e||null==t?NaN:t<e?-1:t>e?1:t>=e?0:NaN}function op(e){let t,r,n;function o(e,n,o=0,i=e.length){if(o<i){if(0!==t(n,n))return i;do{let t=o+i>>>1;0>r(e[t],n)?o=t+1:i=t}while(o<i)}return o}return 2!==e.length?(t=os,r=(t,r)=>os(e(t),r),n=(t,r)=>e(t)-r):(t=e===os||e===of?e:od,r=e,n=e),{left:o,center:function(e,t,r=0,i=e.length){let a=o(e,t,r,i-1);return a>r&&n(e[a-1],t)>-n(e[a],t)?a-1:a},right:function(e,n,o=0,i=e.length){if(o<i){if(0!==t(n,n))return i;do{let t=o+i>>>1;0>=r(e[t],n)?o=t+1:i=t}while(o<i)}return o}}}function od(){return 0}function oh(e){return null===e?NaN:+e}let oy=op(os),ov=oy.right;function om(e,t,r){e.prototype=t.prototype=r,r.constructor=e}function og(e,t){var r=Object.create(e.prototype);for(var n in t)r[n]=t[n];return r}function ob(){}oy.left,op(oh).center;var ox="\\s*([+-]?\\d+)\\s*",ow="\\s*([+-]?(?:\\d*\\.)?\\d+(?:[eE][+-]?\\d+)?)\\s*",oO="\\s*([+-]?(?:\\d*\\.)?\\d+(?:[eE][+-]?\\d+)?)%\\s*",oj=/^#([0-9a-f]{3,8})$/,oS=RegExp(`^rgb\\(${ox},${ox},${ox}\\)$`),oP=RegExp(`^rgb\\(${oO},${oO},${oO}\\)$`),oE=RegExp(`^rgba\\(${ox},${ox},${ox},${ow}\\)$`),ok=RegExp(`^rgba\\(${oO},${oO},${oO},${ow}\\)$`),oA=RegExp(`^hsl\\(${ow},${oO},${oO}\\)$`),oM=RegExp(`^hsla\\(${ow},${oO},${oO},${ow}\\)$`),oT={aliceblue:15792383,antiquewhite:16444375,aqua:65535,aquamarine:8388564,azure:15794175,beige:16119260,bisque:16770244,black:0,blanchedalmond:16772045,blue:255,blueviolet:9055202,brown:10824234,burlywood:14596231,cadetblue:6266528,chartreuse:8388352,chocolate:13789470,coral:16744272,cornflowerblue:6591981,cornsilk:16775388,crimson:14423100,cyan:65535,darkblue:139,darkcyan:35723,darkgoldenrod:12092939,darkgray:11119017,darkgreen:25600,darkgrey:11119017,darkkhaki:12433259,darkmagenta:9109643,darkolivegreen:5597999,darkorange:16747520,darkorchid:10040012,darkred:9109504,darksalmon:15308410,darkseagreen:9419919,darkslateblue:4734347,darkslategray:3100495,darkslategrey:3100495,darkturquoise:52945,darkviolet:9699539,deeppink:16716947,deepskyblue:49151,dimgray:6908265,dimgrey:6908265,dodgerblue:2003199,firebrick:11674146,floralwhite:16775920,forestgreen:2263842,fuchsia:16711935,gainsboro:14474460,ghostwhite:16316671,gold:16766720,goldenrod:14329120,gray:8421504,green:32768,greenyellow:11403055,grey:8421504,honeydew:15794160,hotpink:16738740,indianred:13458524,indigo:4915330,ivory:16777200,khaki:15787660,lavender:15132410,lavenderblush:16773365,lawngreen:8190976,lemonchiffon:16775885,lightblue:11393254,lightcoral:15761536,lightcyan:14745599,lightgoldenrodyellow:16448210,lightgray:13882323,lightgreen:9498256,lightgrey:13882323,lightpink:16758465,lightsalmon:16752762,lightseagreen:2142890,lightskyblue:8900346,lightslategray:7833753,lightslategrey:7833753,lightsteelblue:11584734,lightyellow:16777184,lime:65280,limegreen:3329330,linen:16445670,magenta:16711935,maroon:8388608,mediumaquamarine:6737322,mediumblue:205,mediumorchid:12211667,mediumpurple:9662683,mediumseagreen:3978097,mediumslateblue:8087790,mediumspringgreen:64154,mediumturquoise:4772300,mediumvioletred:13047173,midnightblue:1644912,mintcream:16121850,mistyrose:16770273,moccasin:16770229,navajowhite:16768685,navy:128,oldlace:16643558,olive:8421376,olivedrab:7048739,orange:16753920,orangered:16729344,orchid:14315734,palegoldenrod:15657130,palegreen:10025880,paleturquoise:11529966,palevioletred:14381203,papayawhip:16773077,peachpuff:16767673,peru:13468991,pink:16761035,plum:14524637,powderblue:11591910,purple:8388736,rebeccapurple:6697881,red:16711680,rosybrown:12357519,royalblue:4286945,saddlebrown:9127187,salmon:16416882,sandybrown:16032864,seagreen:3050327,seashell:16774638,sienna:10506797,silver:12632256,skyblue:8900331,slateblue:6970061,slategray:7372944,slategrey:7372944,snow:16775930,springgreen:65407,steelblue:4620980,tan:13808780,teal:32896,thistle:14204888,tomato:16737095,turquoise:4251856,violet:15631086,wheat:16113331,white:16777215,whitesmoke:16119285,yellow:16776960,yellowgreen:10145074};function o_(){return this.rgb().formatHex()}function oC(){return this.rgb().formatRgb()}function oN(e){var t,r;return e=(e+"").trim().toLowerCase(),(t=oj.exec(e))?(r=t[1].length,t=parseInt(t[1],16),6===r?oD(t):3===r?new oL(t>>8&15|t>>4&240,t>>4&15|240&t,(15&t)<<4|15&t,1):8===r?oI(t>>24&255,t>>16&255,t>>8&255,(255&t)/255):4===r?oI(t>>12&15|t>>8&240,t>>8&15|t>>4&240,t>>4&15|240&t,((15&t)<<4|15&t)/255):null):(t=oS.exec(e))?new oL(t[1],t[2],t[3],1):(t=oP.exec(e))?new oL(255*t[1]/100,255*t[2]/100,255*t[3]/100,1):(t=oE.exec(e))?oI(t[1],t[2],t[3],t[4]):(t=ok.exec(e))?oI(255*t[1]/100,255*t[2]/100,255*t[3]/100,t[4]):(t=oA.exec(e))?oW(t[1],t[2]/100,t[3]/100,1):(t=oM.exec(e))?oW(t[1],t[2]/100,t[3]/100,t[4]):oT.hasOwnProperty(e)?oD(oT[e]):"transparent"===e?new oL(NaN,NaN,NaN,0):null}function oD(e){return new oL(e>>16&255,e>>8&255,255&e,1)}function oI(e,t,r,n){return n<=0&&(e=t=r=NaN),new oL(e,t,r,n)}function oR(e,t,r,n){var o;return 1==arguments.length?((o=e)instanceof ob||(o=oN(o)),o)?new oL((o=o.rgb()).r,o.g,o.b,o.opacity):new oL:new oL(e,t,r,null==n?1:n)}function oL(e,t,r,n){this.r=+e,this.g=+t,this.b=+r,this.opacity=+n}function oB(){return`#${o$(this.r)}${o$(this.g)}${o$(this.b)}`}function oF(){let e=oU(this.opacity);return`${1===e?"rgb(":"rgba("}${oz(this.r)}, ${oz(this.g)}, ${oz(this.b)}${1===e?")":`, ${e})`}`}function oU(e){return isNaN(e)?1:Math.max(0,Math.min(1,e))}function oz(e){return Math.max(0,Math.min(255,Math.round(e)||0))}function o$(e){return((e=oz(e))<16?"0":"")+e.toString(16)}function oW(e,t,r,n){return n<=0?e=t=r=NaN:r<=0||r>=1?e=t=NaN:t<=0&&(e=NaN),new oH(e,t,r,n)}function oq(e){if(e instanceof oH)return new oH(e.h,e.s,e.l,e.opacity);if(e instanceof ob||(e=oN(e)),!e)return new oH;if(e instanceof oH)return e;var t=(e=e.rgb()).r/255,r=e.g/255,n=e.b/255,o=Math.min(t,r,n),i=Math.max(t,r,n),a=NaN,u=i-o,c=(i+o)/2;return u?(a=t===i?(r-n)/u+(r<n)*6:r===i?(n-t)/u+2:(t-r)/u+4,u/=c<.5?i+o:2-i-o,a*=60):u=c>0&&c<1?0:a,new oH(a,u,c,e.opacity)}function oH(e,t,r,n){this.h=+e,this.s=+t,this.l=+r,this.opacity=+n}function oV(e){return(e=(e||0)%360)<0?e+360:e}function oX(e){return Math.max(0,Math.min(1,e||0))}function oZ(e,t,r){return(e<60?t+(r-t)*e/60:e<180?r:e<240?t+(r-t)*(240-e)/60:t)*255}function oG(e,t,r,n,o){var i=e*e,a=i*e;return((1-3*e+3*i-a)*t+(4-6*i+3*a)*r+(1+3*e+3*i-3*a)*n+a*o)/6}om(ob,oN,{copy(e){return Object.assign(new this.constructor,this,e)},displayable(){return this.rgb().displayable()},hex:o_,formatHex:o_,formatHex8:function(){return this.rgb().formatHex8()},formatHsl:function(){return oq(this).formatHsl()},formatRgb:oC,toString:oC}),om(oL,oR,og(ob,{brighter(e){return e=null==e?1.4285714285714286:Math.pow(1.4285714285714286,e),new oL(this.r*e,this.g*e,this.b*e,this.opacity)},darker(e){return e=null==e?.7:Math.pow(.7,e),new oL(this.r*e,this.g*e,this.b*e,this.opacity)},rgb(){return this},clamp(){return new oL(oz(this.r),oz(this.g),oz(this.b),oU(this.opacity))},displayable(){return -.5<=this.r&&this.r<255.5&&-.5<=this.g&&this.g<255.5&&-.5<=this.b&&this.b<255.5&&0<=this.opacity&&this.opacity<=1},hex:oB,formatHex:oB,formatHex8:function(){return`#${o$(this.r)}${o$(this.g)}${o$(this.b)}${o$((isNaN(this.opacity)?1:this.opacity)*255)}`},formatRgb:oF,toString:oF})),om(oH,function(e,t,r,n){return 1==arguments.length?oq(e):new oH(e,t,r,null==n?1:n)},og(ob,{brighter(e){return e=null==e?1.4285714285714286:Math.pow(1.4285714285714286,e),new oH(this.h,this.s,this.l*e,this.opacity)},darker(e){return e=null==e?.7:Math.pow(.7,e),new oH(this.h,this.s,this.l*e,this.opacity)},rgb(){var e=this.h%360+(this.h<0)*360,t=isNaN(e)||isNaN(this.s)?0:this.s,r=this.l,n=r+(r<.5?r:1-r)*t,o=2*r-n;return new oL(oZ(e>=240?e-240:e+120,o,n),oZ(e,o,n),oZ(e<120?e+240:e-120,o,n),this.opacity)},clamp(){return new oH(oV(this.h),oX(this.s),oX(this.l),oU(this.opacity))},displayable(){return(0<=this.s&&this.s<=1||isNaN(this.s))&&0<=this.l&&this.l<=1&&0<=this.opacity&&this.opacity<=1},formatHsl(){let e=oU(this.opacity);return`${1===e?"hsl(":"hsla("}${oV(this.h)}, ${100*oX(this.s)}%, ${100*oX(this.l)}%${1===e?")":`, ${e})`}`}}));let oY=e=>()=>e;function oK(e,t){var r=t-e;return r?function(t){return e+t*r}:oY(isNaN(e)?t:e)}let oQ=function e(t){var r,n=1==(r=+(r=t))?oK:function(e,t){var n,o,i;return t-e?(n=e,o=t,n=Math.pow(n,i=r),o=Math.pow(o,i)-n,i=1/i,function(e){return Math.pow(n+e*o,i)}):oY(isNaN(e)?t:e)};function o(e,t){var r=n((e=oR(e)).r,(t=oR(t)).r),o=n(e.g,t.g),i=n(e.b,t.b),a=oK(e.opacity,t.opacity);return function(t){return e.r=r(t),e.g=o(t),e.b=i(t),e.opacity=a(t),e+""}}return o.gamma=e,o}(1);function oJ(e){return function(t){var r,n,o=t.length,i=Array(o),a=Array(o),u=Array(o);for(r=0;r<o;++r)n=oR(t[r]),i[r]=n.r||0,a[r]=n.g||0,u[r]=n.b||0;return i=e(i),a=e(a),u=e(u),n.opacity=1,function(e){return n.r=i(e),n.g=a(e),n.b=u(e),n+""}}}function o0(e,t){return e=+e,t=+t,function(r){return e*(1-r)+t*r}}oJ(function(e){var t=e.length-1;return function(r){var n=r<=0?r=0:r>=1?(r=1,t-1):Math.floor(r*t),o=e[n],i=e[n+1],a=n>0?e[n-1]:2*o-i,u=n<t-1?e[n+2]:2*i-o;return oG((r-n/t)*t,a,o,i,u)}}),oJ(function(e){var t=e.length;return function(r){var n=Math.floor(((r%=1)<0?++r:r)*t),o=e[(n+t-1)%t],i=e[n%t],a=e[(n+1)%t],u=e[(n+2)%t];return oG((r-n/t)*t,o,i,a,u)}});var o1=/[-+]?(?:\d+\.?\d*|\.?\d+)(?:[eE][-+]?\d+)?/g,o2=RegExp(o1.source,"g");function o5(e,t){var r,n,o=typeof t;return null==t||"boolean"===o?oY(t):("number"===o?o0:"string"===o?(n=oN(t))?(t=n,oQ):function(e,t){var r,n,o,i,a,u=o1.lastIndex=o2.lastIndex=0,c=-1,l=[],s=[];for(e+="",t+="";(o=o1.exec(e))&&(i=o2.exec(t));)(a=i.index)>u&&(a=t.slice(u,a),l[c]?l[c]+=a:l[++c]=a),(o=o[0])===(i=i[0])?l[c]?l[c]+=i:l[++c]=i:(l[++c]=null,s.push({i:c,x:o0(o,i)})),u=o2.lastIndex;return u<t.length&&(a=t.slice(u),l[c]?l[c]+=a:l[++c]=a),l.length<2?s[0]?(r=s[0].x,function(e){return r(e)+""}):(n=t,function(){return n}):(t=s.length,function(e){for(var r,n=0;n<t;++n)l[(r=s[n]).i]=r.x(e);return l.join("")})}:t instanceof oN?oQ:t instanceof Date?function(e,t){var r=new Date;return e=+e,t=+t,function(n){return r.setTime(e*(1-n)+t*n),r}}:(r=t,!ArrayBuffer.isView(r)||r instanceof DataView)?Array.isArray(t)?function(e,t){var r,n=t?t.length:0,o=e?Math.min(n,e.length):0,i=Array(o),a=Array(n);for(r=0;r<o;++r)i[r]=o5(e[r],t[r]);for(;r<n;++r)a[r]=t[r];return function(e){for(r=0;r<o;++r)a[r]=i[r](e);return a}}:"function"!=typeof t.valueOf&&"function"!=typeof t.toString||isNaN(t)?function(e,t){var r,n={},o={};for(r in(null===e||"object"!=typeof e)&&(e={}),(null===t||"object"!=typeof t)&&(t={}),t)r in e?n[r]=o5(e[r],t[r]):o[r]=t[r];return function(e){for(r in n)o[r]=n[r](e);return o}}:o0:function(e,t){t||(t=[]);var r,n=e?Math.min(t.length,e.length):0,o=t.slice();return function(i){for(r=0;r<n;++r)o[r]=e[r]*(1-i)+t[r]*i;return o}})(e,t)}function o3(e,t){return e=+e,t=+t,function(r){return Math.round(e*(1-r)+t*r)}}function o4(e){return+e}var o6=[0,1];function o7(e){return e}function o8(e,t){var r;return(t-=e=+e)?function(r){return(r-e)/t}:(r=isNaN(t)?NaN:.5,function(){return r})}function o9(e,t,r){var n=e[0],o=e[1],i=t[0],a=t[1];return o<n?(n=o8(o,n),i=r(a,i)):(n=o8(n,o),i=r(i,a)),function(e){return i(n(e))}}function ie(e,t,r){var n=Math.min(e.length,t.length)-1,o=Array(n),i=Array(n),a=-1;for(e[n]<e[0]&&(e=e.slice().reverse(),t=t.slice().reverse());++a<n;)o[a]=o8(e[a],e[a+1]),i[a]=r(t[a],t[a+1]);return function(t){var r=ov(e,t,1,n)-1;return i[r](o[r](t))}}function it(e,t){return t.domain(e.domain()).range(e.range()).interpolate(e.interpolate()).clamp(e.clamp()).unknown(e.unknown())}function ir(){var e,t,r,n,o,i,a=o6,u=o6,c=o5,l=o7;function s(){var e,t,r,c=Math.min(a.length,u.length);return l!==o7&&(e=a[0],t=a[c-1],e>t&&(r=e,e=t,t=r),l=function(r){return Math.max(e,Math.min(t,r))}),n=c>2?ie:o9,o=i=null,f}function f(t){return null==t||isNaN(t=+t)?r:(o||(o=n(a.map(e),u,c)))(e(l(t)))}return f.invert=function(r){return l(t((i||(i=n(u,a.map(e),o0)))(r)))},f.domain=function(e){return arguments.length?(a=Array.from(e,o4),s()):a.slice()},f.range=function(e){return arguments.length?(u=Array.from(e),s()):u.slice()},f.rangeRound=function(e){return u=Array.from(e),c=o3,s()},f.clamp=function(e){return arguments.length?(l=!!e||o7,s()):l!==o7},f.interpolate=function(e){return arguments.length?(c=e,s()):c},f.unknown=function(e){return arguments.length?(r=e,f):r},function(r,n){return e=r,t=n,s()}}function io(){return ir()(o7,o7)}var ii=/^(?:(.)?([<>=^]))?([+\-( ])?([$#])?(0)?(\d+)?(,)?(\.\d+)?(~)?([a-z%])?$/i;function ia(e){var t;if(!(t=ii.exec(e)))throw Error("invalid format: "+e);return new iu({fill:t[1],align:t[2],sign:t[3],symbol:t[4],zero:t[5],width:t[6],comma:t[7],precision:t[8]&&t[8].slice(1),trim:t[9],type:t[10]})}function iu(e){this.fill=void 0===e.fill?" ":e.fill+"",this.align=void 0===e.align?">":e.align+"",this.sign=void 0===e.sign?"-":e.sign+"",this.symbol=void 0===e.symbol?"":e.symbol+"",this.zero=!!e.zero,this.width=void 0===e.width?void 0:+e.width,this.comma=!!e.comma,this.precision=void 0===e.precision?void 0:+e.precision,this.trim=!!e.trim,this.type=void 0===e.type?"":e.type+""}function ic(e,t){if((r=(e=t?e.toExponential(t-1):e.toExponential()).indexOf("e"))<0)return null;var r,n=e.slice(0,r);return[n.length>1?n[0]+n.slice(2):n,+e.slice(r+1)]}function il(e){return(e=ic(Math.abs(e)))?e[1]:NaN}function is(e,t){var r=ic(e,t);if(!r)return e+"";var n=r[0],o=r[1];return o<0?"0."+Array(-o).join("0")+n:n.length>o+1?n.slice(0,o+1)+"."+n.slice(o+1):n+Array(o-n.length+2).join("0")}ia.prototype=iu.prototype,iu.prototype.toString=function(){return this.fill+this.align+this.sign+this.symbol+(this.zero?"0":"")+(void 0===this.width?"":Math.max(1,0|this.width))+(this.comma?",":"")+(void 0===this.precision?"":"."+Math.max(0,0|this.precision))+(this.trim?"~":"")+this.type};let ip={"%":(e,t)=>(100*e).toFixed(t),b:e=>Math.round(e).toString(2),c:e=>e+"",d:function(e){return Math.abs(e=Math.round(e))>=1e21?e.toLocaleString("en").replace(/,/g,""):e.toString(10)},e:(e,t)=>e.toExponential(t),f:(e,t)=>e.toFixed(t),g:(e,t)=>e.toPrecision(t),o:e=>Math.round(e).toString(8),p:(e,t)=>is(100*e,t),r:is,s:function(e,t){var r=ic(e,t);if(!r)return e+"";var n=r[0],o=r[1],i=o-(uH=3*Math.max(-8,Math.min(8,Math.floor(o/3))))+1,a=n.length;return i===a?n:i>a?n+Array(i-a+1).join("0"):i>0?n.slice(0,i)+"."+n.slice(i):"0."+Array(1-i).join("0")+ic(e,Math.max(0,t+i-1))[0]},X:e=>Math.round(e).toString(16).toUpperCase(),x:e=>Math.round(e).toString(16)};function id(e){return e}var ih=Array.prototype.map,iy=["y","z","a","f","p","n","\xb5","m","","k","M","G","T","P","E","Z","Y"];function iv(e,t,r,n){var o,i,a=ol(e,t,r);switch((n=ia(null==n?",f":n)).type){case"s":var u=Math.max(Math.abs(e),Math.abs(t));return null!=n.precision||isNaN(i=Math.max(0,3*Math.max(-8,Math.min(8,Math.floor(il(u)/3)))-il(Math.abs(a))))||(n.precision=i),uZ(n,u);case"":case"e":case"g":case"p":case"r":null!=n.precision||isNaN(i=Math.max(0,il(Math.abs(Math.max(Math.abs(e),Math.abs(t)))-(o=Math.abs(o=a)))-il(o))+1)||(n.precision=i-("e"===n.type));break;case"f":case"%":null!=n.precision||isNaN(i=Math.max(0,-il(Math.abs(a))))||(n.precision=i-("%"===n.type)*2)}return uX(n)}function im(e){var t=e.domain;return e.ticks=function(e){var r=t();return ou(r[0],r[r.length-1],null==e?10:e)},e.tickFormat=function(e,r){var n=t();return iv(n[0],n[n.length-1],null==e?10:e,r)},e.nice=function(r){null==r&&(r=10);var n,o,i=t(),a=0,u=i.length-1,c=i[a],l=i[u],s=10;for(l<c&&(o=c,c=l,l=o,o=a,a=u,u=o);s-- >0;){if((o=oc(c,l,r))===n)return i[a]=c,i[u]=l,t(i);if(o>0)c=Math.floor(c/o)*o,l=Math.ceil(l/o)*o;else if(o<0)c=Math.ceil(c*o)/o,l=Math.floor(l*o)/o;else break;n=o}return e},e}function ig(){var e=io();return e.copy=function(){return it(e,ig())},nP.apply(e,arguments),im(e)}function ib(e,t){e=e.slice();var r,n=0,o=e.length-1,i=e[n],a=e[o];return a<i&&(r=n,n=o,o=r,r=i,i=a,a=r),e[n]=t.floor(i),e[o]=t.ceil(a),e}function ix(e){return Math.log(e)}function iw(e){return Math.exp(e)}function iO(e){return-Math.log(-e)}function ij(e){return-Math.exp(-e)}function iS(e){return isFinite(e)?+("1e"+e):e<0?0:e}function iP(e){return(t,r)=>-e(-t,r)}function iE(e){let t,r;let n=e(ix,iw),o=n.domain,i=10;function a(){var a,u;return t=(a=i)===Math.E?Math.log:10===a&&Math.log10||2===a&&Math.log2||(a=Math.log(a),e=>Math.log(e)/a),r=10===(u=i)?iS:u===Math.E?Math.exp:e=>Math.pow(u,e),o()[0]<0?(t=iP(t),r=iP(r),e(iO,ij)):e(ix,iw),n}return n.base=function(e){return arguments.length?(i=+e,a()):i},n.domain=function(e){return arguments.length?(o(e),a()):o()},n.ticks=e=>{let n,a;let u=o(),c=u[0],l=u[u.length-1],s=l<c;s&&([c,l]=[l,c]);let f=t(c),p=t(l),d=null==e?10:+e,h=[];if(!(i%1)&&p-f<d){if(f=Math.floor(f),p=Math.ceil(p),c>0){for(;f<=p;++f)for(n=1;n<i;++n)if(!((a=f<0?n/r(-f):n*r(f))<c)){if(a>l)break;h.push(a)}}else for(;f<=p;++f)for(n=i-1;n>=1;--n)if(!((a=f>0?n/r(-f):n*r(f))<c)){if(a>l)break;h.push(a)}2*h.length<d&&(h=ou(c,l,d))}else h=ou(f,p,Math.min(p-f,d)).map(r);return s?h.reverse():h},n.tickFormat=(e,o)=>{if(null==e&&(e=10),null==o&&(o=10===i?"s":","),"function"!=typeof o&&(i%1||null!=(o=ia(o)).precision||(o.trim=!0),o=uX(o)),e===1/0)return o;let a=Math.max(1,i*e/n.ticks().length);return e=>{let n=e/r(Math.round(t(e)));return n*i<i-.5&&(n*=i),n<=a?o(e):""}},n.nice=()=>o(ib(o(),{floor:e=>r(Math.floor(t(e))),ceil:e=>r(Math.ceil(t(e)))})),n}function ik(e){return function(t){return Math.sign(t)*Math.log1p(Math.abs(t/e))}}function iA(e){return function(t){return Math.sign(t)*Math.expm1(Math.abs(t))*e}}function iM(e){var t=1,r=e(ik(1),iA(t));return r.constant=function(r){return arguments.length?e(ik(t=+r),iA(t)):t},im(r)}function iT(e){return function(t){return t<0?-Math.pow(-t,e):Math.pow(t,e)}}function i_(e){return e<0?-Math.sqrt(-e):Math.sqrt(e)}function iC(e){return e<0?-e*e:e*e}function iN(e){var t=e(o7,o7),r=1;return t.exponent=function(t){return arguments.length?1==(r=+t)?e(o7,o7):.5===r?e(i_,iC):e(iT(r),iT(1/r)):r},im(t)}function iD(){var e=iN(ir());return e.copy=function(){return it(e,iD()).exponent(e.exponent())},nP.apply(e,arguments),e}function iI(){return iD.apply(null,arguments).exponent(.5)}function iR(e){return Math.sign(e)*e*e}function iL(e,t){let r;if(void 0===t)for(let t of e)null!=t&&(r<t||void 0===r&&t>=t)&&(r=t);else{let n=-1;for(let o of e)null!=(o=t(o,++n,e))&&(r<o||void 0===r&&o>=o)&&(r=o)}return r}function iB(e,t){let r;if(void 0===t)for(let t of e)null!=t&&(r>t||void 0===r&&t>=t)&&(r=t);else{let n=-1;for(let o of e)null!=(o=t(o,++n,e))&&(r>o||void 0===r&&o>=o)&&(r=o)}return r}function iF(e,t){return(null==e||!(e>=e))-(null==t||!(t>=t))||(e<t?-1:e>t?1:0)}function iU(e,t,r){let n=e[t];e[t]=e[r],e[r]=n}uX=(uV=function(e){var t,r,n,o=void 0===e.grouping||void 0===e.thousands?id:(t=ih.call(e.grouping,Number),r=e.thousands+"",function(e,n){for(var o=e.length,i=[],a=0,u=t[0],c=0;o>0&&u>0&&(c+u+1>n&&(u=Math.max(1,n-c)),i.push(e.substring(o-=u,o+u)),!((c+=u+1)>n));)u=t[a=(a+1)%t.length];return i.reverse().join(r)}),i=void 0===e.currency?"":e.currency[0]+"",a=void 0===e.currency?"":e.currency[1]+"",u=void 0===e.decimal?".":e.decimal+"",c=void 0===e.numerals?id:(n=ih.call(e.numerals,String),function(e){return e.replace(/[0-9]/g,function(e){return n[+e]})}),l=void 0===e.percent?"%":e.percent+"",s=void 0===e.minus?"−":e.minus+"",f=void 0===e.nan?"NaN":e.nan+"";function p(e){var t=(e=ia(e)).fill,r=e.align,n=e.sign,p=e.symbol,d=e.zero,h=e.width,y=e.comma,v=e.precision,m=e.trim,g=e.type;"n"===g?(y=!0,g="g"):ip[g]||(void 0===v&&(v=12),m=!0,g="g"),(d||"0"===t&&"="===r)&&(d=!0,t="0",r="=");var b="$"===p?i:"#"===p&&/[boxX]/.test(g)?"0"+g.toLowerCase():"",x="$"===p?a:/[%p]/.test(g)?l:"",w=ip[g],O=/[defgprs%]/.test(g);function j(e){var i,a,l,p=b,j=x;if("c"===g)j=w(e)+j,e="";else{var S=(e=+e)<0||1/e<0;if(e=isNaN(e)?f:w(Math.abs(e),v),m&&(e=function(e){e:for(var t,r=e.length,n=1,o=-1;n<r;++n)switch(e[n]){case".":o=t=n;break;case"0":0===o&&(o=n),t=n;break;default:if(!+e[n])break e;o>0&&(o=0)}return o>0?e.slice(0,o)+e.slice(t+1):e}(e)),S&&0==+e&&"+"!==n&&(S=!1),p=(S?"("===n?n:s:"-"===n||"("===n?"":n)+p,j=("s"===g?iy[8+uH/3]:"")+j+(S&&"("===n?")":""),O){for(i=-1,a=e.length;++i<a;)if(48>(l=e.charCodeAt(i))||l>57){j=(46===l?u+e.slice(i+1):e.slice(i))+j,e=e.slice(0,i);break}}}y&&!d&&(e=o(e,1/0));var P=p.length+e.length+j.length,E=P<h?Array(h-P+1).join(t):"";switch(y&&d&&(e=o(E+e,E.length?h-j.length:1/0),E=""),r){case"<":e=p+e+j+E;break;case"=":e=p+E+e+j;break;case"^":e=E.slice(0,P=E.length>>1)+p+e+j+E.slice(P);break;default:e=E+p+e+j}return c(e)}return v=void 0===v?6:/[gprs]/.test(g)?Math.max(1,Math.min(21,v)):Math.max(0,Math.min(20,v)),j.toString=function(){return e+""},j}return{format:p,formatPrefix:function(e,t){var r=p(((e=ia(e)).type="f",e)),n=3*Math.max(-8,Math.min(8,Math.floor(il(t)/3))),o=Math.pow(10,-n),i=iy[8+n/3];return function(e){return r(o*e)+i}}}}({thousands:",",grouping:[3],currency:["$",""]})).format,uZ=uV.formatPrefix;let iz=864e5,i$=7*iz,iW=30*iz,iq=365*iz,iH=new Date,iV=new Date;function iX(e,t,r,n){function o(t){return e(t=0==arguments.length?new Date:new Date(+t)),t}return o.floor=t=>(e(t=new Date(+t)),t),o.ceil=r=>(e(r=new Date(r-1)),t(r,1),e(r),r),o.round=e=>{let t=o(e),r=o.ceil(e);return e-t<r-e?t:r},o.offset=(e,r)=>(t(e=new Date(+e),null==r?1:Math.floor(r)),e),o.range=(r,n,i)=>{let a;let u=[];if(r=o.ceil(r),i=null==i?1:Math.floor(i),!(r<n)||!(i>0))return u;do u.push(a=new Date(+r)),t(r,i),e(r);while(a<r&&r<n);return u},o.filter=r=>iX(t=>{if(t>=t)for(;e(t),!r(t);)t.setTime(t-1)},(e,n)=>{if(e>=e){if(n<0)for(;++n<=0;)for(;t(e,-1),!r(e););else for(;--n>=0;)for(;t(e,1),!r(e););}}),r&&(o.count=(t,n)=>(iH.setTime(+t),iV.setTime(+n),e(iH),e(iV),Math.floor(r(iH,iV))),o.every=e=>isFinite(e=Math.floor(e))&&e>0?e>1?o.filter(n?t=>n(t)%e==0:t=>o.count(0,t)%e==0):o:null),o}let iZ=iX(()=>{},(e,t)=>{e.setTime(+e+t)},(e,t)=>t-e);iZ.every=e=>isFinite(e=Math.floor(e))&&e>0?e>1?iX(t=>{t.setTime(Math.floor(t/e)*e)},(t,r)=>{t.setTime(+t+r*e)},(t,r)=>(r-t)/e):iZ:null,iZ.range;let iG=iX(e=>{e.setTime(e-e.getMilliseconds())},(e,t)=>{e.setTime(+e+1e3*t)},(e,t)=>(t-e)/1e3,e=>e.getUTCSeconds());iG.range;let iY=iX(e=>{e.setTime(e-e.getMilliseconds()-1e3*e.getSeconds())},(e,t)=>{e.setTime(+e+6e4*t)},(e,t)=>(t-e)/6e4,e=>e.getMinutes());iY.range;let iK=iX(e=>{e.setUTCSeconds(0,0)},(e,t)=>{e.setTime(+e+6e4*t)},(e,t)=>(t-e)/6e4,e=>e.getUTCMinutes());iK.range;let iQ=iX(e=>{e.setTime(e-e.getMilliseconds()-1e3*e.getSeconds()-6e4*e.getMinutes())},(e,t)=>{e.setTime(+e+36e5*t)},(e,t)=>(t-e)/36e5,e=>e.getHours());iQ.range;let iJ=iX(e=>{e.setUTCMinutes(0,0,0)},(e,t)=>{e.setTime(+e+36e5*t)},(e,t)=>(t-e)/36e5,e=>e.getUTCHours());iJ.range;let i0=iX(e=>e.setHours(0,0,0,0),(e,t)=>e.setDate(e.getDate()+t),(e,t)=>(t-e-(t.getTimezoneOffset()-e.getTimezoneOffset())*6e4)/iz,e=>e.getDate()-1);i0.range;let i1=iX(e=>{e.setUTCHours(0,0,0,0)},(e,t)=>{e.setUTCDate(e.getUTCDate()+t)},(e,t)=>(t-e)/iz,e=>e.getUTCDate()-1);i1.range;let i2=iX(e=>{e.setUTCHours(0,0,0,0)},(e,t)=>{e.setUTCDate(e.getUTCDate()+t)},(e,t)=>(t-e)/iz,e=>Math.floor(e/iz));function i5(e){return iX(t=>{t.setDate(t.getDate()-(t.getDay()+7-e)%7),t.setHours(0,0,0,0)},(e,t)=>{e.setDate(e.getDate()+7*t)},(e,t)=>(t-e-(t.getTimezoneOffset()-e.getTimezoneOffset())*6e4)/i$)}i2.range;let i3=i5(0),i4=i5(1),i6=i5(2),i7=i5(3),i8=i5(4),i9=i5(5),ae=i5(6);function at(e){return iX(t=>{t.setUTCDate(t.getUTCDate()-(t.getUTCDay()+7-e)%7),t.setUTCHours(0,0,0,0)},(e,t)=>{e.setUTCDate(e.getUTCDate()+7*t)},(e,t)=>(t-e)/i$)}i3.range,i4.range,i6.range,i7.range,i8.range,i9.range,ae.range;let ar=at(0),an=at(1),ao=at(2),ai=at(3),aa=at(4),au=at(5),ac=at(6);ar.range,an.range,ao.range,ai.range,aa.range,au.range,ac.range;let al=iX(e=>{e.setDate(1),e.setHours(0,0,0,0)},(e,t)=>{e.setMonth(e.getMonth()+t)},(e,t)=>t.getMonth()-e.getMonth()+(t.getFullYear()-e.getFullYear())*12,e=>e.getMonth());al.range;let as=iX(e=>{e.setUTCDate(1),e.setUTCHours(0,0,0,0)},(e,t)=>{e.setUTCMonth(e.getUTCMonth()+t)},(e,t)=>t.getUTCMonth()-e.getUTCMonth()+(t.getUTCFullYear()-e.getUTCFullYear())*12,e=>e.getUTCMonth());as.range;let af=iX(e=>{e.setMonth(0,1),e.setHours(0,0,0,0)},(e,t)=>{e.setFullYear(e.getFullYear()+t)},(e,t)=>t.getFullYear()-e.getFullYear(),e=>e.getFullYear());af.every=e=>isFinite(e=Math.floor(e))&&e>0?iX(t=>{t.setFullYear(Math.floor(t.getFullYear()/e)*e),t.setMonth(0,1),t.setHours(0,0,0,0)},(t,r)=>{t.setFullYear(t.getFullYear()+r*e)}):null,af.range;let ap=iX(e=>{e.setUTCMonth(0,1),e.setUTCHours(0,0,0,0)},(e,t)=>{e.setUTCFullYear(e.getUTCFullYear()+t)},(e,t)=>t.getUTCFullYear()-e.getUTCFullYear(),e=>e.getUTCFullYear());function ad(e,t,r,n,o,i){let a=[[iG,1,1e3],[iG,5,5e3],[iG,15,15e3],[iG,30,3e4],[i,1,6e4],[i,5,3e5],[i,15,9e5],[i,30,18e5],[o,1,36e5],[o,3,108e5],[o,6,216e5],[o,12,432e5],[n,1,iz],[n,2,2*iz],[r,1,i$],[t,1,iW],[t,3,3*iW],[e,1,iq]];function u(t,r,n){let o=Math.abs(r-t)/n,i=op(([,,e])=>e).right(a,o);if(i===a.length)return e.every(ol(t/iq,r/iq,n));if(0===i)return iZ.every(Math.max(ol(t,r,n),1));let[u,c]=a[o/a[i-1][2]<a[i][2]/o?i-1:i];return u.every(c)}return[function(e,t,r){let n=t<e;n&&([e,t]=[t,e]);let o=r&&"function"==typeof r.range?r:u(e,t,r),i=o?o.range(e,+t+1):[];return n?i.reverse():i},u]}ap.every=e=>isFinite(e=Math.floor(e))&&e>0?iX(t=>{t.setUTCFullYear(Math.floor(t.getUTCFullYear()/e)*e),t.setUTCMonth(0,1),t.setUTCHours(0,0,0,0)},(t,r)=>{t.setUTCFullYear(t.getUTCFullYear()+r*e)}):null,ap.range;let[ah,ay]=ad(ap,as,ar,i2,iJ,iK),[av,am]=ad(af,al,i3,i0,iQ,iY);function ag(e){if(0<=e.y&&e.y<100){var t=new Date(-1,e.m,e.d,e.H,e.M,e.S,e.L);return t.setFullYear(e.y),t}return new Date(e.y,e.m,e.d,e.H,e.M,e.S,e.L)}function ab(e){if(0<=e.y&&e.y<100){var t=new Date(Date.UTC(-1,e.m,e.d,e.H,e.M,e.S,e.L));return t.setUTCFullYear(e.y),t}return new Date(Date.UTC(e.y,e.m,e.d,e.H,e.M,e.S,e.L))}function ax(e,t,r){return{y:e,m:t,d:r,H:0,M:0,S:0,L:0}}var aw={"-":"",_:" ",0:"0"},aO=/^\s*\d+/,aj=/^%/,aS=/[\\^$*+?|[\]().{}]/g;function aP(e,t,r){var n=e<0?"-":"",o=(n?-e:e)+"",i=o.length;return n+(i<r?Array(r-i+1).join(t)+o:o)}function aE(e){return e.replace(aS,"\\$&")}function ak(e){return RegExp("^(?:"+e.map(aE).join("|")+")","i")}function aA(e){return new Map(e.map((e,t)=>[e.toLowerCase(),t]))}function aM(e,t,r){var n=aO.exec(t.slice(r,r+1));return n?(e.w=+n[0],r+n[0].length):-1}function aT(e,t,r){var n=aO.exec(t.slice(r,r+1));return n?(e.u=+n[0],r+n[0].length):-1}function a_(e,t,r){var n=aO.exec(t.slice(r,r+2));return n?(e.U=+n[0],r+n[0].length):-1}function aC(e,t,r){var n=aO.exec(t.slice(r,r+2));return n?(e.V=+n[0],r+n[0].length):-1}function aN(e,t,r){var n=aO.exec(t.slice(r,r+2));return n?(e.W=+n[0],r+n[0].length):-1}function aD(e,t,r){var n=aO.exec(t.slice(r,r+4));return n?(e.y=+n[0],r+n[0].length):-1}function aI(e,t,r){var n=aO.exec(t.slice(r,r+2));return n?(e.y=+n[0]+(+n[0]>68?1900:2e3),r+n[0].length):-1}function aR(e,t,r){var n=/^(Z)|([+-]\d\d)(?::?(\d\d))?/.exec(t.slice(r,r+6));return n?(e.Z=n[1]?0:-(n[2]+(n[3]||"00")),r+n[0].length):-1}function aL(e,t,r){var n=aO.exec(t.slice(r,r+1));return n?(e.q=3*n[0]-3,r+n[0].length):-1}function aB(e,t,r){var n=aO.exec(t.slice(r,r+2));return n?(e.m=n[0]-1,r+n[0].length):-1}function aF(e,t,r){var n=aO.exec(t.slice(r,r+2));return n?(e.d=+n[0],r+n[0].length):-1}function aU(e,t,r){var n=aO.exec(t.slice(r,r+3));return n?(e.m=0,e.d=+n[0],r+n[0].length):-1}function az(e,t,r){var n=aO.exec(t.slice(r,r+2));return n?(e.H=+n[0],r+n[0].length):-1}function a$(e,t,r){var n=aO.exec(t.slice(r,r+2));return n?(e.M=+n[0],r+n[0].length):-1}function aW(e,t,r){var n=aO.exec(t.slice(r,r+2));return n?(e.S=+n[0],r+n[0].length):-1}function aq(e,t,r){var n=aO.exec(t.slice(r,r+3));return n?(e.L=+n[0],r+n[0].length):-1}function aH(e,t,r){var n=aO.exec(t.slice(r,r+6));return n?(e.L=Math.floor(n[0]/1e3),r+n[0].length):-1}function aV(e,t,r){var n=aj.exec(t.slice(r,r+1));return n?r+n[0].length:-1}function aX(e,t,r){var n=aO.exec(t.slice(r));return n?(e.Q=+n[0],r+n[0].length):-1}function aZ(e,t,r){var n=aO.exec(t.slice(r));return n?(e.s=+n[0],r+n[0].length):-1}function aG(e,t){return aP(e.getDate(),t,2)}function aY(e,t){return aP(e.getHours(),t,2)}function aK(e,t){return aP(e.getHours()%12||12,t,2)}function aQ(e,t){return aP(1+i0.count(af(e),e),t,3)}function aJ(e,t){return aP(e.getMilliseconds(),t,3)}function a0(e,t){return aJ(e,t)+"000"}function a1(e,t){return aP(e.getMonth()+1,t,2)}function a2(e,t){return aP(e.getMinutes(),t,2)}function a5(e,t){return aP(e.getSeconds(),t,2)}function a3(e){var t=e.getDay();return 0===t?7:t}function a4(e,t){return aP(i3.count(af(e)-1,e),t,2)}function a6(e){var t=e.getDay();return t>=4||0===t?i8(e):i8.ceil(e)}function a7(e,t){return e=a6(e),aP(i8.count(af(e),e)+(4===af(e).getDay()),t,2)}function a8(e){return e.getDay()}function a9(e,t){return aP(i4.count(af(e)-1,e),t,2)}function ue(e,t){return aP(e.getFullYear()%100,t,2)}function ut(e,t){return aP((e=a6(e)).getFullYear()%100,t,2)}function ur(e,t){return aP(e.getFullYear()%1e4,t,4)}function un(e,t){var r=e.getDay();return aP((e=r>=4||0===r?i8(e):i8.ceil(e)).getFullYear()%1e4,t,4)}function uo(e){var t=e.getTimezoneOffset();return(t>0?"-":(t*=-1,"+"))+aP(t/60|0,"0",2)+aP(t%60,"0",2)}function ui(e,t){return aP(e.getUTCDate(),t,2)}function ua(e,t){return aP(e.getUTCHours(),t,2)}function uu(e,t){return aP(e.getUTCHours()%12||12,t,2)}function uc(e,t){return aP(1+i1.count(ap(e),e),t,3)}function ul(e,t){return aP(e.getUTCMilliseconds(),t,3)}function us(e,t){return ul(e,t)+"000"}function uf(e,t){return aP(e.getUTCMonth()+1,t,2)}function up(e,t){return aP(e.getUTCMinutes(),t,2)}function ud(e,t){return aP(e.getUTCSeconds(),t,2)}function uh(e){var t=e.getUTCDay();return 0===t?7:t}function uy(e,t){return aP(ar.count(ap(e)-1,e),t,2)}function uv(e){var t=e.getUTCDay();return t>=4||0===t?aa(e):aa.ceil(e)}function um(e,t){return e=uv(e),aP(aa.count(ap(e),e)+(4===ap(e).getUTCDay()),t,2)}function ug(e){return e.getUTCDay()}function ub(e,t){return aP(an.count(ap(e)-1,e),t,2)}function ux(e,t){return aP(e.getUTCFullYear()%100,t,2)}function uw(e,t){return aP((e=uv(e)).getUTCFullYear()%100,t,2)}function uO(e,t){return aP(e.getUTCFullYear()%1e4,t,4)}function uj(e,t){var r=e.getUTCDay();return aP((e=r>=4||0===r?aa(e):aa.ceil(e)).getUTCFullYear()%1e4,t,4)}function uS(){return"+0000"}function uP(){return"%"}function uE(e){return+e}function uk(e){return Math.floor(+e/1e3)}function uA(e){return new Date(e)}function uM(e){return e instanceof Date?+e:+new Date(+e)}function uT(e,t,r,n,o,i,a,u,c,l){var s=io(),f=s.invert,p=s.domain,d=l(".%L"),h=l(":%S"),y=l("%I:%M"),v=l("%I %p"),m=l("%a %d"),g=l("%b %d"),b=l("%B"),x=l("%Y");function w(e){return(c(e)<e?d:u(e)<e?h:a(e)<e?y:i(e)<e?v:n(e)<e?o(e)<e?m:g:r(e)<e?b:x)(e)}return s.invert=function(e){return new Date(f(e))},s.domain=function(e){return arguments.length?p(Array.from(e,uM)):p().map(uA)},s.ticks=function(t){var r=p();return e(r[0],r[r.length-1],null==t?10:t)},s.tickFormat=function(e,t){return null==t?w:l(t)},s.nice=function(e){var r=p();return e&&"function"==typeof e.range||(e=t(r[0],r[r.length-1],null==e?10:e)),e?p(ib(r,e)):s},s.copy=function(){return it(s,uT(e,t,r,n,o,i,a,u,c,l))},s}function u_(){return nP.apply(uT(av,am,af,al,i3,i0,iQ,iY,iG,uY).domain([new Date(2e3,0,1),new Date(2e3,0,2)]),arguments)}function uC(){return nP.apply(uT(ah,ay,ap,as,ar,i1,iJ,iK,iG,uK).domain([Date.UTC(2e3,0,1),Date.UTC(2e3,0,2)]),arguments)}function uN(){var e,t,r,n,o,i=0,a=1,u=o7,c=!1;function l(t){return null==t||isNaN(t=+t)?o:u(0===r?.5:(t=(n(t)-e)*r,c?Math.max(0,Math.min(1,t)):t))}function s(e){return function(t){var r,n;return arguments.length?([r,n]=t,u=e(r,n),l):[u(0),u(1)]}}return l.domain=function(o){return arguments.length?([i,a]=o,e=n(i=+i),t=n(a=+a),r=e===t?0:1/(t-e),l):[i,a]},l.clamp=function(e){return arguments.length?(c=!!e,l):c},l.interpolator=function(e){return arguments.length?(u=e,l):u},l.range=s(o5),l.rangeRound=s(o3),l.unknown=function(e){return arguments.length?(o=e,l):o},function(o){return n=o,e=o(i),t=o(a),r=e===t?0:1/(t-e),l}}function uD(e,t){return t.domain(e.domain()).interpolator(e.interpolator()).clamp(e.clamp()).unknown(e.unknown())}function uI(){var e=iN(uN());return e.copy=function(){return uD(e,uI()).exponent(e.exponent())},nE.apply(e,arguments)}function uR(){return uI.apply(null,arguments).exponent(.5)}function uL(){var e,t,r,n,o,i,a,u=0,c=.5,l=1,s=1,f=o7,p=!1;function d(e){return isNaN(e=+e)?a:(e=.5+((e=+i(e))-t)*(s*e<s*t?n:o),f(p?Math.max(0,Math.min(1,e)):e))}function h(e){return function(t){var r,n,o;return arguments.length?([r,n,o]=t,f=function(e,t){void 0===t&&(t=e,e=o5);for(var r=0,n=t.length-1,o=t[0],i=Array(n<0?0:n);r<n;)i[r]=e(o,o=t[++r]);return function(e){var t=Math.max(0,Math.min(n-1,Math.floor(e*=n)));return i[t](e-t)}}(e,[r,n,o]),d):[f(0),f(.5),f(1)]}}return d.domain=function(a){return arguments.length?([u,c,l]=a,e=i(u=+u),t=i(c=+c),r=i(l=+l),n=e===t?0:.5/(t-e),o=t===r?0:.5/(r-t),s=t<e?-1:1,d):[u,c,l]},d.clamp=function(e){return arguments.length?(p=!!e,d):p},d.interpolator=function(e){return arguments.length?(f=e,d):f},d.range=h(o5),d.rangeRound=h(o3),d.unknown=function(e){return arguments.length?(a=e,d):a},function(a){return i=a,e=a(u),t=a(c),r=a(l),n=e===t?0:.5/(t-e),o=t===r?0:.5/(r-t),s=t<e?-1:1,d}}function uB(){var e=iN(uL());return e.copy=function(){return uD(e,uB()).exponent(e.exponent())},nE.apply(e,arguments)}function uF(){return uB.apply(null,arguments).exponent(.5)}function uU(e,t){if((o=e.length)>1)for(var r,n,o,i=1,a=e[t[0]],u=a.length;i<o;++i)for(n=a,a=e[t[i]],r=0;r<u;++r)a[r][1]+=a[r][0]=isNaN(n[r][1])?n[r][0]:n[r][1]}function uz(e){return"object"==typeof e&&"length"in e?e:Array.from(e)}function u$(e){for(var t=e.length,r=Array(t);--t>=0;)r[t]=t;return r}function uW(e,t){return e[t]}function uq(e){let t=[];return t.key=e,t}uY=(uG=function(e){var t=e.dateTime,r=e.date,n=e.time,o=e.periods,i=e.days,a=e.shortDays,u=e.months,c=e.shortMonths,l=ak(o),s=aA(o),f=ak(i),p=aA(i),d=ak(a),h=aA(a),y=ak(u),v=aA(u),m=ak(c),g=aA(c),b={a:function(e){return a[e.getDay()]},A:function(e){return i[e.getDay()]},b:function(e){return c[e.getMonth()]},B:function(e){return u[e.getMonth()]},c:null,d:aG,e:aG,f:a0,g:ut,G:un,H:aY,I:aK,j:aQ,L:aJ,m:a1,M:a2,p:function(e){return o[+(e.getHours()>=12)]},q:function(e){return 1+~~(e.getMonth()/3)},Q:uE,s:uk,S:a5,u:a3,U:a4,V:a7,w:a8,W:a9,x:null,X:null,y:ue,Y:ur,Z:uo,"%":uP},x={a:function(e){return a[e.getUTCDay()]},A:function(e){return i[e.getUTCDay()]},b:function(e){return c[e.getUTCMonth()]},B:function(e){return u[e.getUTCMonth()]},c:null,d:ui,e:ui,f:us,g:uw,G:uj,H:ua,I:uu,j:uc,L:ul,m:uf,M:up,p:function(e){return o[+(e.getUTCHours()>=12)]},q:function(e){return 1+~~(e.getUTCMonth()/3)},Q:uE,s:uk,S:ud,u:uh,U:uy,V:um,w:ug,W:ub,x:null,X:null,y:ux,Y:uO,Z:uS,"%":uP},w={a:function(e,t,r){var n=d.exec(t.slice(r));return n?(e.w=h.get(n[0].toLowerCase()),r+n[0].length):-1},A:function(e,t,r){var n=f.exec(t.slice(r));return n?(e.w=p.get(n[0].toLowerCase()),r+n[0].length):-1},b:function(e,t,r){var n=m.exec(t.slice(r));return n?(e.m=g.get(n[0].toLowerCase()),r+n[0].length):-1},B:function(e,t,r){var n=y.exec(t.slice(r));return n?(e.m=v.get(n[0].toLowerCase()),r+n[0].length):-1},c:function(e,r,n){return S(e,t,r,n)},d:aF,e:aF,f:aH,g:aI,G:aD,H:az,I:az,j:aU,L:aq,m:aB,M:a$,p:function(e,t,r){var n=l.exec(t.slice(r));return n?(e.p=s.get(n[0].toLowerCase()),r+n[0].length):-1},q:aL,Q:aX,s:aZ,S:aW,u:aT,U:a_,V:aC,w:aM,W:aN,x:function(e,t,n){return S(e,r,t,n)},X:function(e,t,r){return S(e,n,t,r)},y:aI,Y:aD,Z:aR,"%":aV};function O(e,t){return function(r){var n,o,i,a=[],u=-1,c=0,l=e.length;for(r instanceof Date||(r=new Date(+r));++u<l;)37===e.charCodeAt(u)&&(a.push(e.slice(c,u)),null!=(o=aw[n=e.charAt(++u)])?n=e.charAt(++u):o="e"===n?" ":"0",(i=t[n])&&(n=i(r,o)),a.push(n),c=u+1);return a.push(e.slice(c,u)),a.join("")}}function j(e,t){return function(r){var n,o,i=ax(1900,void 0,1);if(S(i,e,r+="",0)!=r.length)return null;if("Q"in i)return new Date(i.Q);if("s"in i)return new Date(1e3*i.s+("L"in i?i.L:0));if(!t||"Z"in i||(i.Z=0),"p"in i&&(i.H=i.H%12+12*i.p),void 0===i.m&&(i.m="q"in i?i.q:0),"V"in i){if(i.V<1||i.V>53)return null;"w"in i||(i.w=1),"Z"in i?(n=(o=(n=ab(ax(i.y,0,1))).getUTCDay())>4||0===o?an.ceil(n):an(n),n=i1.offset(n,(i.V-1)*7),i.y=n.getUTCFullYear(),i.m=n.getUTCMonth(),i.d=n.getUTCDate()+(i.w+6)%7):(n=(o=(n=ag(ax(i.y,0,1))).getDay())>4||0===o?i4.ceil(n):i4(n),n=i0.offset(n,(i.V-1)*7),i.y=n.getFullYear(),i.m=n.getMonth(),i.d=n.getDate()+(i.w+6)%7)}else("W"in i||"U"in i)&&("w"in i||(i.w="u"in i?i.u%7:"W"in i?1:0),o="Z"in i?ab(ax(i.y,0,1)).getUTCDay():ag(ax(i.y,0,1)).getDay(),i.m=0,i.d="W"in i?(i.w+6)%7+7*i.W-(o+5)%7:i.w+7*i.U-(o+6)%7);return"Z"in i?(i.H+=i.Z/100|0,i.M+=i.Z%100,ab(i)):ag(i)}}function S(e,t,r,n){for(var o,i,a=0,u=t.length,c=r.length;a<u;){if(n>=c)return -1;if(37===(o=t.charCodeAt(a++))){if(!(i=w[(o=t.charAt(a++))in aw?t.charAt(a++):o])||(n=i(e,r,n))<0)return -1}else if(o!=r.charCodeAt(n++))return -1}return n}return b.x=O(r,b),b.X=O(n,b),b.c=O(t,b),x.x=O(r,x),x.X=O(n,x),x.c=O(t,x),{format:function(e){var t=O(e+="",b);return t.toString=function(){return e},t},parse:function(e){var t=j(e+="",!1);return t.toString=function(){return e},t},utcFormat:function(e){var t=O(e+="",x);return t.toString=function(){return e},t},utcParse:function(e){var t=j(e+="",!0);return t.toString=function(){return e},t}}}({dateTime:"%x, %X",date:"%-m/%-d/%Y",time:"%-I:%M:%S %p",periods:["AM","PM"],days:["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"],shortDays:["Sun","Mon","Tue","Wed","Thu","Fri","Sat"],months:["January","February","March","April","May","June","July","August","September","October","November","December"],shortMonths:["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"]})).format,uG.parse,uK=uG.utcFormat,uG.utcParse,Array.prototype.slice;var uH,uV,uX,uZ,uG,uY,uK,uQ,uJ,u0=r(8172),u1=r.n(u0),u2=r(5140),u5=r.n(u2),u3=r(1969),u4=r.n(u3),u6=r(9154),u7=r.n(u6),u8=!0,u9="[DecimalError] ",ce=u9+"Invalid argument: ",ct=u9+"Exponent out of range: ",cr=Math.floor,cn=Math.pow,co=/^(\d+(\.\d*)?|\.\d+)(e[+-]?\d+)?$/i,ci=cr(1286742750677284.5),ca={};function cu(e,t){var r,n,o,i,a,u,c,l,s=e.constructor,f=s.precision;if(!e.s||!t.s)return t.s||(t=new s(e)),u8?cm(t,f):t;if(c=e.d,l=t.d,a=e.e,o=t.e,c=c.slice(),i=a-o){for(i<0?(n=c,i=-i,u=l.length):(n=l,o=a,u=c.length),i>(u=(a=Math.ceil(f/7))>u?a+1:u+1)&&(i=u,n.length=1),n.reverse();i--;)n.push(0);n.reverse()}for((u=c.length)-(i=l.length)<0&&(i=u,n=l,l=c,c=n),r=0;i;)r=(c[--i]=c[i]+l[i]+r)/1e7|0,c[i]%=1e7;for(r&&(c.unshift(r),++o),u=c.length;0==c[--u];)c.pop();return t.d=c,t.e=o,u8?cm(t,f):t}function cc(e,t,r){if(e!==~~e||e<t||e>r)throw Error(ce+e)}function cl(e){var t,r,n,o=e.length-1,i="",a=e[0];if(o>0){for(i+=a,t=1;t<o;t++)(r=7-(n=e[t]+"").length)&&(i+=ch(r)),i+=n;(r=7-(n=(a=e[t])+"").length)&&(i+=ch(r))}else if(0===a)return"0";for(;a%10==0;)a/=10;return i+a}ca.absoluteValue=ca.abs=function(){var e=new this.constructor(this);return e.s&&(e.s=1),e},ca.comparedTo=ca.cmp=function(e){var t,r,n,o;if(e=new this.constructor(e),this.s!==e.s)return this.s||-e.s;if(this.e!==e.e)return this.e>e.e^this.s<0?1:-1;for(t=0,r=(n=this.d.length)<(o=e.d.length)?n:o;t<r;++t)if(this.d[t]!==e.d[t])return this.d[t]>e.d[t]^this.s<0?1:-1;return n===o?0:n>o^this.s<0?1:-1},ca.decimalPlaces=ca.dp=function(){var e=this.d.length-1,t=(e-this.e)*7;if(e=this.d[e])for(;e%10==0;e/=10)t--;return t<0?0:t},ca.dividedBy=ca.div=function(e){return cs(this,new this.constructor(e))},ca.dividedToIntegerBy=ca.idiv=function(e){var t=this.constructor;return cm(cs(this,new t(e),0,1),t.precision)},ca.equals=ca.eq=function(e){return!this.cmp(e)},ca.exponent=function(){return cp(this)},ca.greaterThan=ca.gt=function(e){return this.cmp(e)>0},ca.greaterThanOrEqualTo=ca.gte=function(e){return this.cmp(e)>=0},ca.isInteger=ca.isint=function(){return this.e>this.d.length-2},ca.isNegative=ca.isneg=function(){return this.s<0},ca.isPositive=ca.ispos=function(){return this.s>0},ca.isZero=function(){return 0===this.s},ca.lessThan=ca.lt=function(e){return 0>this.cmp(e)},ca.lessThanOrEqualTo=ca.lte=function(e){return 1>this.cmp(e)},ca.logarithm=ca.log=function(e){var t,r=this.constructor,n=r.precision,o=n+5;if(void 0===e)e=new r(10);else if((e=new r(e)).s<1||e.eq(uJ))throw Error(u9+"NaN");if(this.s<1)throw Error(u9+(this.s?"NaN":"-Infinity"));return this.eq(uJ)?new r(0):(u8=!1,t=cs(cy(this,o),cy(e,o),o),u8=!0,cm(t,n))},ca.minus=ca.sub=function(e){return e=new this.constructor(e),this.s==e.s?cg(this,e):cu(this,(e.s=-e.s,e))},ca.modulo=ca.mod=function(e){var t,r=this.constructor,n=r.precision;if(!(e=new r(e)).s)throw Error(u9+"NaN");return this.s?(u8=!1,t=cs(this,e,0,1).times(e),u8=!0,this.minus(t)):cm(new r(this),n)},ca.naturalExponential=ca.exp=function(){return cf(this)},ca.naturalLogarithm=ca.ln=function(){return cy(this)},ca.negated=ca.neg=function(){var e=new this.constructor(this);return e.s=-e.s||0,e},ca.plus=ca.add=function(e){return e=new this.constructor(e),this.s==e.s?cu(this,e):cg(this,(e.s=-e.s,e))},ca.precision=ca.sd=function(e){var t,r,n;if(void 0!==e&&!!e!==e&&1!==e&&0!==e)throw Error(ce+e);if(t=cp(this)+1,r=7*(n=this.d.length-1)+1,n=this.d[n]){for(;n%10==0;n/=10)r--;for(n=this.d[0];n>=10;n/=10)r++}return e&&t>r?t:r},ca.squareRoot=ca.sqrt=function(){var e,t,r,n,o,i,a,u=this.constructor;if(this.s<1){if(!this.s)return new u(0);throw Error(u9+"NaN")}for(e=cp(this),u8=!1,0==(o=Math.sqrt(+this))||o==1/0?(((t=cl(this.d)).length+e)%2==0&&(t+="0"),o=Math.sqrt(t),e=cr((e+1)/2)-(e<0||e%2),n=new u(t=o==1/0?"5e"+e:(t=o.toExponential()).slice(0,t.indexOf("e")+1)+e)):n=new u(o.toString()),o=a=(r=u.precision)+3;;)if(n=(i=n).plus(cs(this,i,a+2)).times(.5),cl(i.d).slice(0,a)===(t=cl(n.d)).slice(0,a)){if(t=t.slice(a-3,a+1),o==a&&"4999"==t){if(cm(i,r+1,0),i.times(i).eq(this)){n=i;break}}else if("9999"!=t)break;a+=4}return u8=!0,cm(n,r)},ca.times=ca.mul=function(e){var t,r,n,o,i,a,u,c,l,s=this.constructor,f=this.d,p=(e=new s(e)).d;if(!this.s||!e.s)return new s(0);for(e.s*=this.s,r=this.e+e.e,(c=f.length)<(l=p.length)&&(i=f,f=p,p=i,a=c,c=l,l=a),i=[],n=a=c+l;n--;)i.push(0);for(n=l;--n>=0;){for(t=0,o=c+n;o>n;)u=i[o]+p[n]*f[o-n-1]+t,i[o--]=u%1e7|0,t=u/1e7|0;i[o]=(i[o]+t)%1e7|0}for(;!i[--a];)i.pop();return t?++r:i.shift(),e.d=i,e.e=r,u8?cm(e,s.precision):e},ca.toDecimalPlaces=ca.todp=function(e,t){var r=this,n=r.constructor;return(r=new n(r),void 0===e)?r:(cc(e,0,1e9),void 0===t?t=n.rounding:cc(t,0,8),cm(r,e+cp(r)+1,t))},ca.toExponential=function(e,t){var r,n=this,o=n.constructor;return void 0===e?r=cb(n,!0):(cc(e,0,1e9),void 0===t?t=o.rounding:cc(t,0,8),r=cb(n=cm(new o(n),e+1,t),!0,e+1)),r},ca.toFixed=function(e,t){var r,n,o=this.constructor;return void 0===e?cb(this):(cc(e,0,1e9),void 0===t?t=o.rounding:cc(t,0,8),r=cb((n=cm(new o(this),e+cp(this)+1,t)).abs(),!1,e+cp(n)+1),this.isneg()&&!this.isZero()?"-"+r:r)},ca.toInteger=ca.toint=function(){var e=this.constructor;return cm(new e(this),cp(this)+1,e.rounding)},ca.toNumber=function(){return+this},ca.toPower=ca.pow=function(e){var t,r,n,o,i,a,u=this,c=u.constructor,l=+(e=new c(e));if(!e.s)return new c(uJ);if(!(u=new c(u)).s){if(e.s<1)throw Error(u9+"Infinity");return u}if(u.eq(uJ))return u;if(n=c.precision,e.eq(uJ))return cm(u,n);if(a=(t=e.e)>=(r=e.d.length-1),i=u.s,a){if((r=l<0?-l:l)<=9007199254740991){for(o=new c(uJ),t=Math.ceil(n/7+4),u8=!1;r%2&&cx((o=o.times(u)).d,t),0!==(r=cr(r/2));)cx((u=u.times(u)).d,t);return u8=!0,e.s<0?new c(uJ).div(o):cm(o,n)}}else if(i<0)throw Error(u9+"NaN");return i=i<0&&1&e.d[Math.max(t,r)]?-1:1,u.s=1,u8=!1,o=e.times(cy(u,n+12)),u8=!0,(o=cf(o)).s=i,o},ca.toPrecision=function(e,t){var r,n,o=this,i=o.constructor;return void 0===e?(r=cp(o),n=cb(o,r<=i.toExpNeg||r>=i.toExpPos)):(cc(e,1,1e9),void 0===t?t=i.rounding:cc(t,0,8),r=cp(o=cm(new i(o),e,t)),n=cb(o,e<=r||r<=i.toExpNeg,e)),n},ca.toSignificantDigits=ca.tosd=function(e,t){var r=this.constructor;return void 0===e?(e=r.precision,t=r.rounding):(cc(e,1,1e9),void 0===t?t=r.rounding:cc(t,0,8)),cm(new r(this),e,t)},ca.toString=ca.valueOf=ca.val=ca.toJSON=ca[Symbol.for("nodejs.util.inspect.custom")]=function(){var e=cp(this),t=this.constructor;return cb(this,e<=t.toExpNeg||e>=t.toExpPos)};var cs=function(){function e(e,t){var r,n=0,o=e.length;for(e=e.slice();o--;)r=e[o]*t+n,e[o]=r%1e7|0,n=r/1e7|0;return n&&e.unshift(n),e}function t(e,t,r,n){var o,i;if(r!=n)i=r>n?1:-1;else for(o=i=0;o<r;o++)if(e[o]!=t[o]){i=e[o]>t[o]?1:-1;break}return i}function r(e,t,r){for(var n=0;r--;)e[r]-=n,n=e[r]<t[r]?1:0,e[r]=1e7*n+e[r]-t[r];for(;!e[0]&&e.length>1;)e.shift()}return function(n,o,i,a){var u,c,l,s,f,p,d,h,y,v,m,g,b,x,w,O,j,S,P=n.constructor,E=n.s==o.s?1:-1,k=n.d,A=o.d;if(!n.s)return new P(n);if(!o.s)throw Error(u9+"Division by zero");for(l=0,c=n.e-o.e,j=A.length,w=k.length,h=(d=new P(E)).d=[];A[l]==(k[l]||0);)++l;if(A[l]>(k[l]||0)&&--c,(g=null==i?i=P.precision:a?i+(cp(n)-cp(o))+1:i)<0)return new P(0);if(g=g/7+2|0,l=0,1==j)for(s=0,A=A[0],g++;(l<w||s)&&g--;l++)b=1e7*s+(k[l]||0),h[l]=b/A|0,s=b%A|0;else{for((s=1e7/(A[0]+1)|0)>1&&(A=e(A,s),k=e(k,s),j=A.length,w=k.length),x=j,v=(y=k.slice(0,j)).length;v<j;)y[v++]=0;(S=A.slice()).unshift(0),O=A[0],A[1]>=1e7/2&&++O;do s=0,(u=t(A,y,j,v))<0?(m=y[0],j!=v&&(m=1e7*m+(y[1]||0)),(s=m/O|0)>1?(s>=1e7&&(s=1e7-1),p=(f=e(A,s)).length,v=y.length,1==(u=t(f,y,p,v))&&(s--,r(f,j<p?S:A,p))):(0==s&&(u=s=1),f=A.slice()),(p=f.length)<v&&f.unshift(0),r(y,f,v),-1==u&&(v=y.length,(u=t(A,y,j,v))<1&&(s++,r(y,j<v?S:A,v))),v=y.length):0===u&&(s++,y=[0]),h[l++]=s,u&&y[0]?y[v++]=k[x]||0:(y=[k[x]],v=1);while((x++<w||void 0!==y[0])&&g--)}return h[0]||h.shift(),d.e=c,cm(d,a?i+cp(d)+1:i)}}();function cf(e,t){var r,n,o,i,a,u=0,c=0,l=e.constructor,s=l.precision;if(cp(e)>16)throw Error(ct+cp(e));if(!e.s)return new l(uJ);for(null==t?(u8=!1,a=s):a=t,i=new l(.03125);e.abs().gte(.1);)e=e.times(i),c+=5;for(a+=Math.log(cn(2,c))/Math.LN10*2+5|0,r=n=o=new l(uJ),l.precision=a;;){if(n=cm(n.times(e),a),r=r.times(++u),cl((i=o.plus(cs(n,r,a))).d).slice(0,a)===cl(o.d).slice(0,a)){for(;c--;)o=cm(o.times(o),a);return l.precision=s,null==t?(u8=!0,cm(o,s)):o}o=i}}function cp(e){for(var t=7*e.e,r=e.d[0];r>=10;r/=10)t++;return t}function cd(e,t,r){if(t>e.LN10.sd())throw u8=!0,r&&(e.precision=r),Error(u9+"LN10 precision limit exceeded");return cm(new e(e.LN10),t)}function ch(e){for(var t="";e--;)t+="0";return t}function cy(e,t){var r,n,o,i,a,u,c,l,s,f=1,p=e,d=p.d,h=p.constructor,y=h.precision;if(p.s<1)throw Error(u9+(p.s?"NaN":"-Infinity"));if(p.eq(uJ))return new h(0);if(null==t?(u8=!1,l=y):l=t,p.eq(10))return null==t&&(u8=!0),cd(h,l);if(l+=10,h.precision=l,n=(r=cl(d)).charAt(0),!(15e14>Math.abs(i=cp(p))))return c=cd(h,l+2,y).times(i+""),p=cy(new h(n+"."+r.slice(1)),l-10).plus(c),h.precision=y,null==t?(u8=!0,cm(p,y)):p;for(;n<7&&1!=n||1==n&&r.charAt(1)>3;)n=(r=cl((p=p.times(e)).d)).charAt(0),f++;for(i=cp(p),n>1?(p=new h("0."+r),i++):p=new h(n+"."+r.slice(1)),u=a=p=cs(p.minus(uJ),p.plus(uJ),l),s=cm(p.times(p),l),o=3;;){if(a=cm(a.times(s),l),cl((c=u.plus(cs(a,new h(o),l))).d).slice(0,l)===cl(u.d).slice(0,l))return u=u.times(2),0!==i&&(u=u.plus(cd(h,l+2,y).times(i+""))),u=cs(u,new h(f),l),h.precision=y,null==t?(u8=!0,cm(u,y)):u;u=c,o+=2}}function cv(e,t){var r,n,o;for((r=t.indexOf("."))>-1&&(t=t.replace(".","")),(n=t.search(/e/i))>0?(r<0&&(r=n),r+=+t.slice(n+1),t=t.substring(0,n)):r<0&&(r=t.length),n=0;48===t.charCodeAt(n);)++n;for(o=t.length;48===t.charCodeAt(o-1);)--o;if(t=t.slice(n,o)){if(o-=n,r=r-n-1,e.e=cr(r/7),e.d=[],n=(r+1)%7,r<0&&(n+=7),n<o){for(n&&e.d.push(+t.slice(0,n)),o-=7;n<o;)e.d.push(+t.slice(n,n+=7));n=7-(t=t.slice(n)).length}else n-=o;for(;n--;)t+="0";if(e.d.push(+t),u8&&(e.e>ci||e.e<-ci))throw Error(ct+r)}else e.s=0,e.e=0,e.d=[0];return e}function cm(e,t,r){var n,o,i,a,u,c,l,s,f=e.d;for(a=1,i=f[0];i>=10;i/=10)a++;if((n=t-a)<0)n+=7,o=t,l=f[s=0];else{if((s=Math.ceil((n+1)/7))>=(i=f.length))return e;for(a=1,l=i=f[s];i>=10;i/=10)a++;n%=7,o=n-7+a}if(void 0!==r&&(u=l/(i=cn(10,a-o-1))%10|0,c=t<0||void 0!==f[s+1]||l%i,c=r<4?(u||c)&&(0==r||r==(e.s<0?3:2)):u>5||5==u&&(4==r||c||6==r&&(n>0?o>0?l/cn(10,a-o):0:f[s-1])%10&1||r==(e.s<0?8:7))),t<1||!f[0])return c?(i=cp(e),f.length=1,t=t-i-1,f[0]=cn(10,(7-t%7)%7),e.e=cr(-t/7)||0):(f.length=1,f[0]=e.e=e.s=0),e;if(0==n?(f.length=s,i=1,s--):(f.length=s+1,i=cn(10,7-n),f[s]=o>0?(l/cn(10,a-o)%cn(10,o)|0)*i:0),c)for(;;){if(0==s){1e7==(f[0]+=i)&&(f[0]=1,++e.e);break}if(f[s]+=i,1e7!=f[s])break;f[s--]=0,i=1}for(n=f.length;0===f[--n];)f.pop();if(u8&&(e.e>ci||e.e<-ci))throw Error(ct+cp(e));return e}function cg(e,t){var r,n,o,i,a,u,c,l,s,f,p=e.constructor,d=p.precision;if(!e.s||!t.s)return t.s?t.s=-t.s:t=new p(e),u8?cm(t,d):t;if(c=e.d,f=t.d,n=t.e,l=e.e,c=c.slice(),a=l-n){for((s=a<0)?(r=c,a=-a,u=f.length):(r=f,n=l,u=c.length),a>(o=Math.max(Math.ceil(d/7),u)+2)&&(a=o,r.length=1),r.reverse(),o=a;o--;)r.push(0);r.reverse()}else{for((s=(o=c.length)<(u=f.length))&&(u=o),o=0;o<u;o++)if(c[o]!=f[o]){s=c[o]<f[o];break}a=0}for(s&&(r=c,c=f,f=r,t.s=-t.s),u=c.length,o=f.length-u;o>0;--o)c[u++]=0;for(o=f.length;o>a;){if(c[--o]<f[o]){for(i=o;i&&0===c[--i];)c[i]=1e7-1;--c[i],c[o]+=1e7}c[o]-=f[o]}for(;0===c[--u];)c.pop();for(;0===c[0];c.shift())--n;return c[0]?(t.d=c,t.e=n,u8?cm(t,d):t):new p(0)}function cb(e,t,r){var n,o=cp(e),i=cl(e.d),a=i.length;return t?(r&&(n=r-a)>0?i=i.charAt(0)+"."+i.slice(1)+ch(n):a>1&&(i=i.charAt(0)+"."+i.slice(1)),i=i+(o<0?"e":"e+")+o):o<0?(i="0."+ch(-o-1)+i,r&&(n=r-a)>0&&(i+=ch(n))):o>=a?(i+=ch(o+1-a),r&&(n=r-o-1)>0&&(i=i+"."+ch(n))):((n=o+1)<a&&(i=i.slice(0,n)+"."+i.slice(n)),r&&(n=r-a)>0&&(o+1===a&&(i+="."),i+=ch(n))),e.s<0?"-"+i:i}function cx(e,t){if(e.length>t)return e.length=t,!0}function cw(e){if(!e||"object"!=typeof e)throw Error(u9+"Object expected");var t,r,n,o=["precision",1,1e9,"rounding",0,8,"toExpNeg",-1/0,0,"toExpPos",0,1/0];for(t=0;t<o.length;t+=3)if(void 0!==(n=e[r=o[t]])){if(cr(n)===n&&n>=o[t+1]&&n<=o[t+2])this[r]=n;else throw Error(ce+r+": "+n)}if(void 0!==(n=e[r="LN10"])){if(n==Math.LN10)this[r]=new this(n);else throw Error(ce+r+": "+n)}return this}var uQ=function e(t){var r,n,o;function i(e){if(!(this instanceof i))return new i(e);if(this.constructor=i,e instanceof i){this.s=e.s,this.e=e.e,this.d=(e=e.d)?e.slice():e;return}if("number"==typeof e){if(0*e!=0)throw Error(ce+e);if(e>0)this.s=1;else if(e<0)e=-e,this.s=-1;else{this.s=0,this.e=0,this.d=[0];return}if(e===~~e&&e<1e7){this.e=0,this.d=[e];return}return cv(this,e.toString())}if("string"!=typeof e)throw Error(ce+e);if(45===e.charCodeAt(0)?(e=e.slice(1),this.s=-1):this.s=1,co.test(e))cv(this,e);else throw Error(ce+e)}if(i.prototype=ca,i.ROUND_UP=0,i.ROUND_DOWN=1,i.ROUND_CEIL=2,i.ROUND_FLOOR=3,i.ROUND_HALF_UP=4,i.ROUND_HALF_DOWN=5,i.ROUND_HALF_EVEN=6,i.ROUND_HALF_CEIL=7,i.ROUND_HALF_FLOOR=8,i.clone=e,i.config=i.set=cw,void 0===t&&(t={}),t)for(r=0,o=["precision","rounding","toExpNeg","toExpPos","LN10"];r<o.length;)t.hasOwnProperty(n=o[r++])||(t[n]=this[n]);return i.config(t),i}({precision:20,rounding:4,toExpNeg:-7,toExpPos:21,LN10:"2.302585092994045684017991454684364207601101488628772976033327900967572609677352480235997205089598298341967784042286"});uJ=new uQ(1);let cO=uQ;function cj(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=Array(t);r<t;r++)n[r]=e[r];return n}var cS=function(e){return e},cP={},cE=function(e){return e===cP},ck=function(e){return function t(){return 0==arguments.length||1==arguments.length&&cE(arguments.length<=0?void 0:arguments[0])?t:e.apply(void 0,arguments)}},cA=function(e){return function e(t,r){return 1===t?r:ck(function(){for(var n=arguments.length,o=Array(n),i=0;i<n;i++)o[i]=arguments[i];var a=o.filter(function(e){return e!==cP}).length;return a>=t?r.apply(void 0,o):e(t-a,ck(function(){for(var e=arguments.length,t=Array(e),n=0;n<e;n++)t[n]=arguments[n];var i=o.map(function(e){return cE(e)?t.shift():e});return r.apply(void 0,((function(e){if(Array.isArray(e))return cj(e)})(i)||function(e){if("undefined"!=typeof Symbol&&Symbol.iterator in Object(e))return Array.from(e)}(i)||function(e,t){if(e){if("string"==typeof e)return cj(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);if("Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r)return Array.from(e);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return cj(e,t)}}(i)||function(){throw TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()).concat(t))}))})}(e.length,e)},cM=function(e,t){for(var r=[],n=e;n<t;++n)r[n-e]=n;return r},cT=cA(function(e,t){return Array.isArray(t)?t.map(e):Object.keys(t).map(function(e){return t[e]}).map(e)}),c_=function(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];if(!t.length)return cS;var n=t.reverse(),o=n[0],i=n.slice(1);return function(){return i.reduce(function(e,t){return t(e)},o.apply(void 0,arguments))}},cC=function(e){return Array.isArray(e)?e.reverse():e.split("").reverse.join("")},cN=function(e){var t=null,r=null;return function(){for(var n=arguments.length,o=Array(n),i=0;i<n;i++)o[i]=arguments[i];return t&&o.every(function(e,r){return e===t[r]})?r:(t=o,r=e.apply(void 0,o))}},cD=cA(function(e,t,r){var n=+e;return n+r*(+t-n)}),cI=cA(function(e,t,r){var n=t-+e;return(r-e)/(n=n||1/0)}),cR=cA(function(e,t,r){var n=t-+e;return Math.max(0,Math.min(1,(r-e)/(n=n||1/0)))});let cL={rangeStep:function(e,t,r){for(var n=new cO(e),o=0,i=[];n.lt(t)&&o<1e5;)i.push(n.toNumber()),n=n.add(r),o++;return i},getDigitCount:function(e){return 0===e?1:Math.floor(new cO(e).abs().log(10).toNumber())+1},interpolateNumber:cD,uninterpolateNumber:cI,uninterpolateTruncation:cR};function cB(e){return function(e){if(Array.isArray(e))return cz(e)}(e)||function(e){if("undefined"!=typeof Symbol&&Symbol.iterator in Object(e))return Array.from(e)}(e)||cU(e)||function(){throw TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function cF(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){if("undefined"!=typeof Symbol&&Symbol.iterator in Object(e)){var r=[],n=!0,o=!1,i=void 0;try{for(var a,u=e[Symbol.iterator]();!(n=(a=u.next()).done)&&(r.push(a.value),!t||r.length!==t);n=!0);}catch(e){o=!0,i=e}finally{try{n||null==u.return||u.return()}finally{if(o)throw i}}return r}}(e,t)||cU(e,t)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function cU(e,t){if(e){if("string"==typeof e)return cz(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);if("Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r)return Array.from(e);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return cz(e,t)}}function cz(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=Array(t);r<t;r++)n[r]=e[r];return n}function c$(e){var t=cF(e,2),r=t[0],n=t[1],o=r,i=n;return r>n&&(o=n,i=r),[o,i]}function cW(e,t,r){if(e.lte(0))return new cO(0);var n=cL.getDigitCount(e.toNumber()),o=new cO(10).pow(n),i=e.div(o),a=1!==n?.05:.1,u=new cO(Math.ceil(i.div(a).toNumber())).add(r).mul(a).mul(o);return t?u:new cO(Math.ceil(u))}function cq(e,t,r){var n=1,o=new cO(e);if(!o.isint()&&r){var i=Math.abs(e);i<1?(n=new cO(10).pow(cL.getDigitCount(e)-1),o=new cO(Math.floor(o.div(n).toNumber())).mul(n)):i>1&&(o=new cO(Math.floor(e)))}else 0===e?o=new cO(Math.floor((t-1)/2)):r||(o=new cO(Math.floor(e)));var a=Math.floor((t-1)/2);return c_(cT(function(e){return o.add(new cO(e-a).mul(n)).toNumber()}),cM)(0,t)}var cH=cN(function(e){var t=cF(e,2),r=t[0],n=t[1],o=arguments.length>1&&void 0!==arguments[1]?arguments[1]:6,i=!(arguments.length>2)||void 0===arguments[2]||arguments[2],a=Math.max(o,2),u=cF(c$([r,n]),2),c=u[0],l=u[1];if(c===-1/0||l===1/0){var s=l===1/0?[c].concat(cB(cM(0,o-1).map(function(){return 1/0}))):[].concat(cB(cM(0,o-1).map(function(){return-1/0})),[l]);return r>n?cC(s):s}if(c===l)return cq(c,o,i);var f=function e(t,r,n,o){var i,a=arguments.length>4&&void 0!==arguments[4]?arguments[4]:0;if(!Number.isFinite((r-t)/(n-1)))return{step:new cO(0),tickMin:new cO(0),tickMax:new cO(0)};var u=cW(new cO(r).sub(t).div(n-1),o,a),c=Math.ceil((i=t<=0&&r>=0?new cO(0):(i=new cO(t).add(r).div(2)).sub(new cO(i).mod(u))).sub(t).div(u).toNumber()),l=Math.ceil(new cO(r).sub(i).div(u).toNumber()),s=c+l+1;return s>n?e(t,r,n,o,a+1):(s<n&&(l=r>0?l+(n-s):l,c=r>0?c:c+(n-s)),{step:u,tickMin:i.sub(new cO(c).mul(u)),tickMax:i.add(new cO(l).mul(u))})}(c,l,a,i),p=f.step,d=f.tickMin,h=f.tickMax,y=cL.rangeStep(d,h.add(new cO(.1).mul(p)),p);return r>n?cC(y):y});cN(function(e){var t=cF(e,2),r=t[0],n=t[1],o=arguments.length>1&&void 0!==arguments[1]?arguments[1]:6,i=!(arguments.length>2)||void 0===arguments[2]||arguments[2],a=Math.max(o,2),u=cF(c$([r,n]),2),c=u[0],l=u[1];if(c===-1/0||l===1/0)return[r,n];if(c===l)return cq(c,o,i);var s=cW(new cO(l).sub(c).div(a-1),i,0),f=c_(cT(function(e){return new cO(c).add(new cO(e).mul(s)).toNumber()}),cM)(0,a).filter(function(e){return e>=c&&e<=l});return r>n?cC(f):f});var cV=cN(function(e,t){var r=cF(e,2),n=r[0],o=r[1],i=!(arguments.length>2)||void 0===arguments[2]||arguments[2],a=cF(c$([n,o]),2),u=a[0],c=a[1];if(u===-1/0||c===1/0)return[n,o];if(u===c)return[u];var l=cW(new cO(c).sub(u).div(Math.max(t,2)-1),i,0),s=[].concat(cB(cL.rangeStep(new cO(u),new cO(c).sub(new cO(.99).mul(l)),l)),[c]);return n>o?cC(s):s}),cX=["offset","layout","width","dataKey","data","dataPointFormatter","xAxis","yAxis"];function cZ(e){return(cZ="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function cG(){return(cG=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(this,arguments)}function cY(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=Array(t);r<t;r++)n[r]=e[r];return n}function cK(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,c2(n.key),n)}}function cQ(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(e){}return(cQ=function(){return!!e})()}function cJ(e){return(cJ=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)})(e)}function c0(e,t){return(c0=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e})(e,t)}function c1(e,t,r){return(t=c2(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function c2(e){var t=function(e,t){if("object"!=cZ(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=cZ(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==cZ(t)?t:t+""}var c5=function(e){var t,r;function n(){var e,t;return function(e,t){if(!(e instanceof t))throw TypeError("Cannot call a class as a function")}(this,n),e=n,t=arguments,e=cJ(e),function(e,t){if(t&&("object"===cZ(t)||"function"==typeof t))return t;if(void 0!==t)throw TypeError("Derived constructors may only return object or undefined");return function(e){if(void 0===e)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return e}(e)}(this,cQ()?Reflect.construct(e,t||[],cJ(this).constructor):e.apply(this,t))}return function(e,t){if("function"!=typeof t&&null!==t)throw TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&c0(e,t)}(n,e),t=[{key:"render",value:function(){var e=this.props,t=e.offset,r=e.layout,n=e.width,o=e.dataKey,i=e.data,a=e.dataPointFormatter,u=e.xAxis,c=e.yAxis,s=eb(function(e,t){if(null==e)return{};var r,n,o=function(e,t){if(null==e)return{};var r={};for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){if(t.indexOf(n)>=0)continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(n=0;n<i.length;n++)r=i[n],!(t.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(e,r)&&(o[r]=e[r])}return o}(e,cX),!1);"x"===this.props.direction&&"number"!==u.type&&eN(!1);var f=i.map(function(e){var i,f,p=a(e,o),d=p.x,h=p.y,y=p.value,v=p.errorVal;if(!v)return null;var m=[];if(Array.isArray(v)){var g=function(e){if(Array.isArray(e))return e}(v)||function(e,t){var r=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=r){var n,o,i,a,u=[],c=!0,l=!1;try{if(i=(r=r.call(e)).next,0===t){if(Object(r)!==r)return;c=!1}else for(;!(c=(n=i.call(r)).done)&&(u.push(n.value),u.length!==t);c=!0);}catch(e){l=!0,o=e}finally{try{if(!c&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(l)throw o}}return u}}(v,2)||function(e,t){if(e){if("string"==typeof e)return cY(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);if("Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r)return Array.from(e);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return cY(e,t)}}(v,2)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}();i=g[0],f=g[1]}else i=f=v;if("vertical"===r){var b=u.scale,x=h+t,w=x+n,O=x-n,j=b(y-i),S=b(y+f);m.push({x1:S,y1:w,x2:S,y2:O}),m.push({x1:j,y1:x,x2:S,y2:x}),m.push({x1:j,y1:w,x2:j,y2:O})}else if("horizontal"===r){var P=c.scale,E=d+t,k=E-n,A=E+n,M=P(y-i),T=P(y+f);m.push({x1:k,y1:T,x2:A,y2:T}),m.push({x1:E,y1:M,x2:E,y2:T}),m.push({x1:k,y1:M,x2:A,y2:M})}return l().createElement(eF,cG({className:"recharts-errorBar",key:"bar-".concat(m.map(function(e){return"".concat(e.x1,"-").concat(e.x2,"-").concat(e.y1,"-").concat(e.y2)}))},s),m.map(function(e){return l().createElement("line",cG({},e,{key:"line-".concat(e.x1,"-").concat(e.x2,"-").concat(e.y1,"-").concat(e.y2)}))}))});return l().createElement(eF,{className:"recharts-errorBars"},f)}}],cK(n.prototype,t),r&&cK(n,r),Object.defineProperty(n,"prototype",{writable:!1}),n}(l().Component);function c3(e){return(c3="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function c4(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function c6(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?c4(Object(r),!0).forEach(function(t){var n,o;n=t,o=r[t],(n=function(e){var t=function(e,t){if("object"!=c3(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=c3(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==c3(t)?t:t+""}(n))in e?Object.defineProperty(e,n,{value:o,enumerable:!0,configurable:!0,writable:!0}):e[n]=o}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):c4(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}c1(c5,"defaultProps",{stroke:"black",strokeWidth:1.5,width:5,offset:0,layout:"horizontal"}),c1(c5,"displayName","ErrorBar");var c7=function(e){var t,r=e.children,n=e.formattedGraphicalItems,o=e.legendWidth,i=e.legendContent,a=ey(r,rn);if(!a)return null;var u=rn.defaultProps,c=void 0!==u?c6(c6({},u),a.props):{};return t=a.props&&a.props.payload?a.props&&a.props.payload:"children"===i?(n||[]).reduce(function(e,t){var r=t.item,n=t.props,o=n.sectors||n.data||[];return e.concat(o.map(function(e){return{type:a.props.iconType||r.props.legendType,value:e.name,color:e.fill,payload:e}}))},[]):(n||[]).map(function(e){var t=e.item,r=t.type.defaultProps,n=void 0!==r?c6(c6({},r),t.props):{},o=n.dataKey,i=n.name,a=n.legendType;return{inactive:n.hide,dataKey:o,type:c.iconType||a||"square",color:lu(t),value:i||o,payload:n}}),c6(c6(c6({},c),rn.getWithHeight(a,o)),{},{payload:t,item:a})};function c8(e){return(c8="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function c9(e){return function(e){if(Array.isArray(e))return le(e)}(e)||function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(e)||function(e,t){if(e){if("string"==typeof e)return le(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);if("Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r)return Array.from(e);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return le(e,t)}}(e)||function(){throw TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function le(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=Array(t);r<t;r++)n[r]=e[r];return n}function lt(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function lr(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?lt(Object(r),!0).forEach(function(t){ln(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):lt(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function ln(e,t,r){var n;return(n=function(e,t){if("object"!=c8(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=c8(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(t,"string"),(t="symbol"==c8(n)?n:n+"")in e)?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function lo(e,t,r){return H()(e)||H()(t)?r:D(t)?A()(e,t,r):X()(t)?t(e):r}function li(e,t,r,n){var o=u4()(e,function(e){return lo(e,t)});if("number"===r){var i=o.filter(function(e){return N(e)||parseFloat(e)});return i.length?[u5()(i),u1()(i)]:[1/0,-1/0]}return(n?o.filter(function(e){return!H()(e)}):o).map(function(e){return D(e)||e instanceof Date?e:""})}var la=function(e){var t,r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:[],n=arguments.length>2?arguments[2]:void 0,o=arguments.length>3?arguments[3]:void 0,i=-1,a=null!==(t=null==r?void 0:r.length)&&void 0!==t?t:0;if(a<=1)return 0;if(o&&"angleAxis"===o.axisType&&1e-6>=Math.abs(Math.abs(o.range[1]-o.range[0])-360))for(var u=o.range,c=0;c<a;c++){var l=c>0?n[c-1].coordinate:n[a-1].coordinate,s=n[c].coordinate,f=c>=a-1?n[0].coordinate:n[c+1].coordinate,p=void 0;if(_(s-l)!==_(f-s)){var d=[];if(_(f-s)===_(u[1]-u[0])){p=f;var h=s+u[1]-u[0];d[0]=Math.min(h,(h+l)/2),d[1]=Math.max(h,(h+l)/2)}else{p=l;var y=f+u[1]-u[0];d[0]=Math.min(s,(y+s)/2),d[1]=Math.max(s,(y+s)/2)}var v=[Math.min(s,(p+s)/2),Math.max(s,(p+s)/2)];if(e>v[0]&&e<=v[1]||e>=d[0]&&e<=d[1]){i=n[c].index;break}}else{var m=Math.min(l,f),g=Math.max(l,f);if(e>(m+s)/2&&e<=(g+s)/2){i=n[c].index;break}}}else for(var b=0;b<a;b++)if(0===b&&e<=(r[b].coordinate+r[b+1].coordinate)/2||b>0&&b<a-1&&e>(r[b].coordinate+r[b-1].coordinate)/2&&e<=(r[b].coordinate+r[b+1].coordinate)/2||b===a-1&&e>(r[b].coordinate+r[b-1].coordinate)/2){i=r[b].index;break}return i},lu=function(e){var t,r,n=e.type.displayName,o=null!==(t=e.type)&&void 0!==t&&t.defaultProps?lr(lr({},e.type.defaultProps),e.props):e.props,i=o.stroke,a=o.fill;switch(n){case"Line":r=i;break;case"Area":case"Radar":r=i&&"none"!==i?i:a;break;default:r=a}return r},lc=function(e){var t=e.barSize,r=e.totalSize,n=e.stackGroups,o=void 0===n?{}:n;if(!o)return{};for(var i={},a=Object.keys(o),u=0,c=a.length;u<c;u++)for(var l=o[a[u]].stackGroups,s=Object.keys(l),f=0,p=s.length;f<p;f++){var d=l[s[f]],h=d.items,y=d.cateAxisId,v=h.filter(function(e){return es(e.type).indexOf("Bar")>=0});if(v&&v.length){var m=v[0].type.defaultProps,g=void 0!==m?lr(lr({},m),v[0].props):v[0].props,b=g.barSize,x=g[y];i[x]||(i[x]=[]);var w=H()(b)?t:b;i[x].push({item:v[0],stackList:v.slice(1),barSize:H()(w)?void 0:L(w,r,0)})}}return i},ll=function(e){var t,r=e.barGap,n=e.barCategoryGap,o=e.bandSize,i=e.sizeList,a=void 0===i?[]:i,u=e.maxBarSize,c=a.length;if(c<1)return null;var l=L(r,o,0,!0),s=[];if(a[0].barSize===+a[0].barSize){var f=!1,p=o/c,d=a.reduce(function(e,t){return e+t.barSize||0},0);(d+=(c-1)*l)>=o&&(d-=(c-1)*l,l=0),d>=o&&p>0&&(f=!0,p*=.9,d=c*p);var h={offset:((o-d)/2>>0)-l,size:0};t=a.reduce(function(e,t){var r={item:t.item,position:{offset:h.offset+h.size+l,size:f?p:t.barSize}},n=[].concat(c9(e),[r]);return h=n[n.length-1].position,t.stackList&&t.stackList.length&&t.stackList.forEach(function(e){n.push({item:e,position:h})}),n},s)}else{var y=L(n,o,0,!0);o-2*y-(c-1)*l<=0&&(l=0);var v=(o-2*y-(c-1)*l)/c;v>1&&(v>>=0);var m=u===+u?Math.min(v,u):v;t=a.reduce(function(e,t,r){var n=[].concat(c9(e),[{item:t.item,position:{offset:y+(v+l)*r+(v-m)/2,size:m}}]);return t.stackList&&t.stackList.length&&t.stackList.forEach(function(e){n.push({item:e,position:n[n.length-1].position})}),n},s)}return t},ls=function(e,t,r,n){var o=r.children,i=r.width,a=r.margin,u=c7({children:o,legendWidth:i-(a.left||0)-(a.right||0)});if(u){var c=n||{},l=c.width,s=c.height,f=u.align,p=u.verticalAlign,d=u.layout;if(("vertical"===d||"horizontal"===d&&"middle"===p)&&"center"!==f&&N(e[f]))return lr(lr({},e),{},ln({},f,e[f]+(l||0)));if(("horizontal"===d||"vertical"===d&&"center"===f)&&"middle"!==p&&N(e[p]))return lr(lr({},e),{},ln({},p,e[p]+(s||0)))}return e},lf=function(e,t,r,n,o){var i=eh(t.props.children,c5).filter(function(e){var t;return t=e.props.direction,!!H()(o)||("horizontal"===n?"yAxis"===o:"vertical"===n||"x"===t?"xAxis"===o:"y"!==t||"yAxis"===o)});if(i&&i.length){var a=i.map(function(e){return e.props.dataKey});return e.reduce(function(e,t){var n=lo(t,r);if(H()(n))return e;var o=Array.isArray(n)?[u5()(n),u1()(n)]:[n,n],i=a.reduce(function(e,r){var n=lo(t,r,0),i=o[0]-Math.abs(Array.isArray(n)?n[0]:n),a=o[1]+Math.abs(Array.isArray(n)?n[1]:n);return[Math.min(i,e[0]),Math.max(a,e[1])]},[1/0,-1/0]);return[Math.min(i[0],e[0]),Math.max(i[1],e[1])]},[1/0,-1/0])}return null},lp=function(e,t,r,n,o){var i=t.map(function(t){return lf(e,t,r,o,n)}).filter(function(e){return!H()(e)});return i&&i.length?i.reduce(function(e,t){return[Math.min(e[0],t[0]),Math.max(e[1],t[1])]},[1/0,-1/0]):null},ld=function(e,t,r,n,o){var i=t.map(function(t){var i=t.props.dataKey;return"number"===r&&i&&lf(e,t,i,n)||li(e,i,r,o)});if("number"===r)return i.reduce(function(e,t){return[Math.min(e[0],t[0]),Math.max(e[1],t[1])]},[1/0,-1/0]);var a={};return i.reduce(function(e,t){for(var r=0,n=t.length;r<n;r++)a[t[r]]||(a[t[r]]=!0,e.push(t[r]));return e},[])},lh=function(e,t){return"horizontal"===e&&"xAxis"===t||"vertical"===e&&"yAxis"===t||"centric"===e&&"angleAxis"===t||"radial"===e&&"radiusAxis"===t},ly=function(e,t,r,n){if(n)return e.map(function(e){return e.coordinate});var o,i,a=e.map(function(e){return e.coordinate===t&&(o=!0),e.coordinate===r&&(i=!0),e.coordinate});return o||a.push(t),i||a.push(r),a},lv=function(e,t,r){if(!e)return null;var n=e.scale,o=e.duplicateDomain,i=e.type,a=e.range,u="scaleBand"===e.realScaleType?n.bandwidth()/2:2,c=(t||r)&&"category"===i&&n.bandwidth?n.bandwidth()/u:0;return(c="angleAxis"===e.axisType&&(null==a?void 0:a.length)>=2?2*_(a[0]-a[1])*c:c,t&&(e.ticks||e.niceTicks))?(e.ticks||e.niceTicks).map(function(e){return{coordinate:n(o?o.indexOf(e):e)+c,value:e,offset:c}}).filter(function(e){return!E()(e.coordinate)}):e.isCategorical&&e.categoricalDomain?e.categoricalDomain.map(function(e,t){return{coordinate:n(e)+c,value:e,index:t,offset:c}}):n.ticks&&!r?n.ticks(e.tickCount).map(function(e){return{coordinate:n(e)+c,value:e,offset:c}}):n.domain().map(function(e,t){return{coordinate:n(e)+c,value:o?o[e]:e,index:t,offset:c}})},lm=new WeakMap,lg=function(e,t){if("function"!=typeof t)return e;lm.has(e)||lm.set(e,new WeakMap);var r=lm.get(e);if(r.has(t))return r.get(t);var n=function(){e.apply(void 0,arguments),t.apply(void 0,arguments)};return r.set(t,n),n},lb=function(e,t,r){var o=e.scale,i=e.type,a=e.layout,u=e.axisType;if("auto"===o)return"radial"===a&&"radiusAxis"===u?{scale:nC(),realScaleType:"band"}:"radial"===a&&"angleAxis"===u?{scale:ig(),realScaleType:"linear"}:"category"===i&&t&&(t.indexOf("LineChart")>=0||t.indexOf("AreaChart")>=0||t.indexOf("ComposedChart")>=0&&!r)?{scale:nN(),realScaleType:"point"}:"category"===i?{scale:nC(),realScaleType:"band"}:{scale:ig(),realScaleType:"linear"};if(S()(o)){var c="scale".concat(th()(o));return{scale:(n[c]||nN)(),realScaleType:n[c]?c:"point"}}return X()(o)?{scale:o}:{scale:nN(),realScaleType:"point"}},lx=function(e){var t=e.domain();if(t&&!(t.length<=2)){var r=t.length,n=e.range(),o=Math.min(n[0],n[1])-1e-4,i=Math.max(n[0],n[1])+1e-4,a=e(t[0]),u=e(t[r-1]);(a<o||a>i||u<o||u>i)&&e.domain([t[0],t[r-1]])}},lw=function(e,t){if(!e)return null;for(var r=0,n=e.length;r<n;r++)if(e[r].item===t)return e[r].position;return null},lO=function(e,t){if(!t||2!==t.length||!N(t[0])||!N(t[1]))return e;var r=Math.min(t[0],t[1]),n=Math.max(t[0],t[1]),o=[e[0],e[1]];return(!N(e[0])||e[0]<r)&&(o[0]=r),(!N(e[1])||e[1]>n)&&(o[1]=n),o[0]>n&&(o[0]=n),o[1]<r&&(o[1]=r),o},lj={sign:function(e){var t=e.length;if(!(t<=0))for(var r=0,n=e[0].length;r<n;++r)for(var o=0,i=0,a=0;a<t;++a){var u=E()(e[a][r][1])?e[a][r][0]:e[a][r][1];u>=0?(e[a][r][0]=o,e[a][r][1]=o+u,o=e[a][r][1]):(e[a][r][0]=i,e[a][r][1]=i+u,i=e[a][r][1])}},expand:function(e,t){if((n=e.length)>0){for(var r,n,o,i=0,a=e[0].length;i<a;++i){for(o=r=0;r<n;++r)o+=e[r][i][1]||0;if(o)for(r=0;r<n;++r)e[r][i][1]/=o}uU(e,t)}},none:uU,silhouette:function(e,t){if((r=e.length)>0){for(var r,n=0,o=e[t[0]],i=o.length;n<i;++n){for(var a=0,u=0;a<r;++a)u+=e[a][n][1]||0;o[n][1]+=o[n][0]=-u/2}uU(e,t)}},wiggle:function(e,t){if((o=e.length)>0&&(n=(r=e[t[0]]).length)>0){for(var r,n,o,i=0,a=1;a<n;++a){for(var u=0,c=0,l=0;u<o;++u){for(var s=e[t[u]],f=s[a][1]||0,p=(f-(s[a-1][1]||0))/2,d=0;d<u;++d){var h=e[t[d]];p+=(h[a][1]||0)-(h[a-1][1]||0)}c+=f,l+=p*f}r[a-1][1]+=r[a-1][0]=i,c&&(i-=l/c)}r[a-1][1]+=r[a-1][0]=i,uU(e,t)}},positive:function(e){var t=e.length;if(!(t<=0))for(var r=0,n=e[0].length;r<n;++r)for(var o=0,i=0;i<t;++i){var a=E()(e[i][r][1])?e[i][r][0]:e[i][r][1];a>=0?(e[i][r][0]=o,e[i][r][1]=o+a,o=e[i][r][1]):(e[i][r][0]=0,e[i][r][1]=0)}}},lS=function(e,t,r){var n=t.map(function(e){return e.props.dataKey}),o=lj[r];return(function(){var e=tT([]),t=u$,r=uU,n=uW;function o(o){var i,a,u=Array.from(e.apply(this,arguments),uq),c=u.length,l=-1;for(let e of o)for(i=0,++l;i<c;++i)(u[i][l]=[0,+n(e,u[i].key,l,o)]).data=e;for(i=0,a=uz(t(u));i<c;++i)u[a[i]].index=i;return r(u,a),u}return o.keys=function(t){return arguments.length?(e="function"==typeof t?t:tT(Array.from(t)),o):e},o.value=function(e){return arguments.length?(n="function"==typeof e?e:tT(+e),o):n},o.order=function(e){return arguments.length?(t=null==e?u$:"function"==typeof e?e:tT(Array.from(e)),o):t},o.offset=function(e){return arguments.length?(r=null==e?uU:e,o):r},o})().keys(n).value(function(e,t){return+lo(e,t,0)}).order(u$).offset(o)(e)},lP=function(e,t,r,n,o,i){if(!e)return null;var a=(i?t.reverse():t).reduce(function(e,t){var o,i=null!==(o=t.type)&&void 0!==o&&o.defaultProps?lr(lr({},t.type.defaultProps),t.props):t.props,a=i.stackId;if(i.hide)return e;var u=i[r],c=e[u]||{hasStack:!1,stackGroups:{}};if(D(a)){var l=c.stackGroups[a]||{numericAxisId:r,cateAxisId:n,items:[]};l.items.push(t),c.hasStack=!0,c.stackGroups[a]=l}else c.stackGroups[R("_stackId_")]={numericAxisId:r,cateAxisId:n,items:[t]};return lr(lr({},e),{},ln({},u,c))},{});return Object.keys(a).reduce(function(t,i){var u=a[i];return u.hasStack&&(u.stackGroups=Object.keys(u.stackGroups).reduce(function(t,i){var a=u.stackGroups[i];return lr(lr({},t),{},ln({},i,{numericAxisId:r,cateAxisId:n,items:a.items,stackedData:lS(e,a.items,o)}))},{})),lr(lr({},t),{},ln({},i,u))},{})},lE=function(e,t){var r=t.realScaleType,n=t.type,o=t.tickCount,i=t.originalDomain,a=t.allowDecimals,u=r||t.scale;if("auto"!==u&&"linear"!==u)return null;if(o&&"number"===n&&i&&("auto"===i[0]||"auto"===i[1])){var c=e.domain();if(!c.length)return null;var l=cH(c,o,a);return e.domain([u5()(l),u1()(l)]),{niceTicks:l}}return o&&"number"===n?{niceTicks:cV(e.domain(),o,a)}:null};function lk(e){var t=e.axis,r=e.ticks,n=e.bandSize,o=e.entry,i=e.index,a=e.dataKey;if("category"===t.type){if(!t.allowDuplicatedCategory&&t.dataKey&&!H()(o[t.dataKey])){var u=z(r,"value",o[t.dataKey]);if(u)return u.coordinate+n/2}return r[i]?r[i].coordinate+n/2:null}var c=lo(o,H()(a)?t.dataKey:a);return H()(c)?null:t.scale(c)}var lA=function(e){var t=e.axis,r=e.ticks,n=e.offset,o=e.bandSize,i=e.entry,a=e.index;if("category"===t.type)return r[a]?r[a].coordinate+n:null;var u=lo(i,t.dataKey,t.domain[a]);return H()(u)?null:t.scale(u)-o/2+n},lM=function(e){var t=e.numericAxis,r=t.scale.domain();if("number"===t.type){var n=Math.min(r[0],r[1]),o=Math.max(r[0],r[1]);return n<=0&&o>=0?0:o<0?o:n}return r[0]},lT=function(e,t){var r,n=(null!==(r=e.type)&&void 0!==r&&r.defaultProps?lr(lr({},e.type.defaultProps),e.props):e.props).stackId;if(D(n)){var o=t[n];if(o){var i=o.items.indexOf(e);return i>=0?o.stackedData[i]:null}}return null},l_=function(e,t,r){return Object.keys(e).reduce(function(n,o){var i=e[o].stackedData.reduce(function(e,n){var o=n.slice(t,r+1).reduce(function(e,t){return[u5()(t.concat([e[0]]).filter(N)),u1()(t.concat([e[1]]).filter(N))]},[1/0,-1/0]);return[Math.min(e[0],o[0]),Math.max(e[1],o[1])]},[1/0,-1/0]);return[Math.min(i[0],n[0]),Math.max(i[1],n[1])]},[1/0,-1/0]).map(function(e){return e===1/0||e===-1/0?0:e})},lC=/^dataMin[\s]*-[\s]*([0-9]+([.]{1}[0-9]+){0,1})$/,lN=/^dataMax[\s]*\+[\s]*([0-9]+([.]{1}[0-9]+){0,1})$/,lD=function(e,t,r){if(X()(e))return e(t,r);if(!Array.isArray(e))return t;var n=[];if(N(e[0]))n[0]=r?e[0]:Math.min(e[0],t[0]);else if(lC.test(e[0])){var o=+lC.exec(e[0])[1];n[0]=t[0]-o}else X()(e[0])?n[0]=e[0](t[0]):n[0]=t[0];if(N(e[1]))n[1]=r?e[1]:Math.max(e[1],t[1]);else if(lN.test(e[1])){var i=+lN.exec(e[1])[1];n[1]=t[1]+i}else X()(e[1])?n[1]=e[1](t[1]):n[1]=t[1];return n},lI=function(e,t,r){if(e&&e.scale&&e.scale.bandwidth){var n=e.scale.bandwidth();if(!r||n>0)return n}if(e&&t&&t.length>=2){for(var o=eC()(t,function(e){return e.coordinate}),i=1/0,a=1,u=o.length;a<u;a++){var c=o[a],l=o[a-1];i=Math.min((c.coordinate||0)-(l.coordinate||0),i)}return i===1/0?0:i}return r?void 0:0},lR=function(e,t,r){return!e||!e.length||u7()(e,A()(r,"type.defaultProps.domain"))?t:e},lL=function(e,t){var r=e.type.defaultProps?lr(lr({},e.type.defaultProps),e.props):e.props,n=r.dataKey,o=r.name,i=r.unit,a=r.formatter,u=r.tooltipType,c=r.chartType,l=r.hide;return lr(lr({},eb(e,!1)),{},{dataKey:n,unit:i,formatter:a,name:o||n,color:lu(e),value:lo(t,n),type:u,payload:t,chartType:c,hide:l})};function lB(e){return(lB="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function lF(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function lU(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?lF(Object(r),!0).forEach(function(t){lz(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):lF(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function lz(e,t,r){var n;return(n=function(e,t){if("object"!=lB(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=lB(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(t,"string"),(t="symbol"==lB(n)?n:n+"")in e)?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}var l$=["Webkit","Moz","O","ms"],lW=function(e,t){if(!e)return null;var r=e.replace(/(\w)/,function(e){return e.toUpperCase()}),n=l$.reduce(function(e,n){return lU(lU({},e),{},lz({},n+r,t))},{});return n[e]=t,n};function lq(e){return(lq="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function lH(){return(lH=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(this,arguments)}function lV(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function lX(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?lV(Object(r),!0).forEach(function(t){lQ(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):lV(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function lZ(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,lJ(n.key),n)}}function lG(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(e){}return(lG=function(){return!!e})()}function lY(e){return(lY=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)})(e)}function lK(e,t){return(lK=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e})(e,t)}function lQ(e,t,r){return(t=lJ(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function lJ(e){var t=function(e,t){if("object"!=lq(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=lq(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==lq(t)?t:t+""}var l0=function(e){var t=e.data,r=e.startIndex,n=e.endIndex,o=e.x,i=e.width,a=e.travellerWidth;if(!t||!t.length)return{};var u=t.length,c=nN().domain(eT()(0,u)).range([o,o+i-a]),l=c.domain().map(function(e){return c(e)});return{isTextActive:!1,isSlideMoving:!1,isTravellerMoving:!1,isTravellerFocused:!1,startX:c(r),endX:c(n),scale:c,scaleValues:l}},l1=function(e){return e.changedTouches&&!!e.changedTouches.length},l2=function(e){var t,r;function n(e){var t,r,o;return function(e,t){if(!(e instanceof t))throw TypeError("Cannot call a class as a function")}(this,n),r=n,o=[e],r=lY(r),lQ(t=function(e,t){if(t&&("object"===lq(t)||"function"==typeof t))return t;if(void 0!==t)throw TypeError("Derived constructors may only return object or undefined");return function(e){if(void 0===e)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return e}(e)}(this,lG()?Reflect.construct(r,o||[],lY(this).constructor):r.apply(this,o)),"handleDrag",function(e){t.leaveTimer&&(clearTimeout(t.leaveTimer),t.leaveTimer=null),t.state.isTravellerMoving?t.handleTravellerMove(e):t.state.isSlideMoving&&t.handleSlideDrag(e)}),lQ(t,"handleTouchMove",function(e){null!=e.changedTouches&&e.changedTouches.length>0&&t.handleDrag(e.changedTouches[0])}),lQ(t,"handleDragEnd",function(){t.setState({isTravellerMoving:!1,isSlideMoving:!1},function(){var e=t.props,r=e.endIndex,n=e.onDragEnd,o=e.startIndex;null==n||n({endIndex:r,startIndex:o})}),t.detachDragEndListener()}),lQ(t,"handleLeaveWrapper",function(){(t.state.isTravellerMoving||t.state.isSlideMoving)&&(t.leaveTimer=window.setTimeout(t.handleDragEnd,t.props.leaveTimeOut))}),lQ(t,"handleEnterSlideOrTraveller",function(){t.setState({isTextActive:!0})}),lQ(t,"handleLeaveSlideOrTraveller",function(){t.setState({isTextActive:!1})}),lQ(t,"handleSlideDragStart",function(e){var r=l1(e)?e.changedTouches[0]:e;t.setState({isTravellerMoving:!1,isSlideMoving:!0,slideMoveStartX:r.pageX}),t.attachDragEndListener()}),t.travellerDragStartHandlers={startX:t.handleTravellerDragStart.bind(t,"startX"),endX:t.handleTravellerDragStart.bind(t,"endX")},t.state={},t}return function(e,t){if("function"!=typeof t&&null!==t)throw TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&lK(e,t)}(n,e),t=[{key:"componentWillUnmount",value:function(){this.leaveTimer&&(clearTimeout(this.leaveTimer),this.leaveTimer=null),this.detachDragEndListener()}},{key:"getIndex",value:function(e){var t=e.startX,r=e.endX,o=this.state.scaleValues,i=this.props,a=i.gap,u=i.data.length-1,c=n.getIndexInRange(o,Math.min(t,r)),l=n.getIndexInRange(o,Math.max(t,r));return{startIndex:c-c%a,endIndex:l===u?u:l-l%a}}},{key:"getTextOfTick",value:function(e){var t=this.props,r=t.data,n=t.tickFormatter,o=t.dataKey,i=lo(r[e],o,e);return X()(n)?n(i,e):i}},{key:"attachDragEndListener",value:function(){window.addEventListener("mouseup",this.handleDragEnd,!0),window.addEventListener("touchend",this.handleDragEnd,!0),window.addEventListener("mousemove",this.handleDrag,!0)}},{key:"detachDragEndListener",value:function(){window.removeEventListener("mouseup",this.handleDragEnd,!0),window.removeEventListener("touchend",this.handleDragEnd,!0),window.removeEventListener("mousemove",this.handleDrag,!0)}},{key:"handleSlideDrag",value:function(e){var t=this.state,r=t.slideMoveStartX,n=t.startX,o=t.endX,i=this.props,a=i.x,u=i.width,c=i.travellerWidth,l=i.startIndex,s=i.endIndex,f=i.onChange,p=e.pageX-r;p>0?p=Math.min(p,a+u-c-o,a+u-c-n):p<0&&(p=Math.max(p,a-n,a-o));var d=this.getIndex({startX:n+p,endX:o+p});(d.startIndex!==l||d.endIndex!==s)&&f&&f(d),this.setState({startX:n+p,endX:o+p,slideMoveStartX:e.pageX})}},{key:"handleTravellerDragStart",value:function(e,t){var r=l1(t)?t.changedTouches[0]:t;this.setState({isSlideMoving:!1,isTravellerMoving:!0,movingTravellerId:e,brushMoveStartX:r.pageX}),this.attachDragEndListener()}},{key:"handleTravellerMove",value:function(e){var t=this.state,r=t.brushMoveStartX,n=t.movingTravellerId,o=t.endX,i=t.startX,a=this.state[n],u=this.props,c=u.x,l=u.width,s=u.travellerWidth,f=u.onChange,p=u.gap,d=u.data,h={startX:this.state.startX,endX:this.state.endX},y=e.pageX-r;y>0?y=Math.min(y,c+l-s-a):y<0&&(y=Math.max(y,c-a)),h[n]=a+y;var v=this.getIndex(h),m=v.startIndex,g=v.endIndex,b=function(){var e=d.length-1;return"startX"===n&&(o>i?m%p==0:g%p==0)||o<i&&g===e||"endX"===n&&(o>i?g%p==0:m%p==0)||o>i&&g===e};this.setState(lQ(lQ({},n,a+y),"brushMoveStartX",e.pageX),function(){f&&b()&&f(v)})}},{key:"handleTravellerMoveKeyboard",value:function(e,t){var r=this,n=this.state,o=n.scaleValues,i=n.startX,a=n.endX,u=this.state[t],c=o.indexOf(u);if(-1!==c){var l=c+e;if(-1!==l&&!(l>=o.length)){var s=o[l];"startX"===t&&s>=a||"endX"===t&&s<=i||this.setState(lQ({},t,s),function(){r.props.onChange(r.getIndex({startX:r.state.startX,endX:r.state.endX}))})}}}},{key:"renderBackground",value:function(){var e=this.props,t=e.x,r=e.y,n=e.width,o=e.height,i=e.fill,a=e.stroke;return l().createElement("rect",{stroke:a,fill:i,x:t,y:r,width:n,height:o})}},{key:"renderPanorama",value:function(){var e=this.props,t=e.x,r=e.y,n=e.width,o=e.height,i=e.data,a=e.children,u=e.padding,s=c.Children.only(a);return s?l().cloneElement(s,{x:t,y:r,width:n,height:o,margin:u,compact:!0,data:i}):null}},{key:"renderTravellerLayer",value:function(e,t){var r,o,i=this,a=this.props,u=a.y,c=a.travellerWidth,s=a.height,f=a.traveller,p=a.ariaLabel,d=a.data,h=a.startIndex,y=a.endIndex,v=Math.max(e,this.props.x),m=lX(lX({},eb(this.props,!1)),{},{x:v,y:u,width:c,height:s}),g=p||"Min value: ".concat(null===(r=d[h])||void 0===r?void 0:r.name,", Max value: ").concat(null===(o=d[y])||void 0===o?void 0:o.name);return l().createElement(eF,{tabIndex:0,role:"slider","aria-label":g,"aria-valuenow":e,className:"recharts-brush-traveller",onMouseEnter:this.handleEnterSlideOrTraveller,onMouseLeave:this.handleLeaveSlideOrTraveller,onMouseDown:this.travellerDragStartHandlers[t],onTouchStart:this.travellerDragStartHandlers[t],onKeyDown:function(e){["ArrowLeft","ArrowRight"].includes(e.key)&&(e.preventDefault(),e.stopPropagation(),i.handleTravellerMoveKeyboard("ArrowRight"===e.key?1:-1,t))},onFocus:function(){i.setState({isTravellerFocused:!0})},onBlur:function(){i.setState({isTravellerFocused:!1})},style:{cursor:"col-resize"}},n.renderTraveller(f,m))}},{key:"renderSlide",value:function(e,t){var r=this.props,n=r.y,o=r.height,i=r.stroke,a=r.travellerWidth;return l().createElement("rect",{className:"recharts-brush-slide",onMouseEnter:this.handleEnterSlideOrTraveller,onMouseLeave:this.handleLeaveSlideOrTraveller,onMouseDown:this.handleSlideDragStart,onTouchStart:this.handleSlideDragStart,style:{cursor:"move"},stroke:"none",fill:i,fillOpacity:.2,x:Math.min(e,t)+a,y:n,width:Math.max(Math.abs(t-e)-a,0),height:o})}},{key:"renderText",value:function(){var e=this.props,t=e.startIndex,r=e.endIndex,n=e.y,o=e.height,i=e.travellerWidth,a=e.stroke,u=this.state,c=u.startX,s=u.endX,f={pointerEvents:"none",fill:a};return l().createElement(eF,{className:"recharts-brush-texts"},l().createElement(Text,lH({textAnchor:"end",verticalAnchor:"middle",x:Math.min(c,s)-5,y:n+o/2},f),this.getTextOfTick(t)),l().createElement(Text,lH({textAnchor:"start",verticalAnchor:"middle",x:Math.max(c,s)+i+5,y:n+o/2},f),this.getTextOfTick(r)))}},{key:"render",value:function(){var e=this.props,t=e.data,r=e.className,n=e.children,o=e.x,i=e.y,a=e.width,u=e.height,c=e.alwaysShowText,s=this.state,f=s.startX,p=s.endX,d=s.isTextActive,h=s.isSlideMoving,y=s.isTravellerMoving,v=s.isTravellerFocused;if(!t||!t.length||!N(o)||!N(i)||!N(a)||!N(u)||a<=0||u<=0)return null;var m=(0,x.Z)("recharts-brush",r),g=1===l().Children.count(n),b=lW("userSelect","none");return l().createElement(eF,{className:m,onMouseLeave:this.handleLeaveWrapper,onTouchMove:this.handleTouchMove,style:b},this.renderBackground(),g&&this.renderPanorama(),this.renderSlide(f,p),this.renderTravellerLayer(f,"startX"),this.renderTravellerLayer(p,"endX"),(d||h||y||v||c)&&this.renderText())}}],r=[{key:"renderDefaultTraveller",value:function(e){var t=e.x,r=e.y,n=e.width,o=e.height,i=e.stroke,a=Math.floor(r+o/2)-1;return l().createElement(l().Fragment,null,l().createElement("rect",{x:t,y:r,width:n,height:o,fill:i,stroke:"none"}),l().createElement("line",{x1:t+1,y1:a,x2:t+n-1,y2:a,fill:"none",stroke:"#fff"}),l().createElement("line",{x1:t+1,y1:a+2,x2:t+n-1,y2:a+2,fill:"none",stroke:"#fff"}))}},{key:"renderTraveller",value:function(e,t){return l().isValidElement(e)?l().cloneElement(e,t):X()(e)?e(t):n.renderDefaultTraveller(t)}},{key:"getDerivedStateFromProps",value:function(e,t){var r=e.data,n=e.width,o=e.x,i=e.travellerWidth,a=e.updateId,u=e.startIndex,c=e.endIndex;if(r!==t.prevData||a!==t.prevUpdateId)return lX({prevData:r,prevTravellerWidth:i,prevUpdateId:a,prevX:o,prevWidth:n},r&&r.length?l0({data:r,width:n,x:o,travellerWidth:i,startIndex:u,endIndex:c}):{scale:null,scaleValues:null});if(t.scale&&(n!==t.prevWidth||o!==t.prevX||i!==t.prevTravellerWidth)){t.scale.range([o,o+n-i]);var l=t.scale.domain().map(function(e){return t.scale(e)});return{prevData:r,prevTravellerWidth:i,prevUpdateId:a,prevX:o,prevWidth:n,startX:t.scale(e.startIndex),endX:t.scale(e.endIndex),scaleValues:l}}return null}},{key:"getIndexInRange",value:function(e,t){for(var r=e.length,n=0,o=r-1;o-n>1;){var i=Math.floor((n+o)/2);e[i]>t?o=i:n=i}return t>=e[o]?o:n}}],t&&lZ(n.prototype,t),r&&lZ(n,r),Object.defineProperty(n,"prototype",{writable:!1}),n}(c.PureComponent);function l5(e){return(l5="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function l3(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function l4(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?l3(Object(r),!0).forEach(function(t){(function(e,t,r){var n;(n=function(e,t){if("object"!=l5(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=l5(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(t,"string"),(t="symbol"==l5(n)?n:n+"")in e)?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r})(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):l3(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}lQ(l2,"displayName","Brush"),lQ(l2,"defaultProps",{height:40,travellerWidth:5,gap:1,fill:"#fff",stroke:"#666",padding:{top:1,right:1,bottom:1,left:1},leaveTimeOut:1e3,alwaysShowText:!1});var l6=Math.PI/180,l7=function(e,t,r,n){return{x:e+Math.cos(-l6*n)*r,y:t+Math.sin(-l6*n)*r}},l8=function(e,t){var r=e.x,n=e.y;return Math.sqrt(Math.pow(r-t.x,2)+Math.pow(n-t.y,2))},l9=function(e,t){var r=e.x,n=e.y,o=t.cx,i=t.cy,a=l8({x:r,y:n},{x:o,y:i});if(a<=0)return{radius:a};var u=Math.acos((r-o)/a);return n>i&&(u=2*Math.PI-u),{radius:a,angle:180*u/Math.PI,angleInRadian:u}},se=function(e){var t=e.startAngle,r=e.endAngle,n=Math.min(Math.floor(t/360),Math.floor(r/360));return{startAngle:t-360*n,endAngle:r-360*n}},st=function(e,t){var r,n=l9({x:e.x,y:e.y},t),o=n.radius,i=n.angle,a=t.innerRadius,u=t.outerRadius;if(o<a||o>u)return!1;if(0===o)return!0;var c=se(t),l=c.startAngle,s=c.endAngle,f=i;if(l<=s){for(;f>s;)f-=360;for(;f<l;)f+=360;r=f>=l&&f<=s}else{for(;f>l;)f-=360;for(;f<s;)f+=360;r=f>=s&&f<=l}return r?l4(l4({},t),{},{radius:o,angle:f+360*Math.min(Math.floor(t.startAngle/360),Math.floor(t.endAngle/360))}):null};function sr(e){return(sr="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}var sn=["offset"];function so(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=Array(t);r<t;r++)n[r]=e[r];return n}function si(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function sa(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?si(Object(r),!0).forEach(function(t){var n,o;n=t,o=r[t],(n=function(e){var t=function(e,t){if("object"!=sr(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=sr(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==sr(t)?t:t+""}(n))in e?Object.defineProperty(e,n,{value:o,enumerable:!0,configurable:!0,writable:!0}):e[n]=o}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):si(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function su(){return(su=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(this,arguments)}var sc=function(e){var t=e.value,r=e.formatter,n=H()(e.children)?t:e.children;return X()(r)?r(n):n},sl=function(e,t,r){var n,o,i=e.position,a=e.viewBox,u=e.offset,c=e.className,s=a.cx,f=a.cy,p=a.innerRadius,d=a.outerRadius,h=a.startAngle,y=a.endAngle,v=a.clockWise,m=(p+d)/2,g=_(y-h)*Math.min(Math.abs(y-h),360),b=g>=0?1:-1;"insideStart"===i?(n=h+b*u,o=v):"insideEnd"===i?(n=y-b*u,o=!v):"end"===i&&(n=y+b*u,o=v),o=g<=0?o:!o;var w=l7(s,f,m,n),O=l7(s,f,m,n+(o?1:-1)*359),j="M".concat(w.x,",").concat(w.y,"\n    A").concat(m,",").concat(m,",0,1,").concat(o?0:1,",\n    ").concat(O.x,",").concat(O.y),S=H()(e.id)?R("recharts-radial-line-"):e.id;return l().createElement("text",su({},r,{dominantBaseline:"central",className:(0,x.Z)("recharts-radial-bar-label",c)}),l().createElement("defs",null,l().createElement("path",{id:S,d:j})),l().createElement("textPath",{xlinkHref:"#".concat(S)},t))},ss=function(e){var t=e.viewBox,r=e.offset,n=e.position,o=t.cx,i=t.cy,a=t.innerRadius,u=t.outerRadius,c=(t.startAngle+t.endAngle)/2;if("outside"===n){var l=l7(o,i,u+r,c),s=l.x;return{x:s,y:l.y,textAnchor:s>=o?"start":"end",verticalAnchor:"middle"}}if("center"===n)return{x:o,y:i,textAnchor:"middle",verticalAnchor:"middle"};if("centerTop"===n)return{x:o,y:i,textAnchor:"middle",verticalAnchor:"start"};if("centerBottom"===n)return{x:o,y:i,textAnchor:"middle",verticalAnchor:"end"};var f=l7(o,i,(a+u)/2,c);return{x:f.x,y:f.y,textAnchor:"middle",verticalAnchor:"middle"}},sf=function(e){var t=e.viewBox,r=e.parentViewBox,n=e.offset,o=e.position,i=t.x,a=t.y,u=t.width,c=t.height,l=c>=0?1:-1,s=l*n,f=l>0?"end":"start",p=l>0?"start":"end",d=u>=0?1:-1,h=d*n,y=d>0?"end":"start",v=d>0?"start":"end";if("top"===o)return sa(sa({},{x:i+u/2,y:a-l*n,textAnchor:"middle",verticalAnchor:f}),r?{height:Math.max(a-r.y,0),width:u}:{});if("bottom"===o)return sa(sa({},{x:i+u/2,y:a+c+s,textAnchor:"middle",verticalAnchor:p}),r?{height:Math.max(r.y+r.height-(a+c),0),width:u}:{});if("left"===o){var m={x:i-h,y:a+c/2,textAnchor:y,verticalAnchor:"middle"};return sa(sa({},m),r?{width:Math.max(m.x-r.x,0),height:c}:{})}if("right"===o){var g={x:i+u+h,y:a+c/2,textAnchor:v,verticalAnchor:"middle"};return sa(sa({},g),r?{width:Math.max(r.x+r.width-g.x,0),height:c}:{})}var b=r?{width:u,height:c}:{};return"insideLeft"===o?sa({x:i+h,y:a+c/2,textAnchor:v,verticalAnchor:"middle"},b):"insideRight"===o?sa({x:i+u-h,y:a+c/2,textAnchor:y,verticalAnchor:"middle"},b):"insideTop"===o?sa({x:i+u/2,y:a+s,textAnchor:"middle",verticalAnchor:p},b):"insideBottom"===o?sa({x:i+u/2,y:a+c-s,textAnchor:"middle",verticalAnchor:f},b):"insideTopLeft"===o?sa({x:i+h,y:a+s,textAnchor:v,verticalAnchor:p},b):"insideTopRight"===o?sa({x:i+u-h,y:a+s,textAnchor:y,verticalAnchor:p},b):"insideBottomLeft"===o?sa({x:i+h,y:a+c-s,textAnchor:v,verticalAnchor:f},b):"insideBottomRight"===o?sa({x:i+u-h,y:a+c-s,textAnchor:y,verticalAnchor:f},b):G()(o)&&(N(o.x)||C(o.x))&&(N(o.y)||C(o.y))?sa({x:i+L(o.x,u),y:a+L(o.y,c),textAnchor:"end",verticalAnchor:"end"},b):sa({x:i+u/2,y:a+c/2,textAnchor:"middle",verticalAnchor:"middle"},b)};function sp(e){var t,r=e.offset,n=sa({offset:void 0===r?5:r},function(e,t){if(null==e)return{};var r,n,o=function(e,t){if(null==e)return{};var r={};for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){if(t.indexOf(n)>=0)continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(n=0;n<i.length;n++)r=i[n],!(t.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(e,r)&&(o[r]=e[r])}return o}(e,sn)),o=n.viewBox,i=n.position,a=n.value,u=n.children,s=n.content,f=n.className,p=n.textBreakAll;if(!o||H()(a)&&H()(u)&&!(0,c.isValidElement)(s)&&!X()(s))return null;if((0,c.isValidElement)(s))return(0,c.cloneElement)(s,n);if(X()(s)){if(t=(0,c.createElement)(s,n),(0,c.isValidElement)(t))return t}else t=sc(n);var d="cx"in o&&N(o.cx),h=eb(n,!0);if(d&&("insideStart"===i||"insideEnd"===i||"end"===i))return sl(n,t,h);var y=d?ss(n):sf(n);return l().createElement(Text,su({className:(0,x.Z)("recharts-label",void 0===f?"":f)},h,y,{breakAll:p}),t)}sp.displayName="Label";var sd=function(e){var t=e.cx,r=e.cy,n=e.angle,o=e.startAngle,i=e.endAngle,a=e.r,u=e.radius,c=e.innerRadius,l=e.outerRadius,s=e.x,f=e.y,p=e.top,d=e.left,h=e.width,y=e.height,v=e.clockWise,m=e.labelViewBox;if(m)return m;if(N(h)&&N(y)){if(N(s)&&N(f))return{x:s,y:f,width:h,height:y};if(N(p)&&N(d))return{x:p,y:d,width:h,height:y}}return N(s)&&N(f)?{x:s,y:f,width:0,height:0}:N(t)&&N(r)?{cx:t,cy:r,startAngle:o||n||0,endAngle:i||n||0,innerRadius:c||0,outerRadius:l||u||a||0,clockWise:v}:e.viewBox?e.viewBox:{}};sp.parseViewBox=sd,sp.renderCallByParent=function(e,t){var r,n,o=!(arguments.length>2)||void 0===arguments[2]||arguments[2];if(!e||!e.children&&o&&!e.label)return null;var i=e.children,a=sd(e),u=eh(i,sp).map(function(e,r){return(0,c.cloneElement)(e,{viewBox:t||a,key:"label-".concat(r)})});return o?[(r=e.label,n=t||a,r?!0===r?l().createElement(sp,{key:"label-implicit",viewBox:n}):D(r)?l().createElement(sp,{key:"label-implicit",viewBox:n,value:r}):(0,c.isValidElement)(r)?r.type===sp?(0,c.cloneElement)(r,{key:"label-implicit",viewBox:n}):l().createElement(sp,{key:"label-implicit",content:r,viewBox:n}):X()(r)?l().createElement(sp,{key:"label-implicit",content:r,viewBox:n}):G()(r)?l().createElement(sp,su({viewBox:n},r,{key:"label-implicit"})):null:null)].concat(function(e){if(Array.isArray(e))return so(e)}(u)||function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(u)||function(e,t){if(e){if("string"==typeof e)return so(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);if("Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r)return Array.from(e);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return so(e,t)}}(u)||function(){throw TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()):u};var sh=function(e,t){var r=e.alwaysShow,n=e.ifOverflow;return r&&(n="extendDomain"),n===t},sy=r(5125),sv=r.n(sy),sm=r(6871),sg=r.n(sm),sb=function(e){return null};sb.displayName="Cell";var sx=r(6839),sw=r.n(sx);function sO(e){return(sO="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}var sj=["valueAccessor"],sS=["data","dataKey","clockWise","id","textBreakAll"];function sP(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=Array(t);r<t;r++)n[r]=e[r];return n}function sE(){return(sE=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(this,arguments)}function sk(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function sA(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?sk(Object(r),!0).forEach(function(t){var n,o;n=t,o=r[t],(n=function(e){var t=function(e,t){if("object"!=sO(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=sO(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==sO(t)?t:t+""}(n))in e?Object.defineProperty(e,n,{value:o,enumerable:!0,configurable:!0,writable:!0}):e[n]=o}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):sk(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function sM(e,t){if(null==e)return{};var r,n,o=function(e,t){if(null==e)return{};var r={};for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){if(t.indexOf(n)>=0)continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(n=0;n<i.length;n++)r=i[n],!(t.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(e,r)&&(o[r]=e[r])}return o}var sT=function(e){return Array.isArray(e.value)?sw()(e.value):e.value};function s_(e){var t=e.valueAccessor,r=void 0===t?sT:t,n=sM(e,sj),o=n.data,i=n.dataKey,a=n.clockWise,u=n.id,c=n.textBreakAll,s=sM(n,sS);return o&&o.length?l().createElement(eF,{className:"recharts-label-list"},o.map(function(e,t){var n=H()(i)?r(e,t):lo(e&&e.payload,i),o=H()(u)?{}:{id:"".concat(u,"-").concat(t)};return l().createElement(sp,sE({},eb(e,!0),s,o,{parentViewBox:e.parentViewBox,value:n,textBreakAll:c,viewBox:sp.parseViewBox(H()(a)?e:sA(sA({},e),{},{clockWise:a})),key:"label-".concat(t),index:t}))})):null}s_.displayName="LabelList",s_.renderCallByParent=function(e,t){var r,n=!(arguments.length>2)||void 0===arguments[2]||arguments[2];if(!e||!e.children&&n&&!e.label)return null;var o=eh(e.children,s_).map(function(e,r){return(0,c.cloneElement)(e,{data:t,key:"labelList-".concat(r)})});return n?[(r=e.label)?!0===r?l().createElement(s_,{key:"labelList-implicit",data:t}):l().isValidElement(r)||X()(r)?l().createElement(s_,{key:"labelList-implicit",data:t,content:r}):G()(r)?l().createElement(s_,sE({data:t},r,{key:"labelList-implicit"})):null:null].concat(function(e){if(Array.isArray(e))return sP(e)}(o)||function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(o)||function(e,t){if(e){if("string"==typeof e)return sP(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);if("Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r)return Array.from(e);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return sP(e,t)}}(o)||function(){throw TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()):o};var sC=r(6030),sN=r.n(sC),sD=r(518),sI=r.n(sD);function sR(e){return(sR="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function sL(){return(sL=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(this,arguments)}function sB(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=Array(t);r<t;r++)n[r]=e[r];return n}function sF(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function sU(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?sF(Object(r),!0).forEach(function(t){var n,o;n=t,o=r[t],(n=function(e){var t=function(e,t){if("object"!=sR(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=sR(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==sR(t)?t:t+""}(n))in e?Object.defineProperty(e,n,{value:o,enumerable:!0,configurable:!0,writable:!0}):e[n]=o}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):sF(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}var sz=function(e,t,r,n,o){var i=r-n;return"M ".concat(e,",").concat(t)+"L ".concat(e+r,",").concat(t)+"L ".concat(e+r-i/2,",").concat(t+o)+"L ".concat(e+r-i/2-n,",").concat(t+o)+"L ".concat(e,",").concat(t," Z")},s$={x:0,y:0,upperWidth:0,lowerWidth:0,height:0,isUpdateAnimationActive:!1,animationBegin:0,animationDuration:1500,animationEasing:"ease"},sW=function(e){var t,r=sU(sU({},s$),e),n=(0,c.useRef)(),o=function(e){if(Array.isArray(e))return e}(t=(0,c.useState)(-1))||function(e,t){var r=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=r){var n,o,i,a,u=[],c=!0,l=!1;try{if(i=(r=r.call(e)).next,0===t){if(Object(r)!==r)return;c=!1}else for(;!(c=(n=i.call(r)).done)&&(u.push(n.value),u.length!==t);c=!0);}catch(e){l=!0,o=e}finally{try{if(!c&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(l)throw o}}return u}}(t,2)||function(e,t){if(e){if("string"==typeof e)return sB(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);if("Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r)return Array.from(e);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return sB(e,t)}}(t,2)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}(),i=o[0],a=o[1];(0,c.useEffect)(function(){if(n.current&&n.current.getTotalLength)try{var e=n.current.getTotalLength();e&&a(e)}catch(e){}},[]);var u=r.x,s=r.y,f=r.upperWidth,p=r.lowerWidth,d=r.height,h=r.className,y=r.animationEasing,v=r.animationDuration,m=r.animationBegin,g=r.isUpdateAnimationActive;if(u!==+u||s!==+s||f!==+f||p!==+p||d!==+d||0===f&&0===p||0===d)return null;var b=(0,x.Z)("recharts-trapezoid",h);return g?l().createElement(ny,{canBegin:i>0,from:{upperWidth:0,lowerWidth:0,height:d,x:u,y:s},to:{upperWidth:f,lowerWidth:p,height:d,x:u,y:s},duration:v,animationEasing:y,isActive:g},function(e){var t=e.upperWidth,o=e.lowerWidth,a=e.height,u=e.x,c=e.y;return l().createElement(ny,{canBegin:i>0,from:"0px ".concat(-1===i?1:i,"px"),to:"".concat(i,"px 0px"),attributeName:"strokeDasharray",begin:m,duration:v,easing:y},l().createElement("path",sL({},eb(r,!0),{className:b,d:sz(u,c,t,o,a),ref:n})))}):l().createElement("g",null,l().createElement("path",sL({},eb(r,!0),{className:b,d:sz(u,s,f,p,d)})))};function sq(e){return(sq="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function sH(){return(sH=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(this,arguments)}function sV(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function sX(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?sV(Object(r),!0).forEach(function(t){var n,o;n=t,o=r[t],(n=function(e){var t=function(e,t){if("object"!=sq(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=sq(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==sq(t)?t:t+""}(n))in e?Object.defineProperty(e,n,{value:o,enumerable:!0,configurable:!0,writable:!0}):e[n]=o}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):sV(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}var sZ=function(e){var t=e.cx,r=e.cy,n=e.radius,o=e.angle,i=e.sign,a=e.isExternal,u=e.cornerRadius,c=e.cornerIsExternal,l=u*(a?1:-1)+n,s=Math.asin(u/l)/l6,f=c?o:o+i*s;return{center:l7(t,r,l,f),circleTangency:l7(t,r,n,f),lineTangency:l7(t,r,l*Math.cos(s*l6),c?o-i*s:o),theta:s}},sG=function(e){var t,r=e.cx,n=e.cy,o=e.innerRadius,i=e.outerRadius,a=e.startAngle,u=_((t=e.endAngle)-a)*Math.min(Math.abs(t-a),359.999),c=a+u,l=l7(r,n,i,a),s=l7(r,n,i,c),f="M ".concat(l.x,",").concat(l.y,"\n    A ").concat(i,",").concat(i,",0,\n    ").concat(+(Math.abs(u)>180),",").concat(+(a>c),",\n    ").concat(s.x,",").concat(s.y,"\n  ");if(o>0){var p=l7(r,n,o,a),d=l7(r,n,o,c);f+="L ".concat(d.x,",").concat(d.y,"\n            A ").concat(o,",").concat(o,",0,\n            ").concat(+(Math.abs(u)>180),",").concat(+(a<=c),",\n            ").concat(p.x,",").concat(p.y," Z")}else f+="L ".concat(r,",").concat(n," Z");return f},sY=function(e){var t=e.cx,r=e.cy,n=e.innerRadius,o=e.outerRadius,i=e.cornerRadius,a=e.forceCornerRadius,u=e.cornerIsExternal,c=e.startAngle,l=e.endAngle,s=_(l-c),f=sZ({cx:t,cy:r,radius:o,angle:c,sign:s,cornerRadius:i,cornerIsExternal:u}),p=f.circleTangency,d=f.lineTangency,h=f.theta,y=sZ({cx:t,cy:r,radius:o,angle:l,sign:-s,cornerRadius:i,cornerIsExternal:u}),v=y.circleTangency,m=y.lineTangency,g=y.theta,b=u?Math.abs(c-l):Math.abs(c-l)-h-g;if(b<0)return a?"M ".concat(d.x,",").concat(d.y,"\n        a").concat(i,",").concat(i,",0,0,1,").concat(2*i,",0\n        a").concat(i,",").concat(i,",0,0,1,").concat(-(2*i),",0\n      "):sG({cx:t,cy:r,innerRadius:n,outerRadius:o,startAngle:c,endAngle:l});var x="M ".concat(d.x,",").concat(d.y,"\n    A").concat(i,",").concat(i,",0,0,").concat(+(s<0),",").concat(p.x,",").concat(p.y,"\n    A").concat(o,",").concat(o,",0,").concat(+(b>180),",").concat(+(s<0),",").concat(v.x,",").concat(v.y,"\n    A").concat(i,",").concat(i,",0,0,").concat(+(s<0),",").concat(m.x,",").concat(m.y,"\n  ");if(n>0){var w=sZ({cx:t,cy:r,radius:n,angle:c,sign:s,isExternal:!0,cornerRadius:i,cornerIsExternal:u}),O=w.circleTangency,j=w.lineTangency,S=w.theta,P=sZ({cx:t,cy:r,radius:n,angle:l,sign:-s,isExternal:!0,cornerRadius:i,cornerIsExternal:u}),E=P.circleTangency,k=P.lineTangency,A=P.theta,M=u?Math.abs(c-l):Math.abs(c-l)-S-A;if(M<0&&0===i)return"".concat(x,"L").concat(t,",").concat(r,"Z");x+="L".concat(k.x,",").concat(k.y,"\n      A").concat(i,",").concat(i,",0,0,").concat(+(s<0),",").concat(E.x,",").concat(E.y,"\n      A").concat(n,",").concat(n,",0,").concat(+(M>180),",").concat(+(s>0),",").concat(O.x,",").concat(O.y,"\n      A").concat(i,",").concat(i,",0,0,").concat(+(s<0),",").concat(j.x,",").concat(j.y,"Z")}else x+="L".concat(t,",").concat(r,"Z");return x},sK={cx:0,cy:0,innerRadius:0,outerRadius:0,startAngle:0,endAngle:0,cornerRadius:0,forceCornerRadius:!1,cornerIsExternal:!1},sQ=function(e){var t,r=sX(sX({},sK),e),n=r.cx,o=r.cy,i=r.innerRadius,a=r.outerRadius,u=r.cornerRadius,c=r.forceCornerRadius,s=r.cornerIsExternal,f=r.startAngle,p=r.endAngle,d=r.className;if(a<i||f===p)return null;var h=(0,x.Z)("recharts-sector",d),y=a-i,v=L(u,y,0,!0);return t=v>0&&360>Math.abs(f-p)?sY({cx:n,cy:o,innerRadius:i,outerRadius:a,cornerRadius:Math.min(v,y/2),forceCornerRadius:c,cornerIsExternal:s,startAngle:f,endAngle:p}):sG({cx:n,cy:o,innerRadius:i,outerRadius:a,startAngle:f,endAngle:p}),l().createElement("path",sH({},eb(r,!0),{className:h,d:t,role:"img"}))},sJ=["option","shapeType","propTransformer","activeClassName","isActive"];function s0(e){return(s0="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function s1(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function s2(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?s1(Object(r),!0).forEach(function(t){var n,o;n=t,o=r[t],(n=function(e){var t=function(e,t){if("object"!=s0(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=s0(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==s0(t)?t:t+""}(n))in e?Object.defineProperty(e,n,{value:o,enumerable:!0,configurable:!0,writable:!0}):e[n]=o}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):s1(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function s5(e){var t=e.shapeType,r=e.elementProps;switch(t){case"rectangle":return l().createElement(nS,r);case"trapezoid":return l().createElement(sW,r);case"sector":return l().createElement(sQ,r);case"symbols":if("symbols"===t)return l().createElement(tH,r);break;default:return null}}function s3(e){var t,r=e.option,n=e.shapeType,o=e.propTransformer,i=e.activeClassName,a=e.isActive,u=function(e,t){if(null==e)return{};var r,n,o=function(e,t){if(null==e)return{};var r={};for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){if(t.indexOf(n)>=0)continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(n=0;n<i.length;n++)r=i[n],!(t.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(e,r)&&(o[r]=e[r])}return o}(e,sJ);if((0,c.isValidElement)(r))t=(0,c.cloneElement)(r,s2(s2({},u),(0,c.isValidElement)(r)?r.props:r));else if(X()(r))t=r(u);else if(sN()(r)&&!sI()(r)){var s=(void 0===o?function(e,t){return s2(s2({},t),e)}:o)(r,u);t=l().createElement(s5,{shapeType:n,elementProps:s})}else t=l().createElement(s5,{shapeType:n,elementProps:u});return a?l().createElement(eF,{className:void 0===i?"recharts-active-shape":i},t):t}function s4(e,t){return null!=t&&"trapezoids"in e.props}function s6(e,t){return null!=t&&"sectors"in e.props}function s7(e,t){return null!=t&&"points"in e.props}function s8(e,t){var r,n,o=e.x===(null==t||null===(r=t.labelViewBox)||void 0===r?void 0:r.x)||e.x===t.x,i=e.y===(null==t||null===(n=t.labelViewBox)||void 0===n?void 0:n.y)||e.y===t.y;return o&&i}function s9(e,t){var r=e.endAngle===t.endAngle,n=e.startAngle===t.startAngle;return r&&n}function fe(e,t){var r=e.x===t.x,n=e.y===t.y,o=e.z===t.z;return r&&n&&o}var ft=["x","y"];function fr(e){return(fr="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function fn(){return(fn=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(this,arguments)}function fo(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function fi(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?fo(Object(r),!0).forEach(function(t){var n,o;n=t,o=r[t],(n=function(e){var t=function(e,t){if("object"!=fr(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=fr(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==fr(t)?t:t+""}(n))in e?Object.defineProperty(e,n,{value:o,enumerable:!0,configurable:!0,writable:!0}):e[n]=o}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):fo(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function fa(e,t){var r=e.x,n=e.y,o=function(e,t){if(null==e)return{};var r,n,o=function(e,t){if(null==e)return{};var r={};for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){if(t.indexOf(n)>=0)continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(n=0;n<i.length;n++)r=i[n],!(t.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(e,r)&&(o[r]=e[r])}return o}(e,ft),i=parseInt("".concat(r),10),a=parseInt("".concat(n),10),u=parseInt("".concat(t.height||o.height),10),c=parseInt("".concat(t.width||o.width),10);return fi(fi(fi(fi(fi({},t),o),i?{x:i}:{}),a?{y:a}:{}),{},{height:u,width:c,name:t.name,radius:t.radius})}function fu(e){return l().createElement(s3,fn({shapeType:"rectangle",propTransformer:fa,activeClassName:"recharts-active-bar"},e))}var fc=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0;return function(r,n){if("number"==typeof e)return e;var o="number"==typeof r;return o?e(r,n):(o||eN(!1),t)}},fl=["value","background"];function fs(e){return(fs="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function ff(){return(ff=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(this,arguments)}function fp(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function fd(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?fp(Object(r),!0).forEach(function(t){fg(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):fp(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function fh(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,fb(n.key),n)}}function fy(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(e){}return(fy=function(){return!!e})()}function fv(e){return(fv=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)})(e)}function fm(e,t){return(fm=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e})(e,t)}function fg(e,t,r){return(t=fb(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function fb(e){var t=function(e,t){if("object"!=fs(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=fs(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==fs(t)?t:t+""}var fx=function(e){var t,r;function n(){!function(e,t){if(!(e instanceof t))throw TypeError("Cannot call a class as a function")}(this,n);for(var e,t,r,o=arguments.length,i=Array(o),a=0;a<o;a++)i[a]=arguments[a];return t=n,r=[].concat(i),t=fv(t),fg(e=function(e,t){if(t&&("object"===fs(t)||"function"==typeof t))return t;if(void 0!==t)throw TypeError("Derived constructors may only return object or undefined");return function(e){if(void 0===e)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return e}(e)}(this,fy()?Reflect.construct(t,r||[],fv(this).constructor):t.apply(this,r)),"state",{isAnimationFinished:!1}),fg(e,"id",R("recharts-bar-")),fg(e,"handleAnimationEnd",function(){var t=e.props.onAnimationEnd;e.setState({isAnimationFinished:!0}),t&&t()}),fg(e,"handleAnimationStart",function(){var t=e.props.onAnimationStart;e.setState({isAnimationFinished:!1}),t&&t()}),e}return function(e,t){if("function"!=typeof t&&null!==t)throw TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&fm(e,t)}(n,e),t=[{key:"renderRectanglesStatically",value:function(e){var t=this,r=this.props,n=r.shape,o=r.dataKey,i=r.activeIndex,a=r.activeBar,u=eb(this.props,!1);return e&&e.map(function(e,r){var c=r===i,s=fd(fd(fd({},u),e),{},{isActive:c,option:c?a:n,index:r,dataKey:o,onAnimationStart:t.handleAnimationStart,onAnimationEnd:t.handleAnimationEnd});return l().createElement(eF,ff({className:"recharts-bar-rectangle"},eo(t.props,e,r),{key:"rectangle-".concat(null==e?void 0:e.x,"-").concat(null==e?void 0:e.y,"-").concat(null==e?void 0:e.value,"-").concat(r)}),l().createElement(fu,s))})}},{key:"renderRectanglesWithAnimation",value:function(){var e=this,t=this.props,r=t.data,n=t.layout,o=t.isAnimationActive,i=t.animationBegin,a=t.animationDuration,u=t.animationEasing,c=t.animationId,s=this.state.prevData;return l().createElement(ny,{begin:i,duration:a,isActive:o,easing:u,from:{t:0},to:{t:1},key:"bar-".concat(c),onAnimationEnd:this.handleAnimationEnd,onAnimationStart:this.handleAnimationStart},function(t){var o=t.t,i=r.map(function(e,t){var r=s&&s[t];if(r){var i=U(r.x,e.x),a=U(r.y,e.y),u=U(r.width,e.width),c=U(r.height,e.height);return fd(fd({},e),{},{x:i(o),y:a(o),width:u(o),height:c(o)})}if("horizontal"===n){var l=U(0,e.height)(o);return fd(fd({},e),{},{y:e.y+e.height-l,height:l})}var f=U(0,e.width)(o);return fd(fd({},e),{},{width:f})});return l().createElement(eF,null,e.renderRectanglesStatically(i))})}},{key:"renderRectangles",value:function(){var e=this.props,t=e.data,r=e.isAnimationActive,n=this.state.prevData;return r&&t&&t.length&&(!n||!u7()(n,t))?this.renderRectanglesWithAnimation():this.renderRectanglesStatically(t)}},{key:"renderBackground",value:function(){var e=this,t=this.props,r=t.data,n=t.dataKey,o=t.activeIndex,i=eb(this.props.background,!1);return r.map(function(t,r){t.value;var a=t.background,u=function(e,t){if(null==e)return{};var r,n,o=function(e,t){if(null==e)return{};var r={};for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){if(t.indexOf(n)>=0)continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(n=0;n<i.length;n++)r=i[n],!(t.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(e,r)&&(o[r]=e[r])}return o}(t,fl);if(!a)return null;var c=fd(fd(fd(fd(fd({},u),{},{fill:"#eee"},a),i),eo(e.props,t,r)),{},{onAnimationStart:e.handleAnimationStart,onAnimationEnd:e.handleAnimationEnd,dataKey:n,index:r,className:"recharts-bar-background-rectangle"});return l().createElement(fu,ff({key:"background-bar-".concat(r),option:e.props.background,isActive:r===o},c))})}},{key:"renderErrorBar",value:function(e,t){if(this.props.isAnimationActive&&!this.state.isAnimationFinished)return null;var r=this.props,n=r.data,o=r.xAxis,i=r.yAxis,a=r.layout,u=eh(r.children,c5);if(!u)return null;var c="vertical"===a?n[0].height/2:n[0].width/2,s=function(e,t){var r=Array.isArray(e.value)?e.value[1]:e.value;return{x:e.x,y:e.y,value:r,errorVal:lo(e,t)}};return l().createElement(eF,{clipPath:e?"url(#clipPath-".concat(t,")"):null},u.map(function(e){return l().cloneElement(e,{key:"error-bar-".concat(t,"-").concat(e.props.dataKey),data:n,xAxis:o,yAxis:i,layout:a,offset:c,dataPointFormatter:s})}))}},{key:"render",value:function(){var e=this.props,t=e.hide,r=e.data,n=e.className,o=e.xAxis,i=e.yAxis,a=e.left,u=e.top,c=e.width,s=e.height,f=e.isAnimationActive,p=e.background,d=e.id;if(t||!r||!r.length)return null;var h=this.state.isAnimationFinished,y=(0,x.Z)("recharts-bar",n),v=o&&o.allowDataOverflow,m=i&&i.allowDataOverflow,g=v||m,b=H()(d)?this.id:d;return l().createElement(eF,{className:y},v||m?l().createElement("defs",null,l().createElement("clipPath",{id:"clipPath-".concat(b)},l().createElement("rect",{x:v?a:a-c/2,y:m?u:u-s/2,width:v?c:2*c,height:m?s:2*s}))):null,l().createElement(eF,{className:"recharts-bar-rectangles",clipPath:g?"url(#clipPath-".concat(b,")"):null},p?this.renderBackground():null,this.renderRectangles()),this.renderErrorBar(g,b),(!f||h)&&s_.renderCallByParent(this.props,r))}}],r=[{key:"getDerivedStateFromProps",value:function(e,t){return e.animationId!==t.prevAnimationId?{prevAnimationId:e.animationId,curData:e.data,prevData:t.curData}:e.data!==t.curData?{curData:e.data}:null}}],t&&fh(n.prototype,t),r&&fh(n,r),Object.defineProperty(n,"prototype",{writable:!1}),n}(c.PureComponent);function fw(e){return(fw="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function fO(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,fE(n.key),n)}}function fj(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function fS(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?fj(Object(r),!0).forEach(function(t){fP(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):fj(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function fP(e,t,r){return(t=fE(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function fE(e){var t=function(e,t){if("object"!=fw(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=fw(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==fw(t)?t:t+""}fg(fx,"displayName","Bar"),fg(fx,"defaultProps",{xAxisId:0,yAxisId:0,legendType:"rect",minPointSize:0,hide:!1,data:[],layout:"vertical",activeBar:!1,isAnimationActive:!e8.isSsr,animationBegin:0,animationDuration:400,animationEasing:"ease"}),fg(fx,"getComposedData",function(e){var t=e.props,r=e.item,n=e.barPosition,o=e.bandSize,i=e.xAxis,a=e.yAxis,u=e.xAxisTicks,c=e.yAxisTicks,l=e.stackedData,s=e.dataStartIndex,f=e.displayedData,p=e.offset,d=lw(n,r);if(!d)return null;var h=t.layout,y=r.type.defaultProps,v=void 0!==y?fd(fd({},y),r.props):r.props,m=v.dataKey,g=v.children,b=v.minPointSize,x="horizontal"===h?a:i,w=l?x.scale.domain():null,O=lM({numericAxis:x}),j=eh(g,sb),S=f.map(function(e,t){l?f=lO(l[s+t],w):Array.isArray(f=lo(e,m))||(f=[O,f]);var n=fc(b,fx.defaultProps.minPointSize)(f[1],t);if("horizontal"===h){var f,p,y,v,g,x,S,P=[a.scale(f[0]),a.scale(f[1])],E=P[0],k=P[1];p=lA({axis:i,ticks:u,bandSize:o,offset:d.offset,entry:e,index:t}),y=null!==(S=null!=k?k:E)&&void 0!==S?S:void 0,v=d.size;var A=E-k;if(g=Number.isNaN(A)?0:A,x={x:p,y:a.y,width:v,height:a.height},Math.abs(n)>0&&Math.abs(g)<Math.abs(n)){var M=_(g||n)*(Math.abs(n)-Math.abs(g));y-=M,g+=M}}else{var T=[i.scale(f[0]),i.scale(f[1])],C=T[0],N=T[1];if(p=C,y=lA({axis:a,ticks:c,bandSize:o,offset:d.offset,entry:e,index:t}),v=N-C,g=d.size,x={x:i.x,y:y,width:i.width,height:g},Math.abs(n)>0&&Math.abs(v)<Math.abs(n)){var D=_(v||n)*(Math.abs(n)-Math.abs(v));v+=D}}return fd(fd(fd({},e),{},{x:p,y:y,width:v,height:g,value:l?f:f[1],payload:e,background:x},j&&j[t]&&j[t].props),{},{tooltipPayload:[lL(r,e)],tooltipPosition:{x:p+v/2,y:y+g/2}})});return fd({data:S,layout:h},p)});var fk=function(e,t,r,n,o){var i=e.width,a=e.height,u=e.layout,c=e.children,l=Object.keys(t),s={left:r.left,leftMirror:r.left,right:i-r.right,rightMirror:i-r.right,top:r.top,topMirror:r.top,bottom:a-r.bottom,bottomMirror:a-r.bottom},f=!!ey(c,fx);return l.reduce(function(i,a){var c,l,p,d,h,y=t[a],v=y.orientation,m=y.domain,g=y.padding,b=void 0===g?{}:g,x=y.mirror,w=y.reversed,O="".concat(v).concat(x?"Mirror":"");if("number"===y.type&&("gap"===y.padding||"no-gap"===y.padding)){var j=m[1]-m[0],S=1/0,P=y.categoricalDomain.sort($);if(P.forEach(function(e,t){t>0&&(S=Math.min((e||0)-(P[t-1]||0),S))}),Number.isFinite(S)){var E=S/j,k="vertical"===y.layout?r.height:r.width;if("gap"===y.padding&&(c=E*k/2),"no-gap"===y.padding){var A=L(e.barCategoryGap,E*k),M=E*k/2;c=M-A-(M-A)/k*A}}}l="xAxis"===n?[r.left+(b.left||0)+(c||0),r.left+r.width-(b.right||0)-(c||0)]:"yAxis"===n?"horizontal"===u?[r.top+r.height-(b.bottom||0),r.top+(b.top||0)]:[r.top+(b.top||0)+(c||0),r.top+r.height-(b.bottom||0)-(c||0)]:y.range,w&&(l=[l[1],l[0]]);var T=lb(y,o,f),_=T.scale,C=T.realScaleType;_.domain(m).range(l),lx(_);var N=lE(_,fS(fS({},y),{},{realScaleType:C}));"xAxis"===n?(h="top"===v&&!x||"bottom"===v&&x,p=r.left,d=s[O]-h*y.height):"yAxis"===n&&(h="left"===v&&!x||"right"===v&&x,p=s[O]-h*y.width,d=r.top);var D=fS(fS(fS({},y),N),{},{realScaleType:C,x:p,y:d,scale:_,width:"xAxis"===n?r.width:y.width,height:"yAxis"===n?r.height:y.height});return D.bandSize=lI(D,N),y.hide||"xAxis"!==n?y.hide||(s[O]+=(h?-1:1)*D.width):s[O]+=(h?-1:1)*D.height,fS(fS({},i),{},fP({},a,D))},{})},fA=function(e,t){var r=e.x,n=e.y,o=t.x,i=t.y;return{x:Math.min(r,o),y:Math.min(n,i),width:Math.abs(o-r),height:Math.abs(i-n)}},fM=function(){var e,t;function r(e){(function(e,t){if(!(e instanceof t))throw TypeError("Cannot call a class as a function")})(this,r),this.scale=e}return e=[{key:"domain",get:function(){return this.scale.domain}},{key:"range",get:function(){return this.scale.range}},{key:"rangeMin",get:function(){return this.range()[0]}},{key:"rangeMax",get:function(){return this.range()[1]}},{key:"bandwidth",get:function(){return this.scale.bandwidth}},{key:"apply",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},r=t.bandAware,n=t.position;if(void 0!==e){if(n)switch(n){case"start":default:return this.scale(e);case"middle":var o=this.bandwidth?this.bandwidth()/2:0;return this.scale(e)+o;case"end":var i=this.bandwidth?this.bandwidth():0;return this.scale(e)+i}if(r){var a=this.bandwidth?this.bandwidth()/2:0;return this.scale(e)+a}return this.scale(e)}}},{key:"isInRange",value:function(e){var t=this.range(),r=t[0],n=t[t.length-1];return r<=n?e>=r&&e<=n:e>=n&&e<=r}}],t=[{key:"create",value:function(e){return new r(e)}}],e&&fO(r.prototype,e),t&&fO(r,t),Object.defineProperty(r,"prototype",{writable:!1}),r}();fP(fM,"EPS",1e-4);var fT=function(e){var t=Object.keys(e).reduce(function(t,r){return fS(fS({},t),{},fP({},r,fM.create(e[r])))},{});return fS(fS({},t),{},{apply:function(e){var r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=r.bandAware,o=r.position;return sv()(e,function(e,r){return t[r].apply(e,{bandAware:n,position:o})})},isInRange:function(e){return sg()(e,function(e,r){return t[r].isInRange(e)})}})},f_=function(e){var t=e.width,r=e.height,n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,o=(n%180+180)%180*Math.PI/180,i=Math.atan(r/t);return Math.abs(o>i&&o<Math.PI-i?r/Math.sin(o):t/Math.cos(o))};function fC(){return(fC=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(this,arguments)}function fN(e){return(fN="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function fD(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function fI(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?fD(Object(r),!0).forEach(function(t){fU(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):fD(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function fR(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,fz(n.key),n)}}function fL(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(e){}return(fL=function(){return!!e})()}function fB(e){return(fB=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)})(e)}function fF(e,t){return(fF=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e})(e,t)}function fU(e,t,r){return(t=fz(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function fz(e){var t=function(e,t){if("object"!=fN(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=fN(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==fN(t)?t:t+""}var f$=function(e){var t=e.x,r=e.y,n=e.xAxis,o=e.yAxis,i=fT({x:n.scale,y:o.scale}),a=i.apply({x:t,y:r},{bandAware:!0});return sh(e,"discard")&&!i.isInRange(a)?null:a},fW=function(e){var t,r;function n(){var e,t;return function(e,t){if(!(e instanceof t))throw TypeError("Cannot call a class as a function")}(this,n),e=n,t=arguments,e=fB(e),function(e,t){if(t&&("object"===fN(t)||"function"==typeof t))return t;if(void 0!==t)throw TypeError("Derived constructors may only return object or undefined");return function(e){if(void 0===e)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return e}(e)}(this,fL()?Reflect.construct(e,t||[],fB(this).constructor):e.apply(this,t))}return function(e,t){if("function"!=typeof t&&null!==t)throw TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&fF(e,t)}(n,e),t=[{key:"render",value:function(){var e=this.props,t=e.x,r=e.y,o=e.r,i=e.alwaysShow,a=e.clipPathId,u=D(t),c=D(r);if(W(void 0===i,'The alwaysShow prop is deprecated. Please use ifOverflow="extendDomain" instead.'),!u||!c)return null;var s=f$(this.props);if(!s)return null;var f=s.x,p=s.y,d=this.props,h=d.shape,y=d.className,v=fI(fI({clipPath:sh(this.props,"hidden")?"url(#".concat(a,")"):void 0},eb(this.props,!0)),{},{cx:f,cy:p});return l().createElement(eF,{className:(0,x.Z)("recharts-reference-dot",y)},n.renderDot(h,v),sp.renderCallByParent(this.props,{x:f-o,y:p-o,width:2*o,height:2*o}))}}],fR(n.prototype,t),r&&fR(n,r),Object.defineProperty(n,"prototype",{writable:!1}),n}(l().Component);fU(fW,"displayName","ReferenceDot"),fU(fW,"defaultProps",{isFront:!1,ifOverflow:"discard",xAxisId:0,yAxisId:0,r:10,fill:"#fff",stroke:"#ccc",fillOpacity:1,strokeWidth:1}),fU(fW,"renderDot",function(e,t){return l().isValidElement(e)?l().cloneElement(e,t):X()(e)?e(t):l().createElement(ri,fC({},t,{cx:t.cx,cy:t.cy,className:"recharts-reference-dot-dot"}))});var fq=r(7942),fH=r.n(fq),fV=r(7712),fX=r.n(fV),fZ=r(5109),fG=r.n(fZ)()(function(e){return{x:e.left,y:e.top,width:e.width,height:e.height}},function(e){return["l",e.left,"t",e.top,"w",e.width,"h",e.height].join("")}),fY=(0,c.createContext)(void 0),fK=(0,c.createContext)(void 0),fQ=(0,c.createContext)(void 0),fJ=(0,c.createContext)({}),f0=(0,c.createContext)(void 0),f1=(0,c.createContext)(0),f2=(0,c.createContext)(0),f5=function(e){var t=e.state,r=t.xAxisMap,n=t.yAxisMap,o=t.offset,i=e.clipPathId,a=e.children,u=e.width,c=e.height,s=fG(o);return l().createElement(fY.Provider,{value:r},l().createElement(fK.Provider,{value:n},l().createElement(fJ.Provider,{value:o},l().createElement(fQ.Provider,{value:s},l().createElement(f0.Provider,{value:i},l().createElement(f1.Provider,{value:c},l().createElement(f2.Provider,{value:u},a)))))))},f3=function(e){var t=(0,c.useContext)(fY);null!=t||eN(!1);var r=t[e];return null!=r||eN(!1),r},f4=function(){var e=(0,c.useContext)(fK);return fX()(e,function(e){return sg()(e.domain,Number.isFinite)})||B(e)},f6=function(e){var t=(0,c.useContext)(fK);null!=t||eN(!1);var r=t[e];return null!=r||eN(!1),r},f7=function(){return(0,c.useContext)(f2)},f8=function(){return(0,c.useContext)(f1)};function f9(e){return(f9="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function pe(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,pu(n.key),n)}}function pt(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(e){}return(pt=function(){return!!e})()}function pr(e){return(pr=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)})(e)}function pn(e,t){return(pn=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e})(e,t)}function po(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function pi(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?po(Object(r),!0).forEach(function(t){pa(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):po(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function pa(e,t,r){return(t=pu(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function pu(e){var t=function(e,t){if("object"!=f9(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=f9(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==f9(t)?t:t+""}function pc(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=Array(t);r<t;r++)n[r]=e[r];return n}function pl(){return(pl=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(this,arguments)}var ps=function(e,t,r,n,o,i,a,u,c){var l=o.x,s=o.y,f=o.width,p=o.height;if(r){var d=c.y,h=e.y.apply(d,{position:i});if(sh(c,"discard")&&!e.y.isInRange(h))return null;var y=[{x:l+f,y:h},{x:l,y:h}];return"left"===u?y.reverse():y}if(t){var v=c.x,m=e.x.apply(v,{position:i});if(sh(c,"discard")&&!e.x.isInRange(m))return null;var g=[{x:m,y:s+p},{x:m,y:s}];return"top"===a?g.reverse():g}if(n){var b=c.segment.map(function(t){return e.apply(t,{position:i})});return sh(c,"discard")&&fH()(b,function(t){return!e.isInRange(t)})?null:b}return null};function pf(e){var t,r,n,o=e.x,i=e.y,a=e.segment,u=e.xAxisId,s=e.yAxisId,f=e.shape,p=e.className,d=e.alwaysShow,h=(0,c.useContext)(f0),y=f3(u),v=f6(s),m=(0,c.useContext)(fQ);if(!h||!m)return null;W(void 0===d,'The alwaysShow prop is deprecated. Please use ifOverflow="extendDomain" instead.');var g=ps(fT({x:y.scale,y:v.scale}),D(o),D(i),a&&2===a.length,m,e.position,y.orientation,v.orientation,e);if(!g)return null;var b=function(e){if(Array.isArray(e))return e}(g)||function(e,t){var r=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=r){var n,o,i,a,u=[],c=!0,l=!1;try{if(i=(r=r.call(e)).next,0===t){if(Object(r)!==r)return;c=!1}else for(;!(c=(n=i.call(r)).done)&&(u.push(n.value),u.length!==t);c=!0);}catch(e){l=!0,o=e}finally{try{if(!c&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(l)throw o}}return u}}(g,2)||function(e,t){if(e){if("string"==typeof e)return pc(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);if("Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r)return Array.from(e);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return pc(e,t)}}(g,2)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}(),w=b[0],O=w.x,j=w.y,S=b[1],P=S.x,E=S.y,k=pi(pi({clipPath:sh(e,"hidden")?"url(#".concat(h,")"):void 0},eb(e,!0)),{},{x1:O,y1:j,x2:P,y2:E});return l().createElement(eF,{className:(0,x.Z)("recharts-reference-line",p)},(t=f,r=k,l().isValidElement(t)?l().cloneElement(t,r):X()(t)?t(r):l().createElement("line",pl({},r,{className:"recharts-reference-line-line"}))),sp.renderCallByParent(e,fA({x:(n={x1:O,y1:j,x2:P,y2:E}).x1,y:n.y1},{x:n.x2,y:n.y2})))}var pp=function(e){var t,r;function n(){var e,t;return function(e,t){if(!(e instanceof t))throw TypeError("Cannot call a class as a function")}(this,n),e=n,t=arguments,e=pr(e),function(e,t){if(t&&("object"===f9(t)||"function"==typeof t))return t;if(void 0!==t)throw TypeError("Derived constructors may only return object or undefined");return function(e){if(void 0===e)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return e}(e)}(this,pt()?Reflect.construct(e,t||[],pr(this).constructor):e.apply(this,t))}return function(e,t){if("function"!=typeof t&&null!==t)throw TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&pn(e,t)}(n,e),t=[{key:"render",value:function(){return l().createElement(pf,this.props)}}],pe(n.prototype,t),r&&pe(n,r),Object.defineProperty(n,"prototype",{writable:!1}),n}(l().Component);function pd(){return(pd=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(this,arguments)}function ph(e){return(ph="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function py(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function pv(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?py(Object(r),!0).forEach(function(t){pw(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):py(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function pm(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,pO(n.key),n)}}function pg(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(e){}return(pg=function(){return!!e})()}function pb(e){return(pb=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)})(e)}function px(e,t){return(px=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e})(e,t)}function pw(e,t,r){return(t=pO(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function pO(e){var t=function(e,t){if("object"!=ph(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=ph(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==ph(t)?t:t+""}pa(pp,"displayName","ReferenceLine"),pa(pp,"defaultProps",{isFront:!1,ifOverflow:"discard",xAxisId:0,yAxisId:0,fill:"none",stroke:"#ccc",fillOpacity:1,strokeWidth:1,position:"middle"});var pj=function(e,t,r,n,o){var i=o.x1,a=o.x2,u=o.y1,c=o.y2,l=o.xAxis,s=o.yAxis;if(!l||!s)return null;var f=fT({x:l.scale,y:s.scale}),p={x:e?f.x.apply(i,{position:"start"}):f.x.rangeMin,y:r?f.y.apply(u,{position:"start"}):f.y.rangeMin},d={x:t?f.x.apply(a,{position:"end"}):f.x.rangeMax,y:n?f.y.apply(c,{position:"end"}):f.y.rangeMax};return!sh(o,"discard")||f.isInRange(p)&&f.isInRange(d)?fA(p,d):null},pS=function(e){var t,r;function n(){var e,t;return function(e,t){if(!(e instanceof t))throw TypeError("Cannot call a class as a function")}(this,n),e=n,t=arguments,e=pb(e),function(e,t){if(t&&("object"===ph(t)||"function"==typeof t))return t;if(void 0!==t)throw TypeError("Derived constructors may only return object or undefined");return function(e){if(void 0===e)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return e}(e)}(this,pg()?Reflect.construct(e,t||[],pb(this).constructor):e.apply(this,t))}return function(e,t){if("function"!=typeof t&&null!==t)throw TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&px(e,t)}(n,e),t=[{key:"render",value:function(){var e=this.props,t=e.x1,r=e.x2,o=e.y1,i=e.y2,a=e.className,u=e.alwaysShow,c=e.clipPathId;W(void 0===u,'The alwaysShow prop is deprecated. Please use ifOverflow="extendDomain" instead.');var s=D(t),f=D(r),p=D(o),d=D(i),h=this.props.shape;if(!s&&!f&&!p&&!d&&!h)return null;var y=pj(s,f,p,d,this.props);if(!y&&!h)return null;var v=sh(this.props,"hidden")?"url(#".concat(c,")"):void 0;return l().createElement(eF,{className:(0,x.Z)("recharts-reference-area",a)},n.renderRect(h,pv(pv({clipPath:v},eb(this.props,!0)),y)),sp.renderCallByParent(this.props,y))}}],pm(n.prototype,t),r&&pm(n,r),Object.defineProperty(n,"prototype",{writable:!1}),n}(l().Component);function pP(e){return function(e){if(Array.isArray(e))return pE(e)}(e)||function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(e)||function(e,t){if(e){if("string"==typeof e)return pE(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);if("Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r)return Array.from(e);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return pE(e,t)}}(e)||function(){throw TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function pE(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=Array(t);r<t;r++)n[r]=e[r];return n}pw(pS,"displayName","ReferenceArea"),pw(pS,"defaultProps",{isFront:!1,ifOverflow:"discard",xAxisId:0,yAxisId:0,r:10,fill:"#ccc",fillOpacity:.5,stroke:"none",strokeWidth:1}),pw(pS,"renderRect",function(e,t){return l().isValidElement(e)?l().cloneElement(e,t):X()(e)?e(t):l().createElement(nS,pd({},t,{className:"recharts-reference-area-rect"}))});var pk=function(e,t,r,n,o){var i=eh(e,pp),a=eh(e,fW),u=[].concat(pP(i),pP(a)),c=eh(e,pS),l="".concat(n,"Id"),s=n[0],f=t;if(u.length&&(f=u.reduce(function(e,t){if(t.props[l]===r&&sh(t.props,"extendDomain")&&N(t.props[s])){var n=t.props[s];return[Math.min(e[0],n),Math.max(e[1],n)]}return e},f)),c.length){var p="".concat(s,"1"),d="".concat(s,"2");f=c.reduce(function(e,t){if(t.props[l]===r&&sh(t.props,"extendDomain")&&N(t.props[p])&&N(t.props[d])){var n=t.props[p],o=t.props[d];return[Math.min(e[0],n,o),Math.max(e[1],n,o)]}return e},f)}return o&&o.length&&(f=o.reduce(function(e,t){return N(t)?[Math.min(e[0],t),Math.max(e[1],t)]:e},f)),f},pA=r(3935),pM=new(r.n(pA)()),pT="recharts.syncMouseEvents";function p_(e){return(p_="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function pC(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,pD(n.key),n)}}function pN(e,t,r){return(t=pD(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function pD(e){var t=function(e,t){if("object"!=p_(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=p_(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==p_(t)?t:t+""}var pI=function(){var e,t,r;return e=function e(){(function(e,t){if(!(e instanceof t))throw TypeError("Cannot call a class as a function")})(this,e),pN(this,"activeIndex",0),pN(this,"coordinateList",[]),pN(this,"layout","horizontal")},t=[{key:"setDetails",value:function(e){var t,r=e.coordinateList,n=void 0===r?null:r,o=e.container,i=void 0===o?null:o,a=e.layout,u=void 0===a?null:a,c=e.offset,l=void 0===c?null:c,s=e.mouseHandlerCallback,f=void 0===s?null:s;this.coordinateList=null!==(t=null!=n?n:this.coordinateList)&&void 0!==t?t:[],this.container=null!=i?i:this.container,this.layout=null!=u?u:this.layout,this.offset=null!=l?l:this.offset,this.mouseHandlerCallback=null!=f?f:this.mouseHandlerCallback,this.activeIndex=Math.min(Math.max(this.activeIndex,0),this.coordinateList.length-1)}},{key:"focus",value:function(){this.spoofMouse()}},{key:"keyboardEvent",value:function(e){if(0!==this.coordinateList.length)switch(e.key){case"ArrowRight":if("horizontal"!==this.layout)return;this.activeIndex=Math.min(this.activeIndex+1,this.coordinateList.length-1),this.spoofMouse();break;case"ArrowLeft":if("horizontal"!==this.layout)return;this.activeIndex=Math.max(this.activeIndex-1,0),this.spoofMouse()}}},{key:"setIndex",value:function(e){this.activeIndex=e}},{key:"spoofMouse",value:function(){if("horizontal"===this.layout&&0!==this.coordinateList.length){var e,t,r=this.container.getBoundingClientRect(),n=r.x,o=r.y,i=r.height,a=this.coordinateList[this.activeIndex].coordinate,u=(null===(e=window)||void 0===e?void 0:e.scrollX)||0,c=(null===(t=window)||void 0===t?void 0:t.scrollY)||0,l=o+this.offset.top+i/2+c;this.mouseHandlerCallback({pageX:n+a+u,pageY:l})}}}],pC(e.prototype,t),r&&pC(e,r),Object.defineProperty(e,"prototype",{writable:!1}),e}();function pR(){}function pL(e,t,r){e._context.bezierCurveTo((2*e._x0+e._x1)/3,(2*e._y0+e._y1)/3,(e._x0+2*e._x1)/3,(e._y0+2*e._y1)/3,(e._x0+4*e._x1+t)/6,(e._y0+4*e._y1+r)/6)}function pB(e){this._context=e}function pF(e){this._context=e}function pU(e){this._context=e}pB.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x0=this._x1=this._y0=this._y1=NaN,this._point=0},lineEnd:function(){switch(this._point){case 3:pL(this,this._x1,this._y1);case 2:this._context.lineTo(this._x1,this._y1)}(this._line||0!==this._line&&1===this._point)&&this._context.closePath(),this._line=1-this._line},point:function(e,t){switch(e=+e,t=+t,this._point){case 0:this._point=1,this._line?this._context.lineTo(e,t):this._context.moveTo(e,t);break;case 1:this._point=2;break;case 2:this._point=3,this._context.lineTo((5*this._x0+this._x1)/6,(5*this._y0+this._y1)/6);default:pL(this,e,t)}this._x0=this._x1,this._x1=e,this._y0=this._y1,this._y1=t}},pF.prototype={areaStart:pR,areaEnd:pR,lineStart:function(){this._x0=this._x1=this._x2=this._x3=this._x4=this._y0=this._y1=this._y2=this._y3=this._y4=NaN,this._point=0},lineEnd:function(){switch(this._point){case 1:this._context.moveTo(this._x2,this._y2),this._context.closePath();break;case 2:this._context.moveTo((this._x2+2*this._x3)/3,(this._y2+2*this._y3)/3),this._context.lineTo((this._x3+2*this._x2)/3,(this._y3+2*this._y2)/3),this._context.closePath();break;case 3:this.point(this._x2,this._y2),this.point(this._x3,this._y3),this.point(this._x4,this._y4)}},point:function(e,t){switch(e=+e,t=+t,this._point){case 0:this._point=1,this._x2=e,this._y2=t;break;case 1:this._point=2,this._x3=e,this._y3=t;break;case 2:this._point=3,this._x4=e,this._y4=t,this._context.moveTo((this._x0+4*this._x1+e)/6,(this._y0+4*this._y1+t)/6);break;default:pL(this,e,t)}this._x0=this._x1,this._x1=e,this._y0=this._y1,this._y1=t}},pU.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x0=this._x1=this._y0=this._y1=NaN,this._point=0},lineEnd:function(){(this._line||0!==this._line&&3===this._point)&&this._context.closePath(),this._line=1-this._line},point:function(e,t){switch(e=+e,t=+t,this._point){case 0:this._point=1;break;case 1:this._point=2;break;case 2:this._point=3;var r=(this._x0+4*this._x1+e)/6,n=(this._y0+4*this._y1+t)/6;this._line?this._context.lineTo(r,n):this._context.moveTo(r,n);break;case 3:this._point=4;default:pL(this,e,t)}this._x0=this._x1,this._x1=e,this._y0=this._y1,this._y1=t}};class pz{constructor(e,t){this._context=e,this._x=t}areaStart(){this._line=0}areaEnd(){this._line=NaN}lineStart(){this._point=0}lineEnd(){(this._line||0!==this._line&&1===this._point)&&this._context.closePath(),this._line=1-this._line}point(e,t){switch(e=+e,t=+t,this._point){case 0:this._point=1,this._line?this._context.lineTo(e,t):this._context.moveTo(e,t);break;case 1:this._point=2;default:this._x?this._context.bezierCurveTo(this._x0=(this._x0+e)/2,this._y0,this._x0,t,e,t):this._context.bezierCurveTo(this._x0,this._y0=(this._y0+t)/2,e,this._y0,e,t)}this._x0=e,this._y0=t}}function p$(e){this._context=e}function pW(e){this._context=e}function pq(e){return new pW(e)}function pH(e,t,r){var n=e._x1-e._x0,o=t-e._x1,i=(e._y1-e._y0)/(n||o<0&&-0),a=(r-e._y1)/(o||n<0&&-0);return((i<0?-1:1)+(a<0?-1:1))*Math.min(Math.abs(i),Math.abs(a),.5*Math.abs((i*o+a*n)/(n+o)))||0}function pV(e,t){var r=e._x1-e._x0;return r?(3*(e._y1-e._y0)/r-t)/2:t}function pX(e,t,r){var n=e._x0,o=e._y0,i=e._x1,a=e._y1,u=(i-n)/3;e._context.bezierCurveTo(n+u,o+u*t,i-u,a-u*r,i,a)}function pZ(e){this._context=e}function pG(e){this._context=new pY(e)}function pY(e){this._context=e}function pK(e){this._context=e}function pQ(e){var t,r,n=e.length-1,o=Array(n),i=Array(n),a=Array(n);for(o[0]=0,i[0]=2,a[0]=e[0]+2*e[1],t=1;t<n-1;++t)o[t]=1,i[t]=4,a[t]=4*e[t]+2*e[t+1];for(o[n-1]=2,i[n-1]=7,a[n-1]=8*e[n-1]+e[n],t=1;t<n;++t)r=o[t]/i[t-1],i[t]-=r,a[t]-=r*a[t-1];for(o[n-1]=a[n-1]/i[n-1],t=n-2;t>=0;--t)o[t]=(a[t]-o[t+1])/i[t];for(t=0,i[n-1]=(e[n]+o[n-1])/2;t<n-1;++t)i[t]=2*e[t+1]-o[t+1];return[o,i]}function pJ(e,t){this._context=e,this._t=t}function p0(e){return e[0]}function p1(e){return e[1]}function p2(e,t){var r=tT(!0),n=null,o=pq,i=null,a=tR(u);function u(u){var c,l,s,f=(u=uz(u)).length,p=!1;for(null==n&&(i=o(s=a())),c=0;c<=f;++c)!(c<f&&r(l=u[c],c,u))===p&&((p=!p)?i.lineStart():i.lineEnd()),p&&i.point(+e(l,c,u),+t(l,c,u));if(s)return i=null,s+""||null}return e="function"==typeof e?e:void 0===e?p0:tT(e),t="function"==typeof t?t:void 0===t?p1:tT(t),u.x=function(t){return arguments.length?(e="function"==typeof t?t:tT(+t),u):e},u.y=function(e){return arguments.length?(t="function"==typeof e?e:tT(+e),u):t},u.defined=function(e){return arguments.length?(r="function"==typeof e?e:tT(!!e),u):r},u.curve=function(e){return arguments.length?(o=e,null!=n&&(i=o(n)),u):o},u.context=function(e){return arguments.length?(null==e?n=i=null:i=o(n=e),u):n},u}function p5(e,t,r){var n=null,o=tT(!0),i=null,a=pq,u=null,c=tR(l);function l(l){var s,f,p,d,h,y=(l=uz(l)).length,v=!1,m=Array(y),g=Array(y);for(null==i&&(u=a(h=c())),s=0;s<=y;++s){if(!(s<y&&o(d=l[s],s,l))===v){if(v=!v)f=s,u.areaStart(),u.lineStart();else{for(u.lineEnd(),u.lineStart(),p=s-1;p>=f;--p)u.point(m[p],g[p]);u.lineEnd(),u.areaEnd()}}v&&(m[s]=+e(d,s,l),g[s]=+t(d,s,l),u.point(n?+n(d,s,l):m[s],r?+r(d,s,l):g[s]))}if(h)return u=null,h+""||null}function s(){return p2().defined(o).curve(a).context(i)}return e="function"==typeof e?e:void 0===e?p0:tT(+e),t="function"==typeof t?t:void 0===t?tT(0):tT(+t),r="function"==typeof r?r:void 0===r?p1:tT(+r),l.x=function(t){return arguments.length?(e="function"==typeof t?t:tT(+t),n=null,l):e},l.x0=function(t){return arguments.length?(e="function"==typeof t?t:tT(+t),l):e},l.x1=function(e){return arguments.length?(n=null==e?null:"function"==typeof e?e:tT(+e),l):n},l.y=function(e){return arguments.length?(t="function"==typeof e?e:tT(+e),r=null,l):t},l.y0=function(e){return arguments.length?(t="function"==typeof e?e:tT(+e),l):t},l.y1=function(e){return arguments.length?(r=null==e?null:"function"==typeof e?e:tT(+e),l):r},l.lineX0=l.lineY0=function(){return s().x(e).y(t)},l.lineY1=function(){return s().x(e).y(r)},l.lineX1=function(){return s().x(n).y(t)},l.defined=function(e){return arguments.length?(o="function"==typeof e?e:tT(!!e),l):o},l.curve=function(e){return arguments.length?(a=e,null!=i&&(u=a(i)),l):a},l.context=function(e){return arguments.length?(null==e?i=u=null:u=a(i=e),l):i},l}function p3(e){return(p3="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function p4(){return(p4=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(this,arguments)}function p6(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function p7(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?p6(Object(r),!0).forEach(function(t){var n,o;n=t,o=r[t],(n=function(e){var t=function(e,t){if("object"!=p3(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=p3(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==p3(t)?t:t+""}(n))in e?Object.defineProperty(e,n,{value:o,enumerable:!0,configurable:!0,writable:!0}):e[n]=o}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):p6(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}p$.prototype={areaStart:pR,areaEnd:pR,lineStart:function(){this._point=0},lineEnd:function(){this._point&&this._context.closePath()},point:function(e,t){e=+e,t=+t,this._point?this._context.lineTo(e,t):(this._point=1,this._context.moveTo(e,t))}},pW.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._point=0},lineEnd:function(){(this._line||0!==this._line&&1===this._point)&&this._context.closePath(),this._line=1-this._line},point:function(e,t){switch(e=+e,t=+t,this._point){case 0:this._point=1,this._line?this._context.lineTo(e,t):this._context.moveTo(e,t);break;case 1:this._point=2;default:this._context.lineTo(e,t)}}},pZ.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x0=this._x1=this._y0=this._y1=this._t0=NaN,this._point=0},lineEnd:function(){switch(this._point){case 2:this._context.lineTo(this._x1,this._y1);break;case 3:pX(this,this._t0,pV(this,this._t0))}(this._line||0!==this._line&&1===this._point)&&this._context.closePath(),this._line=1-this._line},point:function(e,t){var r=NaN;if(t=+t,(e=+e)!==this._x1||t!==this._y1){switch(this._point){case 0:this._point=1,this._line?this._context.lineTo(e,t):this._context.moveTo(e,t);break;case 1:this._point=2;break;case 2:this._point=3,pX(this,pV(this,r=pH(this,e,t)),r);break;default:pX(this,this._t0,r=pH(this,e,t))}this._x0=this._x1,this._x1=e,this._y0=this._y1,this._y1=t,this._t0=r}}},(pG.prototype=Object.create(pZ.prototype)).point=function(e,t){pZ.prototype.point.call(this,t,e)},pY.prototype={moveTo:function(e,t){this._context.moveTo(t,e)},closePath:function(){this._context.closePath()},lineTo:function(e,t){this._context.lineTo(t,e)},bezierCurveTo:function(e,t,r,n,o,i){this._context.bezierCurveTo(t,e,n,r,i,o)}},pK.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x=[],this._y=[]},lineEnd:function(){var e=this._x,t=this._y,r=e.length;if(r){if(this._line?this._context.lineTo(e[0],t[0]):this._context.moveTo(e[0],t[0]),2===r)this._context.lineTo(e[1],t[1]);else for(var n=pQ(e),o=pQ(t),i=0,a=1;a<r;++i,++a)this._context.bezierCurveTo(n[0][i],o[0][i],n[1][i],o[1][i],e[a],t[a])}(this._line||0!==this._line&&1===r)&&this._context.closePath(),this._line=1-this._line,this._x=this._y=null},point:function(e,t){this._x.push(+e),this._y.push(+t)}},pJ.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x=this._y=NaN,this._point=0},lineEnd:function(){0<this._t&&this._t<1&&2===this._point&&this._context.lineTo(this._x,this._y),(this._line||0!==this._line&&1===this._point)&&this._context.closePath(),this._line>=0&&(this._t=1-this._t,this._line=1-this._line)},point:function(e,t){switch(e=+e,t=+t,this._point){case 0:this._point=1,this._line?this._context.lineTo(e,t):this._context.moveTo(e,t);break;case 1:this._point=2;default:if(this._t<=0)this._context.lineTo(this._x,t),this._context.lineTo(e,t);else{var r=this._x*(1-this._t)+e*this._t;this._context.lineTo(r,this._y),this._context.lineTo(r,t)}}this._x=e,this._y=t}};var p8={curveBasisClosed:function(e){return new pF(e)},curveBasisOpen:function(e){return new pU(e)},curveBasis:function(e){return new pB(e)},curveBumpX:function(e){return new pz(e,!0)},curveBumpY:function(e){return new pz(e,!1)},curveLinearClosed:function(e){return new p$(e)},curveLinear:pq,curveMonotoneX:function(e){return new pZ(e)},curveMonotoneY:function(e){return new pG(e)},curveNatural:function(e){return new pK(e)},curveStep:function(e){return new pJ(e,.5)},curveStepAfter:function(e){return new pJ(e,1)},curveStepBefore:function(e){return new pJ(e,0)}},p9=function(e){return e.x===+e.x&&e.y===+e.y},de=function(e){return e.x},dt=function(e){return e.y},dr=function(e,t){if(X()(e))return e;var r="curve".concat(th()(e));return("curveMonotone"===r||"curveBump"===r)&&t?p8["".concat(r).concat("vertical"===t?"Y":"X")]:p8[r]||pq},dn=function(e){var t,r=e.type,n=e.points,o=void 0===n?[]:n,i=e.baseLine,a=e.layout,u=e.connectNulls,c=void 0!==u&&u,l=dr(void 0===r?"linear":r,a),s=c?o.filter(function(e){return p9(e)}):o;if(Array.isArray(i)){var f=c?i.filter(function(e){return p9(e)}):i,p=s.map(function(e,t){return p7(p7({},e),{},{base:f[t]})});return(t="vertical"===a?p5().y(dt).x1(de).x0(function(e){return e.base.x}):p5().x(de).y1(dt).y0(function(e){return e.base.y})).defined(p9).curve(l),t(p)}return(t="vertical"===a&&N(i)?p5().y(dt).x1(de).x0(i):N(i)?p5().x(de).y1(dt).y0(i):p2().x(de).y(dt)).defined(p9).curve(l),t(s)},di=function(e){var t=e.className,r=e.points,n=e.path,o=e.pathRef;if((!r||!r.length)&&!n)return null;var i=r&&r.length?dn(e):n;return l().createElement("path",p4({},eb(e,!1),en(e),{className:(0,x.Z)("recharts-curve",t),d:i,ref:o}))};function da(e){return(da="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}var du=["x","y","top","left","width","height","className"];function dc(){return(dc=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(this,arguments)}function dl(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}var ds=function(e){var t=e.x,r=void 0===t?0:t,n=e.y,o=void 0===n?0:n,i=e.top,a=void 0===i?0:i,u=e.left,c=void 0===u?0:u,s=e.width,f=void 0===s?0:s,p=e.height,d=void 0===p?0:p,h=e.className,y=function(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?dl(Object(r),!0).forEach(function(t){var n,o;n=t,o=r[t],(n=function(e){var t=function(e,t){if("object"!=da(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=da(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==da(t)?t:t+""}(n))in e?Object.defineProperty(e,n,{value:o,enumerable:!0,configurable:!0,writable:!0}):e[n]=o}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):dl(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}({x:r,y:o,top:a,left:c,width:f,height:d},function(e,t){if(null==e)return{};var r,n,o=function(e,t){if(null==e)return{};var r={};for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){if(t.indexOf(n)>=0)continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(n=0;n<i.length;n++)r=i[n],!(t.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(e,r)&&(o[r]=e[r])}return o}(e,du));return N(r)&&N(o)&&N(f)&&N(d)&&N(a)&&N(c)?l().createElement("path",dc({},eb(y,!0),{className:(0,x.Z)("recharts-cross",h),d:"M".concat(r,",").concat(a,"v").concat(d,"M").concat(c,",").concat(o,"h").concat(f)})):null};function df(e){var t=e.cx,r=e.cy,n=e.radius,o=e.startAngle,i=e.endAngle;return{points:[l7(t,r,n,o),l7(t,r,n,i)],cx:t,cy:r,radius:n,startAngle:o,endAngle:i}}function dp(e){return(dp="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function dd(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function dh(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?dd(Object(r),!0).forEach(function(t){var n,o;n=t,o=r[t],(n=function(e){var t=function(e,t){if("object"!=dp(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=dp(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==dp(t)?t:t+""}(n))in e?Object.defineProperty(e,n,{value:o,enumerable:!0,configurable:!0,writable:!0}):e[n]=o}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):dd(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function dy(e){var t,r,n,o,i=e.element,a=e.tooltipEventType,u=e.isActive,l=e.activeCoordinate,s=e.activePayload,f=e.offset,p=e.activeTooltipIndex,d=e.tooltipAxisBandSize,h=e.layout,y=e.chartName,v=null!==(r=i.props.cursor)&&void 0!==r?r:null===(n=i.type.defaultProps)||void 0===n?void 0:n.cursor;if(!i||!v||!u||!l||"ScatterChart"!==y&&"axis"!==a)return null;var m=di;if("ScatterChart"===y)o=l,m=ds;else if("BarChart"===y)t=d/2,o={stroke:"none",fill:"#ccc",x:"horizontal"===h?l.x-t:f.left+.5,y:"horizontal"===h?f.top+.5:l.y-t,width:"horizontal"===h?d:f.width-1,height:"horizontal"===h?f.height-1:d},m=nS;else if("radial"===h){var g=df(l),b=g.cx,w=g.cy,O=g.radius;o={cx:b,cy:w,startAngle:g.startAngle,endAngle:g.endAngle,innerRadius:O,outerRadius:O},m=sQ}else o={points:function(e,t,r){var n,o,i,a;if("horizontal"===e)i=n=t.x,o=r.top,a=r.top+r.height;else if("vertical"===e)a=o=t.y,n=r.left,i=r.left+r.width;else if(null!=t.cx&&null!=t.cy){if("centric"!==e)return df(t);var u=t.cx,c=t.cy,l=t.innerRadius,s=t.outerRadius,f=t.angle,p=l7(u,c,l,f),d=l7(u,c,s,f);n=p.x,o=p.y,i=d.x,a=d.y}return[{x:n,y:o},{x:i,y:a}]}(h,l,f)},m=di;var j=dh(dh(dh(dh({stroke:"#ccc",pointerEvents:"none"},f),o),eb(v,!1)),{},{payload:s,payloadIndex:p,className:(0,x.Z)("recharts-tooltip-cursor",v.className)});return(0,c.isValidElement)(v)?(0,c.cloneElement)(v,j):(0,c.createElement)(m,j)}var dv=["item"],dm=["children","className","width","height","style","compact","title","desc"];function dg(e){return(dg="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function db(){return(db=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(this,arguments)}function dx(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var r=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=r){var n,o,i,a,u=[],c=!0,l=!1;try{if(i=(r=r.call(e)).next,0===t){if(Object(r)!==r)return;c=!1}else for(;!(c=(n=i.call(r)).done)&&(u.push(n.value),u.length!==t);c=!0);}catch(e){l=!0,o=e}finally{try{if(!c&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(l)throw o}}return u}}(e,t)||dk(e,t)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function dw(e,t){if(null==e)return{};var r,n,o=function(e,t){if(null==e)return{};var r={};for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){if(t.indexOf(n)>=0)continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(n=0;n<i.length;n++)r=i[n],!(t.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(e,r)&&(o[r]=e[r])}return o}function dO(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,dC(n.key),n)}}function dj(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(e){}return(dj=function(){return!!e})()}function dS(e){return(dS=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)})(e)}function dP(e,t){return(dP=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e})(e,t)}function dE(e){return function(e){if(Array.isArray(e))return dA(e)}(e)||function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(e)||dk(e)||function(){throw TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function dk(e,t){if(e){if("string"==typeof e)return dA(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);if("Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r)return Array.from(e);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return dA(e,t)}}function dA(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=Array(t);r<t;r++)n[r]=e[r];return n}function dM(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function dT(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?dM(Object(r),!0).forEach(function(t){d_(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):dM(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function d_(e,t,r){return(t=dC(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function dC(e){var t=function(e,t){if("object"!=dg(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=dg(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==dg(t)?t:t+""}var dN={xAxis:["bottom","top"],yAxis:["left","right"]},dD={width:"100%",height:"100%"},dI={x:0,y:0};function dR(e){return e}var dL=function(e,t,r,n){var o=t.find(function(e){return e&&e.index===r});if(o){if("horizontal"===e)return{x:o.coordinate,y:n.y};if("vertical"===e)return{x:n.x,y:o.coordinate};if("centric"===e){var i=o.coordinate,a=n.radius;return dT(dT(dT({},n),l7(n.cx,n.cy,a,i)),{},{angle:i,radius:a})}var u=o.coordinate,c=n.angle;return dT(dT(dT({},n),l7(n.cx,n.cy,u,c)),{},{angle:c,radius:u})}return dI},dB=function(e,t){var r=t.graphicalItems,n=t.dataStartIndex,o=t.dataEndIndex,i=(null!=r?r:[]).reduce(function(e,t){var r=t.props.data;return r&&r.length?[].concat(dE(e),dE(r)):e},[]);return i.length>0?i:e&&e.length&&N(n)&&N(o)?e.slice(n,o+1):[]};function dF(e){return"number"===e?[0,"auto"]:void 0}var dU=function(e,t,r,n){var o=e.graphicalItems,i=e.tooltipAxis,a=dB(t,e);return r<0||!o||!o.length||r>=a.length?null:o.reduce(function(o,u){var c,l,s=null!==(c=u.props.data)&&void 0!==c?c:t;return(s&&e.dataStartIndex+e.dataEndIndex!==0&&e.dataEndIndex-e.dataStartIndex>=r&&(s=s.slice(e.dataStartIndex,e.dataEndIndex+1)),l=i.dataKey&&!i.allowDuplicatedCategory?z(void 0===s?a:s,i.dataKey,n):s&&s[r]||a[r])?[].concat(dE(o),[lL(u,l)]):o},[])},dz=function(e,t,r,n){var o=n||{x:e.chartX,y:e.chartY},i="horizontal"===r?o.x:"vertical"===r?o.y:"centric"===r?o.angle:o.radius,a=e.orderedTooltipTicks,u=e.tooltipAxis,c=e.tooltipTicks,l=la(i,a,c,u);if(l>=0&&c){var s=c[l]&&c[l].value,f=dU(e,t,l,s),p=dL(r,a,l,o);return{activeTooltipIndex:l,activeLabel:s,activePayload:f,activeCoordinate:p}}return null},d$=function(e,t){var r=t.axes,n=t.graphicalItems,o=t.axisType,i=t.axisIdKey,a=t.stackGroups,u=t.dataStartIndex,c=t.dataEndIndex,l=e.layout,s=e.children,f=e.stackOffset,p=lh(l,o);return r.reduce(function(t,r){var d=void 0!==r.type.defaultProps?dT(dT({},r.type.defaultProps),r.props):r.props,h=d.type,y=d.dataKey,v=d.allowDataOverflow,m=d.allowDuplicatedCategory,g=d.scale,b=d.ticks,x=d.includeHidden,w=d[i];if(t[w])return t;var O=dB(e.data,{graphicalItems:n.filter(function(e){var t;return(i in e.props?e.props[i]:null===(t=e.type.defaultProps)||void 0===t?void 0:t[i])===w}),dataStartIndex:u,dataEndIndex:c}),j=O.length;(function(e,t,r){if("number"===r&&!0===t&&Array.isArray(e)){var n=null==e?void 0:e[0],o=null==e?void 0:e[1];if(n&&o&&N(n)&&N(o))return!0}return!1})(d.domain,v,h)&&(E=lD(d.domain,null,v),p&&("number"===h||"auto"!==g)&&(A=li(O,y,"category")));var S=dF(h);if(!E||0===E.length){var P,E,k,A,M,T=null!==(M=d.domain)&&void 0!==M?M:S;if(y){if(E=li(O,y,h),"category"===h&&p){var _=F(E);m&&_?(k=E,E=eT()(0,j)):m||(E=lR(T,E,r).reduce(function(e,t){return e.indexOf(t)>=0?e:[].concat(dE(e),[t])},[]))}else if("category"===h)E=m?E.filter(function(e){return""!==e&&!H()(e)}):lR(T,E,r).reduce(function(e,t){return e.indexOf(t)>=0||""===t||H()(t)?e:[].concat(dE(e),[t])},[]);else if("number"===h){var C=lp(O,n.filter(function(e){var t,r,n=i in e.props?e.props[i]:null===(t=e.type.defaultProps)||void 0===t?void 0:t[i],o="hide"in e.props?e.props.hide:null===(r=e.type.defaultProps)||void 0===r?void 0:r.hide;return n===w&&(x||!o)}),y,o,l);C&&(E=C)}p&&("number"===h||"auto"!==g)&&(A=li(O,y,"category"))}else E=p?eT()(0,j):a&&a[w]&&a[w].hasStack&&"number"===h?"expand"===f?[0,1]:l_(a[w].stackGroups,u,c):ld(O,n.filter(function(e){var t=i in e.props?e.props[i]:e.type.defaultProps[i],r="hide"in e.props?e.props.hide:e.type.defaultProps.hide;return t===w&&(x||!r)}),h,l,!0);"number"===h?(E=pk(s,E,w,o,b),T&&(E=lD(T,E,v))):"category"===h&&T&&E.every(function(e){return T.indexOf(e)>=0})&&(E=T)}return dT(dT({},t),{},d_({},w,dT(dT({},d),{},{axisType:o,domain:E,categoricalDomain:A,duplicateDomain:k,originalDomain:null!==(P=d.domain)&&void 0!==P?P:S,isCategorical:p,layout:l})))},{})},dW=function(e,t){var r=t.graphicalItems,n=t.Axis,o=t.axisType,i=t.axisIdKey,a=t.stackGroups,u=t.dataStartIndex,c=t.dataEndIndex,l=e.layout,s=e.children,f=dB(e.data,{graphicalItems:r,dataStartIndex:u,dataEndIndex:c}),p=f.length,d=lh(l,o),h=-1;return r.reduce(function(e,t){var y,v=(void 0!==t.type.defaultProps?dT(dT({},t.type.defaultProps),t.props):t.props)[i],m=dF("number");return e[v]?e:(h++,y=d?eT()(0,p):a&&a[v]&&a[v].hasStack?pk(s,y=l_(a[v].stackGroups,u,c),v,o):pk(s,y=lD(m,ld(f,r.filter(function(e){var t,r,n=i in e.props?e.props[i]:null===(t=e.type.defaultProps)||void 0===t?void 0:t[i],o="hide"in e.props?e.props.hide:null===(r=e.type.defaultProps)||void 0===r?void 0:r.hide;return n===v&&!o}),"number",l),n.defaultProps.allowDataOverflow),v,o),dT(dT({},e),{},d_({},v,dT(dT({axisType:o},n.defaultProps),{},{hide:!0,orientation:A()(dN,"".concat(o,".").concat(h%2),null),domain:y,originalDomain:m,isCategorical:d,layout:l}))))},{})},dq=function(e,t){var r=t.axisType,n=void 0===r?"xAxis":r,o=t.AxisComp,i=t.graphicalItems,a=t.stackGroups,u=t.dataStartIndex,c=t.dataEndIndex,l=e.children,s="".concat(n,"Id"),f=eh(l,o),p={};return f&&f.length?p=d$(e,{axes:f,graphicalItems:i,axisType:n,axisIdKey:s,stackGroups:a,dataStartIndex:u,dataEndIndex:c}):i&&i.length&&(p=dW(e,{Axis:o,graphicalItems:i,axisType:n,axisIdKey:s,stackGroups:a,dataStartIndex:u,dataEndIndex:c})),p},dH=function(e){var t=B(e),r=lv(t,!1,!0);return{tooltipTicks:r,orderedTooltipTicks:eC()(r,function(e){return e.coordinate}),tooltipAxis:t,tooltipAxisBandSize:lI(t,r)}},dV=function(e){var t=e.children,r=e.defaultShowTooltip,n=ey(t,l2),o=0,i=0;return e.data&&0!==e.data.length&&(i=e.data.length-1),n&&n.props&&(n.props.startIndex>=0&&(o=n.props.startIndex),n.props.endIndex>=0&&(i=n.props.endIndex)),{chartX:0,chartY:0,dataStartIndex:o,dataEndIndex:i,activeTooltipIndex:-1,isTooltipActive:!!r}},dX=function(e){return"horizontal"===e?{numericAxisName:"yAxis",cateAxisName:"xAxis"}:"vertical"===e?{numericAxisName:"xAxis",cateAxisName:"yAxis"}:"centric"===e?{numericAxisName:"radiusAxis",cateAxisName:"angleAxis"}:{numericAxisName:"angleAxis",cateAxisName:"radiusAxis"}},dZ=function(e,t){var r=e.props,n=e.graphicalItems,o=e.xAxisMap,i=void 0===o?{}:o,a=e.yAxisMap,u=void 0===a?{}:a,c=r.width,l=r.height,s=r.children,f=r.margin||{},p=ey(s,l2),d=ey(s,rn),h=Object.keys(u).reduce(function(e,t){var r=u[t],n=r.orientation;return r.mirror||r.hide?e:dT(dT({},e),{},d_({},n,e[n]+r.width))},{left:f.left||0,right:f.right||0}),y=Object.keys(i).reduce(function(e,t){var r=i[t],n=r.orientation;return r.mirror||r.hide?e:dT(dT({},e),{},d_({},n,A()(e,"".concat(n))+r.height))},{top:f.top||0,bottom:f.bottom||0}),v=dT(dT({},y),h),m=v.bottom;p&&(v.bottom+=p.props.height||l2.defaultProps.height),d&&t&&(v=ls(v,n,r,t));var g=c-v.left-v.right,b=l-v.top-v.bottom;return dT(dT({brushBottom:m},v),{},{width:Math.max(g,0),height:Math.max(b,0)})},dG=function(e){var t=e.chartName,r=e.GraphicalChild,n=e.defaultTooltipEventType,o=void 0===n?"axis":n,i=e.validateTooltipEventTypes,a=void 0===i?["axis"]:i,u=e.axisComponents,s=e.legendContent,f=e.formatAxisMap,p=e.defaultProps,d=function(e,t){var r=t.graphicalItems,n=t.stackGroups,o=t.offset,i=t.updateId,a=t.dataStartIndex,c=t.dataEndIndex,l=e.barSize,s=e.layout,f=e.barGap,p=e.barCategoryGap,d=e.maxBarSize,h=dX(s),y=h.numericAxisName,v=h.cateAxisName,m=!!r&&!!r.length&&r.some(function(e){var t=es(e&&e.type);return t&&t.indexOf("Bar")>=0}),g=[];return r.forEach(function(r,h){var b=dB(e.data,{graphicalItems:[r],dataStartIndex:a,dataEndIndex:c}),x=void 0!==r.type.defaultProps?dT(dT({},r.type.defaultProps),r.props):r.props,w=x.dataKey,O=x.maxBarSize,j=x["".concat(y,"Id")],S=x["".concat(v,"Id")],P=u.reduce(function(e,r){var n=t["".concat(r.axisType,"Map")],o=x["".concat(r.axisType,"Id")];n&&n[o]||"zAxis"===r.axisType||eN(!1);var i=n[o];return dT(dT({},e),{},d_(d_({},r.axisType,i),"".concat(r.axisType,"Ticks"),lv(i)))},{}),E=P[v],k=P["".concat(v,"Ticks")],A=n&&n[j]&&n[j].hasStack&&lT(r,n[j].stackGroups),M=es(r.type).indexOf("Bar")>=0,T=lI(E,k),_=[],C=m&&lc({barSize:l,stackGroups:n,totalSize:"xAxis"===v?P[v].width:"yAxis"===v?P[v].height:void 0});if(M){var N,D,I=H()(O)?d:O,R=null!==(N=null!==(D=lI(E,k,!0))&&void 0!==D?D:I)&&void 0!==N?N:0;_=ll({barGap:f,barCategoryGap:p,bandSize:R!==T?R:T,sizeList:C[S],maxBarSize:I}),R!==T&&(_=_.map(function(e){return dT(dT({},e),{},{position:dT(dT({},e.position),{},{offset:e.position.offset-R/2})})}))}var L=r&&r.type&&r.type.getComposedData;L&&g.push({props:dT(dT({},L(dT(dT({},P),{},{displayedData:b,props:e,dataKey:w,item:r,bandSize:T,barPosition:_,offset:o,stackedData:A,layout:s,dataStartIndex:a,dataEndIndex:c}))),{},d_(d_(d_({key:r.key||"item-".concat(h)},y,P[y]),v,P[v]),"animationId",i)),childIndex:ed(e.children).indexOf(r),item:r})}),g},h=function(e,n){var o=e.props,i=e.dataStartIndex,a=e.dataEndIndex,c=e.updateId;if(!ev({props:o}))return null;var l=o.children,s=o.layout,p=o.stackOffset,h=o.data,y=o.reverseStackOrder,v=dX(s),m=v.numericAxisName,g=v.cateAxisName,b=eh(l,r),x=lP(h,b,"".concat(m,"Id"),"".concat(g,"Id"),p,y),w=u.reduce(function(e,t){var r="".concat(t.axisType,"Map");return dT(dT({},e),{},d_({},r,dq(o,dT(dT({},t),{},{graphicalItems:b,stackGroups:t.axisType===m&&x,dataStartIndex:i,dataEndIndex:a}))))},{}),O=dZ(dT(dT({},w),{},{props:o,graphicalItems:b}),null==n?void 0:n.legendBBox);Object.keys(w).forEach(function(e){w[e]=f(o,w[e],O,e.replace("Map",""),t)});var j=dH(w["".concat(g,"Map")]),S=d(o,dT(dT({},w),{},{dataStartIndex:i,dataEndIndex:a,updateId:c,graphicalItems:b,stackGroups:x,offset:O}));return dT(dT({formattedGraphicalItems:S,graphicalItems:b,offset:O,stackGroups:x},j),w)},y=function(e){var r,n;function i(e){var r,n,o,a,u;return function(e,t){if(!(e instanceof t))throw TypeError("Cannot call a class as a function")}(this,i),a=i,u=[e],a=dS(a),d_(o=function(e,t){if(t&&("object"===dg(t)||"function"==typeof t))return t;if(void 0!==t)throw TypeError("Derived constructors may only return object or undefined");return function(e){if(void 0===e)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return e}(e)}(this,dj()?Reflect.construct(a,u||[],dS(this).constructor):a.apply(this,u)),"eventEmitterSymbol",Symbol("rechartsEventEmitter")),d_(o,"accessibilityManager",new pI),d_(o,"handleLegendBBoxUpdate",function(e){if(e){var t=o.state,r=t.dataStartIndex,n=t.dataEndIndex,i=t.updateId;o.setState(dT({legendBBox:e},h({props:o.props,dataStartIndex:r,dataEndIndex:n,updateId:i},dT(dT({},o.state),{},{legendBBox:e}))))}}),d_(o,"handleReceiveSyncEvent",function(e,t,r){o.props.syncId===e&&(r!==o.eventEmitterSymbol||"function"==typeof o.props.syncMethod)&&o.applySyncEvent(t)}),d_(o,"handleBrushChange",function(e){var t=e.startIndex,r=e.endIndex;if(t!==o.state.dataStartIndex||r!==o.state.dataEndIndex){var n=o.state.updateId;o.setState(function(){return dT({dataStartIndex:t,dataEndIndex:r},h({props:o.props,dataStartIndex:t,dataEndIndex:r,updateId:n},o.state))}),o.triggerSyncEvent({dataStartIndex:t,dataEndIndex:r})}}),d_(o,"handleMouseEnter",function(e){var t=o.getMouseInfo(e);if(t){var r=dT(dT({},t),{},{isTooltipActive:!0});o.setState(r),o.triggerSyncEvent(r);var n=o.props.onMouseEnter;X()(n)&&n(r,e)}}),d_(o,"triggeredAfterMouseMove",function(e){var t=o.getMouseInfo(e),r=t?dT(dT({},t),{},{isTooltipActive:!0}):{isTooltipActive:!1};o.setState(r),o.triggerSyncEvent(r);var n=o.props.onMouseMove;X()(n)&&n(r,e)}),d_(o,"handleItemMouseEnter",function(e){o.setState(function(){return{isTooltipActive:!0,activeItem:e,activePayload:e.tooltipPayload,activeCoordinate:e.tooltipPosition||{x:e.cx,y:e.cy}}})}),d_(o,"handleItemMouseLeave",function(){o.setState(function(){return{isTooltipActive:!1}})}),d_(o,"handleMouseMove",function(e){e.persist(),o.throttleTriggeredAfterMouseMove(e)}),d_(o,"handleMouseLeave",function(e){o.throttleTriggeredAfterMouseMove.cancel();var t={isTooltipActive:!1};o.setState(t),o.triggerSyncEvent(t);var r=o.props.onMouseLeave;X()(r)&&r(t,e)}),d_(o,"handleOuterEvent",function(e){var t,r=ej(e),n=A()(o.props,"".concat(r));r&&X()(n)&&n(null!==(t=/.*touch.*/i.test(r)?o.getMouseInfo(e.changedTouches[0]):o.getMouseInfo(e))&&void 0!==t?t:{},e)}),d_(o,"handleClick",function(e){var t=o.getMouseInfo(e);if(t){var r=dT(dT({},t),{},{isTooltipActive:!0});o.setState(r),o.triggerSyncEvent(r);var n=o.props.onClick;X()(n)&&n(r,e)}}),d_(o,"handleMouseDown",function(e){var t=o.props.onMouseDown;X()(t)&&t(o.getMouseInfo(e),e)}),d_(o,"handleMouseUp",function(e){var t=o.props.onMouseUp;X()(t)&&t(o.getMouseInfo(e),e)}),d_(o,"handleTouchMove",function(e){null!=e.changedTouches&&e.changedTouches.length>0&&o.throttleTriggeredAfterMouseMove(e.changedTouches[0])}),d_(o,"handleTouchStart",function(e){null!=e.changedTouches&&e.changedTouches.length>0&&o.handleMouseDown(e.changedTouches[0])}),d_(o,"handleTouchEnd",function(e){null!=e.changedTouches&&e.changedTouches.length>0&&o.handleMouseUp(e.changedTouches[0])}),d_(o,"handleDoubleClick",function(e){var t=o.props.onDoubleClick;X()(t)&&t(o.getMouseInfo(e),e)}),d_(o,"handleContextMenu",function(e){var t=o.props.onContextMenu;X()(t)&&t(o.getMouseInfo(e),e)}),d_(o,"triggerSyncEvent",function(e){void 0!==o.props.syncId&&pM.emit(pT,o.props.syncId,e,o.eventEmitterSymbol)}),d_(o,"applySyncEvent",function(e){var t=o.props,r=t.layout,n=t.syncMethod,i=o.state.updateId,a=e.dataStartIndex,u=e.dataEndIndex;if(void 0!==e.dataStartIndex||void 0!==e.dataEndIndex)o.setState(dT({dataStartIndex:a,dataEndIndex:u},h({props:o.props,dataStartIndex:a,dataEndIndex:u,updateId:i},o.state)));else if(void 0!==e.activeTooltipIndex){var c=e.chartX,l=e.chartY,s=e.activeTooltipIndex,f=o.state,p=f.offset,d=f.tooltipTicks;if(!p)return;if("function"==typeof n)s=n(d,e);else if("value"===n){s=-1;for(var y=0;y<d.length;y++)if(d[y].value===e.activeLabel){s=y;break}}var v=dT(dT({},p),{},{x:p.left,y:p.top}),m=Math.min(c,v.x+v.width),g=Math.min(l,v.y+v.height),b=d[s]&&d[s].value,x=dU(o.state,o.props.data,s),w=d[s]?{x:"horizontal"===r?d[s].coordinate:m,y:"horizontal"===r?g:d[s].coordinate}:dI;o.setState(dT(dT({},e),{},{activeLabel:b,activeCoordinate:w,activePayload:x,activeTooltipIndex:s}))}else o.setState(e)}),d_(o,"renderCursor",function(e){var r,n=o.state,i=n.isTooltipActive,a=n.activeCoordinate,u=n.activePayload,c=n.offset,s=n.activeTooltipIndex,f=n.tooltipAxisBandSize,p=o.getTooltipEventType(),d=null!==(r=e.props.active)&&void 0!==r?r:i,h=o.props.layout,y=e.key||"_recharts-cursor";return l().createElement(dy,{key:y,activeCoordinate:a,activePayload:u,activeTooltipIndex:s,chartName:t,element:e,isActive:d,layout:h,offset:c,tooltipAxisBandSize:f,tooltipEventType:p})}),d_(o,"renderPolarAxis",function(e,t,r){var n=A()(e,"type.axisType"),i=A()(o.state,"".concat(n,"Map")),a=e.type.defaultProps,u=void 0!==a?dT(dT({},a),e.props):e.props,l=i&&i[u["".concat(n,"Id")]];return(0,c.cloneElement)(e,dT(dT({},l),{},{className:(0,x.Z)(n,l.className),key:e.key||"".concat(t,"-").concat(r),ticks:lv(l,!0)}))}),d_(o,"renderPolarGrid",function(e){var t=e.props,r=t.radialLines,n=t.polarAngles,i=t.polarRadius,a=o.state,u=a.radiusAxisMap,l=a.angleAxisMap,s=B(u),f=B(l),p=f.cx,d=f.cy,h=f.innerRadius,y=f.outerRadius;return(0,c.cloneElement)(e,{polarAngles:Array.isArray(n)?n:lv(f,!0).map(function(e){return e.coordinate}),polarRadius:Array.isArray(i)?i:lv(s,!0).map(function(e){return e.coordinate}),cx:p,cy:d,innerRadius:h,outerRadius:y,key:e.key||"polar-grid",radialLines:r})}),d_(o,"renderLegend",function(){var e=o.state.formattedGraphicalItems,t=o.props,r=t.children,n=t.width,i=t.height,a=o.props.margin||{},u=c7({children:r,formattedGraphicalItems:e,legendWidth:n-(a.left||0)-(a.right||0),legendContent:s});if(!u)return null;var l=u.item,f=dw(u,dv);return(0,c.cloneElement)(l,dT(dT({},f),{},{chartWidth:n,chartHeight:i,margin:a,onBBoxUpdate:o.handleLegendBBoxUpdate}))}),d_(o,"renderTooltip",function(){var e,t=o.props,r=t.children,n=t.accessibilityLayer,i=ey(r,tp);if(!i)return null;var a=o.state,u=a.isTooltipActive,l=a.activeCoordinate,s=a.activePayload,f=a.activeLabel,p=a.offset,d=null!==(e=i.props.active)&&void 0!==e?e:u;return(0,c.cloneElement)(i,{viewBox:dT(dT({},p),{},{x:p.left,y:p.top}),active:d,label:f,payload:d?s:[],coordinate:l,accessibilityLayer:n})}),d_(o,"renderBrush",function(e){var t=o.props,r=t.margin,n=t.data,i=o.state,a=i.offset,u=i.dataStartIndex,l=i.dataEndIndex,s=i.updateId;return(0,c.cloneElement)(e,{key:e.key||"_recharts-brush",onChange:lg(o.handleBrushChange,e.props.onChange),data:n,x:N(e.props.x)?e.props.x:a.left,y:N(e.props.y)?e.props.y:a.top+a.height+a.brushBottom-(r.bottom||0),width:N(e.props.width)?e.props.width:a.width,startIndex:u,endIndex:l,updateId:"brush-".concat(s)})}),d_(o,"renderReferenceElement",function(e,t,r){if(!e)return null;var n=o.clipPathId,i=o.state,a=i.xAxisMap,u=i.yAxisMap,l=i.offset,s=e.type.defaultProps||{},f=e.props,p=f.xAxisId,d=void 0===p?s.xAxisId:p,h=f.yAxisId,y=void 0===h?s.yAxisId:h;return(0,c.cloneElement)(e,{key:e.key||"".concat(t,"-").concat(r),xAxis:a[d],yAxis:u[y],viewBox:{x:l.left,y:l.top,width:l.width,height:l.height},clipPathId:n})}),d_(o,"renderActivePoints",function(e){var t=e.item,r=e.activePoint,n=e.basePoint,o=e.childIndex,a=e.isRange,u=[],c=t.props.key,l=void 0!==t.item.type.defaultProps?dT(dT({},t.item.type.defaultProps),t.item.props):t.item.props,s=l.activeDot,f=dT(dT({index:o,dataKey:l.dataKey,cx:r.x,cy:r.y,r:4,fill:lu(t.item),strokeWidth:2,stroke:"#fff",payload:r.payload,value:r.value},eb(s,!1)),en(s));return u.push(i.renderActiveDot(s,f,"".concat(c,"-activePoint-").concat(o))),n?u.push(i.renderActiveDot(s,dT(dT({},f),{},{cx:n.x,cy:n.y}),"".concat(c,"-basePoint-").concat(o))):a&&u.push(null),u}),d_(o,"renderGraphicChild",function(e,t,r){var n=o.filterFormatItem(e,t,r);if(!n)return null;var i=o.getTooltipEventType(),a=o.state,u=a.isTooltipActive,l=a.tooltipAxis,s=a.activeTooltipIndex,f=a.activeLabel,p=ey(o.props.children,tp),d=n.props,h=d.points,y=d.isRange,v=d.baseLine,m=void 0!==n.item.type.defaultProps?dT(dT({},n.item.type.defaultProps),n.item.props):n.item.props,g=m.activeDot,b=m.hide,x=m.activeBar,w=m.activeShape,O={};"axis"!==i&&p&&"click"===p.props.trigger?O={onClick:lg(o.handleItemMouseEnter,e.props.onClick)}:"axis"!==i&&(O={onMouseLeave:lg(o.handleItemMouseLeave,e.props.onMouseLeave),onMouseEnter:lg(o.handleItemMouseEnter,e.props.onMouseEnter)});var j=(0,c.cloneElement)(e,dT(dT({},n.props),O));if(!b&&u&&p&&(g||x||w)){if(s>=0){if(l.dataKey&&!l.allowDuplicatedCategory){var S="function"==typeof l.dataKey?function(e){return"function"==typeof l.dataKey?l.dataKey(e.payload):null}:"payload.".concat(l.dataKey.toString());E=z(h,S,f),k=y&&v&&z(v,S,f)}else E=null==h?void 0:h[s],k=y&&v&&v[s];if(w||x){var P=void 0!==e.props.activeIndex?e.props.activeIndex:s;return[(0,c.cloneElement)(e,dT(dT(dT({},n.props),O),{},{activeIndex:P})),null,null]}if(!H()(E))return[j].concat(dE(o.renderActivePoints({item:n,activePoint:E,basePoint:k,childIndex:s,isRange:y})))}else{var E,k,A,M=(null!==(A=o.getItemByXY(o.state.activeCoordinate))&&void 0!==A?A:{graphicalItem:j}).graphicalItem,T=M.item,_=void 0===T?e:T,C=M.childIndex,N=dT(dT(dT({},n.props),O),{},{activeIndex:C});return[(0,c.cloneElement)(_,N),null,null]}}return y?[j,null,null]:[j,null]}),d_(o,"renderCustomized",function(e,t,r){return(0,c.cloneElement)(e,dT(dT({key:"recharts-customized-".concat(r)},o.props),o.state))}),d_(o,"renderMap",{CartesianGrid:{handler:dR,once:!0},ReferenceArea:{handler:o.renderReferenceElement},ReferenceLine:{handler:dR},ReferenceDot:{handler:o.renderReferenceElement},XAxis:{handler:dR},YAxis:{handler:dR},Brush:{handler:o.renderBrush,once:!0},Bar:{handler:o.renderGraphicChild},Line:{handler:o.renderGraphicChild},Area:{handler:o.renderGraphicChild},Radar:{handler:o.renderGraphicChild},RadialBar:{handler:o.renderGraphicChild},Scatter:{handler:o.renderGraphicChild},Pie:{handler:o.renderGraphicChild},Funnel:{handler:o.renderGraphicChild},Tooltip:{handler:o.renderCursor,once:!0},PolarGrid:{handler:o.renderPolarGrid,once:!0},PolarAngleAxis:{handler:o.renderPolarAxis},PolarRadiusAxis:{handler:o.renderPolarAxis},Customized:{handler:o.renderCustomized}}),o.clipPathId="".concat(null!==(r=e.id)&&void 0!==r?r:R("recharts"),"-clip"),o.throttleTriggeredAfterMouseMove=O()(o.triggeredAfterMouseMove,null!==(n=e.throttleDelay)&&void 0!==n?n:1e3/60),o.state={},o}return function(e,t){if("function"!=typeof t&&null!==t)throw TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&dP(e,t)}(i,e),r=[{key:"componentDidMount",value:function(){var e,t;this.addListener(),this.accessibilityManager.setDetails({container:this.container,offset:{left:null!==(e=this.props.margin.left)&&void 0!==e?e:0,top:null!==(t=this.props.margin.top)&&void 0!==t?t:0},coordinateList:this.state.tooltipTicks,mouseHandlerCallback:this.triggeredAfterMouseMove,layout:this.props.layout}),this.displayDefaultTooltip()}},{key:"displayDefaultTooltip",value:function(){var e=this.props,t=e.children,r=e.data,n=e.height,o=e.layout,i=ey(t,tp);if(i){var a=i.props.defaultIndex;if("number"==typeof a&&!(a<0)&&!(a>this.state.tooltipTicks.length-1)){var u=this.state.tooltipTicks[a]&&this.state.tooltipTicks[a].value,c=dU(this.state,r,a,u),l=this.state.tooltipTicks[a].coordinate,s=(this.state.offset.top+n)/2,f="horizontal"===o?{x:l,y:s}:{y:l,x:s},p=this.state.formattedGraphicalItems.find(function(e){return"Scatter"===e.item.type.name});p&&(f=dT(dT({},f),p.props.points[a].tooltipPosition),c=p.props.points[a].tooltipPayload);var d={activeTooltipIndex:a,isTooltipActive:!0,activeLabel:u,activePayload:c,activeCoordinate:f};this.setState(d),this.renderCursor(i),this.accessibilityManager.setIndex(a)}}}},{key:"getSnapshotBeforeUpdate",value:function(e,t){if(!this.props.accessibilityLayer)return null;if(this.state.tooltipTicks!==t.tooltipTicks&&this.accessibilityManager.setDetails({coordinateList:this.state.tooltipTicks}),this.props.layout!==e.layout&&this.accessibilityManager.setDetails({layout:this.props.layout}),this.props.margin!==e.margin){var r,n;this.accessibilityManager.setDetails({offset:{left:null!==(r=this.props.margin.left)&&void 0!==r?r:0,top:null!==(n=this.props.margin.top)&&void 0!==n?n:0}})}return null}},{key:"componentDidUpdate",value:function(e){ex([ey(e.children,tp)],[ey(this.props.children,tp)])||this.displayDefaultTooltip()}},{key:"componentWillUnmount",value:function(){this.removeListener(),this.throttleTriggeredAfterMouseMove.cancel()}},{key:"getTooltipEventType",value:function(){var e=ey(this.props.children,tp);if(e&&"boolean"==typeof e.props.shared){var t=e.props.shared?"axis":"item";return a.indexOf(t)>=0?t:o}return o}},{key:"getMouseInfo",value:function(e){if(!this.container)return null;var t=this.container,r=t.getBoundingClientRect(),n={top:r.top+window.scrollY-document.documentElement.clientTop,left:r.left+window.scrollX-document.documentElement.clientLeft},o={chartX:Math.round(e.pageX-n.left),chartY:Math.round(e.pageY-n.top)},i=r.width/t.offsetWidth||1,a=this.inRange(o.chartX,o.chartY,i);if(!a)return null;var u=this.state,c=u.xAxisMap,l=u.yAxisMap,s=this.getTooltipEventType(),f=dz(this.state,this.props.data,this.props.layout,a);if("axis"!==s&&c&&l){var p=B(c).scale,d=B(l).scale,h=p&&p.invert?p.invert(o.chartX):null,y=d&&d.invert?d.invert(o.chartY):null;return dT(dT({},o),{},{xValue:h,yValue:y},f)}return f?dT(dT({},o),f):null}},{key:"inRange",value:function(e,t){var r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:1,n=this.props.layout,o=e/r,i=t/r;if("horizontal"===n||"vertical"===n){var a=this.state.offset;return o>=a.left&&o<=a.left+a.width&&i>=a.top&&i<=a.top+a.height?{x:o,y:i}:null}var u=this.state,c=u.angleAxisMap,l=u.radiusAxisMap;return c&&l?st({x:o,y:i},B(c)):null}},{key:"parseEventsOfWrapper",value:function(){var e=this.props.children,t=this.getTooltipEventType(),r=ey(e,tp),n={};return r&&"axis"===t&&(n="click"===r.props.trigger?{onClick:this.handleClick}:{onMouseEnter:this.handleMouseEnter,onDoubleClick:this.handleDoubleClick,onMouseMove:this.handleMouseMove,onMouseLeave:this.handleMouseLeave,onTouchMove:this.handleTouchMove,onTouchStart:this.handleTouchStart,onTouchEnd:this.handleTouchEnd,onContextMenu:this.handleContextMenu}),dT(dT({},en(this.props,this.handleOuterEvent)),n)}},{key:"addListener",value:function(){pM.on(pT,this.handleReceiveSyncEvent)}},{key:"removeListener",value:function(){pM.removeListener(pT,this.handleReceiveSyncEvent)}},{key:"filterFormatItem",value:function(e,t,r){for(var n=this.state.formattedGraphicalItems,o=0,i=n.length;o<i;o++){var a=n[o];if(a.item===e||a.props.key===e.key||t===es(a.item.type)&&r===a.childIndex)return a}return null}},{key:"renderClipPath",value:function(){var e=this.clipPathId,t=this.state.offset,r=t.left,n=t.top,o=t.height,i=t.width;return l().createElement("defs",null,l().createElement("clipPath",{id:e},l().createElement("rect",{x:r,y:n,height:o,width:i})))}},{key:"getXScales",value:function(){var e=this.state.xAxisMap;return e?Object.entries(e).reduce(function(e,t){var r=dx(t,2),n=r[0],o=r[1];return dT(dT({},e),{},d_({},n,o.scale))},{}):null}},{key:"getYScales",value:function(){var e=this.state.yAxisMap;return e?Object.entries(e).reduce(function(e,t){var r=dx(t,2),n=r[0],o=r[1];return dT(dT({},e),{},d_({},n,o.scale))},{}):null}},{key:"getXScaleByAxisId",value:function(e){var t;return null===(t=this.state.xAxisMap)||void 0===t||null===(t=t[e])||void 0===t?void 0:t.scale}},{key:"getYScaleByAxisId",value:function(e){var t;return null===(t=this.state.yAxisMap)||void 0===t||null===(t=t[e])||void 0===t?void 0:t.scale}},{key:"getItemByXY",value:function(e){var t=this.state,r=t.formattedGraphicalItems,n=t.activeItem;if(r&&r.length)for(var o=0,i=r.length;o<i;o++){var a=r[o],u=a.props,c=a.item,l=void 0!==c.type.defaultProps?dT(dT({},c.type.defaultProps),c.props):c.props,s=es(c.type);if("Bar"===s){var f=(u.data||[]).find(function(t){return nO(e,t)});if(f)return{graphicalItem:a,payload:f}}else if("RadialBar"===s){var p=(u.data||[]).find(function(t){return st(e,t)});if(p)return{graphicalItem:a,payload:p}}else if(s4(a,n)||s6(a,n)||s7(a,n)){var d=function(e){var t,r,n,o=e.activeTooltipItem,i=e.graphicalItem,a=e.itemData,u=(s4(i,o)?t="trapezoids":s6(i,o)?t="sectors":s7(i,o)&&(t="points"),t),c=s4(i,o)?null===(r=o.tooltipPayload)||void 0===r||null===(r=r[0])||void 0===r||null===(r=r.payload)||void 0===r?void 0:r.payload:s6(i,o)?null===(n=o.tooltipPayload)||void 0===n||null===(n=n[0])||void 0===n||null===(n=n.payload)||void 0===n?void 0:n.payload:s7(i,o)?o.payload:{},l=a.filter(function(e,t){var r=u7()(c,e),n=i.props[u].filter(function(e){var t;return(s4(i,o)?t=s8:s6(i,o)?t=s9:s7(i,o)&&(t=fe),t)(e,o)}),a=i.props[u].indexOf(n[n.length-1]);return r&&t===a});return a.indexOf(l[l.length-1])}({graphicalItem:a,activeTooltipItem:n,itemData:l.data}),h=void 0===l.activeIndex?d:l.activeIndex;return{graphicalItem:dT(dT({},a),{},{childIndex:h}),payload:s7(a,n)?l.data[d]:a.props.data[d]}}}return null}},{key:"render",value:function(){var e,t,r=this;if(!ev(this))return null;var n=this.props,o=n.children,i=n.className,a=n.width,u=n.height,c=n.style,s=n.compact,f=n.title,p=n.desc,d=eb(dw(n,dm),!1);if(s)return l().createElement(f5,{state:this.state,width:this.props.width,height:this.props.height,clipPathId:this.clipPathId},l().createElement(eR,db({},d,{width:a,height:u,title:f,desc:p}),this.renderClipPath(),eO(o,this.renderMap)));this.props.accessibilityLayer&&(d.tabIndex=null!==(e=this.props.tabIndex)&&void 0!==e?e:0,d.role=null!==(t=this.props.role)&&void 0!==t?t:"application",d.onKeyDown=function(e){r.accessibilityManager.keyboardEvent(e)},d.onFocus=function(){r.accessibilityManager.focus()});var h=this.parseEventsOfWrapper();return l().createElement(f5,{state:this.state,width:this.props.width,height:this.props.height,clipPathId:this.clipPathId},l().createElement("div",db({className:(0,x.Z)("recharts-wrapper",i),style:dT({position:"relative",cursor:"default",width:a,height:u},c)},h,{ref:function(e){r.container=e}}),l().createElement(eR,db({},d,{width:a,height:u,title:f,desc:p,style:dD}),this.renderClipPath(),eO(o,this.renderMap)),this.renderLegend(),this.renderTooltip()))}}],dO(i.prototype,r),n&&dO(i,n),Object.defineProperty(i,"prototype",{writable:!1}),i}(c.Component);d_(y,"displayName",t),d_(y,"defaultProps",dT({layout:"horizontal",stackOffset:"none",barCategoryGap:"10%",barGap:4,margin:{top:5,right:5,bottom:5,left:5},reverseStackOrder:!1,syncMethod:"index"},p)),d_(y,"getDerivedStateFromProps",function(e,t){var r=e.dataKey,n=e.data,o=e.children,i=e.width,a=e.height,u=e.layout,c=e.stackOffset,l=e.margin,s=t.dataStartIndex,f=t.dataEndIndex;if(void 0===t.updateId){var p=dV(e);return dT(dT(dT({},p),{},{updateId:0},h(dT(dT({props:e},p),{},{updateId:0}),t)),{},{prevDataKey:r,prevData:n,prevWidth:i,prevHeight:a,prevLayout:u,prevStackOffset:c,prevMargin:l,prevChildren:o})}if(r!==t.prevDataKey||n!==t.prevData||i!==t.prevWidth||a!==t.prevHeight||u!==t.prevLayout||c!==t.prevStackOffset||!K(l,t.prevMargin)){var d=dV(e),y={chartX:t.chartX,chartY:t.chartY,isTooltipActive:t.isTooltipActive},v=dT(dT({},dz(t,n,u)),{},{updateId:t.updateId+1}),m=dT(dT(dT({},d),y),v);return dT(dT(dT({},m),h(dT({props:e},m),t)),{},{prevDataKey:r,prevData:n,prevWidth:i,prevHeight:a,prevLayout:u,prevStackOffset:c,prevMargin:l,prevChildren:o})}if(!ex(o,t.prevChildren)){var g,b,x,w,O=ey(o,l2),j=O&&null!==(g=null===(b=O.props)||void 0===b?void 0:b.startIndex)&&void 0!==g?g:s,S=O&&null!==(x=null===(w=O.props)||void 0===w?void 0:w.endIndex)&&void 0!==x?x:f,P=H()(n)||j!==s||S!==f?t.updateId+1:t.updateId;return dT(dT({updateId:P},h(dT(dT({props:e},t),{},{updateId:P,dataStartIndex:j,dataEndIndex:S}),t)),{},{prevChildren:o,dataStartIndex:j,dataEndIndex:S})}return null}),d_(y,"renderActiveDot",function(e,t,r){var n;return n=(0,c.isValidElement)(e)?(0,c.cloneElement)(e,t):X()(e)?e(t):l().createElement(ri,t),l().createElement(eF,{className:"recharts-active-dot",key:r},n)});var v=(0,c.forwardRef)(function(e,t){return l().createElement(y,db({},e,{ref:t}))});return v.displayName=y.displayName,v},dY=["type","layout","connectNulls","ref"],dK=["key"];function dQ(e){return(dQ="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function dJ(e,t){if(null==e)return{};var r,n,o=function(e,t){if(null==e)return{};var r={};for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){if(t.indexOf(n)>=0)continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(n=0;n<i.length;n++)r=i[n],!(t.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(e,r)&&(o[r]=e[r])}return o}function d0(){return(d0=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(this,arguments)}function d1(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function d2(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?d1(Object(r),!0).forEach(function(t){d9(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):d1(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function d5(e){return function(e){if(Array.isArray(e))return d3(e)}(e)||function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(e)||function(e,t){if(e){if("string"==typeof e)return d3(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);if("Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r)return Array.from(e);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return d3(e,t)}}(e)||function(){throw TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function d3(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=Array(t);r<t;r++)n[r]=e[r];return n}function d4(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,he(n.key),n)}}function d6(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(e){}return(d6=function(){return!!e})()}function d7(e){return(d7=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)})(e)}function d8(e,t){return(d8=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e})(e,t)}function d9(e,t,r){return(t=he(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function he(e){var t=function(e,t){if("object"!=dQ(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=dQ(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==dQ(t)?t:t+""}var ht=function(e){var t,r;function n(){!function(e,t){if(!(e instanceof t))throw TypeError("Cannot call a class as a function")}(this,n);for(var e,t,r,o=arguments.length,i=Array(o),a=0;a<o;a++)i[a]=arguments[a];return t=n,r=[].concat(i),t=d7(t),d9(e=function(e,t){if(t&&("object"===dQ(t)||"function"==typeof t))return t;if(void 0!==t)throw TypeError("Derived constructors may only return object or undefined");return function(e){if(void 0===e)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return e}(e)}(this,d6()?Reflect.construct(t,r||[],d7(this).constructor):t.apply(this,r)),"state",{isAnimationFinished:!0,totalLength:0}),d9(e,"generateSimpleStrokeDasharray",function(e,t){return"".concat(t,"px ").concat(e-t,"px")}),d9(e,"getStrokeDasharray",function(t,r,o){var i=o.reduce(function(e,t){return e+t});if(!i)return e.generateSimpleStrokeDasharray(r,t);for(var a=t%i,u=r-t,c=[],l=0,s=0;l<o.length;s+=o[l],++l)if(s+o[l]>a){c=[].concat(d5(o.slice(0,l)),[a-s]);break}var f=c.length%2==0?[0,u]:[u];return[].concat(d5(n.repeat(o,Math.floor(t/i))),d5(c),f).map(function(e){return"".concat(e,"px")}).join(", ")}),d9(e,"id",R("recharts-line-")),d9(e,"pathRef",function(t){e.mainCurve=t}),d9(e,"handleAnimationEnd",function(){e.setState({isAnimationFinished:!0}),e.props.onAnimationEnd&&e.props.onAnimationEnd()}),d9(e,"handleAnimationStart",function(){e.setState({isAnimationFinished:!1}),e.props.onAnimationStart&&e.props.onAnimationStart()}),e}return function(e,t){if("function"!=typeof t&&null!==t)throw TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&d8(e,t)}(n,e),t=[{key:"componentDidMount",value:function(){if(this.props.isAnimationActive){var e=this.getTotalLength();this.setState({totalLength:e})}}},{key:"componentDidUpdate",value:function(){if(this.props.isAnimationActive){var e=this.getTotalLength();e!==this.state.totalLength&&this.setState({totalLength:e})}}},{key:"getTotalLength",value:function(){var e=this.mainCurve;try{return e&&e.getTotalLength&&e.getTotalLength()||0}catch(e){return 0}}},{key:"renderErrorBar",value:function(e,t){if(this.props.isAnimationActive&&!this.state.isAnimationFinished)return null;var r=this.props,n=r.points,o=r.xAxis,i=r.yAxis,a=r.layout,u=eh(r.children,c5);if(!u)return null;var c=function(e,t){return{x:e.x,y:e.y,value:e.value,errorVal:lo(e.payload,t)}};return l().createElement(eF,{clipPath:e?"url(#clipPath-".concat(t,")"):null},u.map(function(e){return l().cloneElement(e,{key:"bar-".concat(e.props.dataKey),data:n,xAxis:o,yAxis:i,layout:a,dataPointFormatter:c})}))}},{key:"renderDots",value:function(e,t,r){if(this.props.isAnimationActive&&!this.state.isAnimationFinished)return null;var o=this.props,i=o.dot,a=o.points,u=o.dataKey,c=eb(this.props,!1),s=eb(i,!0),f=a.map(function(e,t){var r=d2(d2(d2({key:"dot-".concat(t),r:3},c),s),{},{index:t,cx:e.x,cy:e.y,value:e.value,dataKey:u,payload:e.payload,points:a});return n.renderDotItem(i,r)}),p={clipPath:e?"url(#clipPath-".concat(t?"":"dots-").concat(r,")"):null};return l().createElement(eF,d0({className:"recharts-line-dots",key:"dots"},p),f)}},{key:"renderCurveStatically",value:function(e,t,r,n){var o=this.props,i=o.type,a=o.layout,u=o.connectNulls,c=d2(d2(d2({},eb((o.ref,dJ(o,dY)),!0)),{},{fill:"none",className:"recharts-line-curve",clipPath:t?"url(#clipPath-".concat(r,")"):null,points:e},n),{},{type:i,layout:a,connectNulls:u});return l().createElement(di,d0({},c,{pathRef:this.pathRef}))}},{key:"renderCurveWithAnimation",value:function(e,t){var r=this,n=this.props,o=n.points,i=n.strokeDasharray,a=n.isAnimationActive,u=n.animationBegin,c=n.animationDuration,s=n.animationEasing,f=n.animationId,p=n.animateNewValues,d=n.width,h=n.height,y=this.state,v=y.prevPoints,m=y.totalLength;return l().createElement(ny,{begin:u,duration:c,isActive:a,easing:s,from:{t:0},to:{t:1},key:"line-".concat(f),onAnimationEnd:this.handleAnimationEnd,onAnimationStart:this.handleAnimationStart},function(n){var a,u=n.t;if(v){var c=v.length/o.length,l=o.map(function(e,t){var r=Math.floor(t*c);if(v[r]){var n=v[r],o=U(n.x,e.x),i=U(n.y,e.y);return d2(d2({},e),{},{x:o(u),y:i(u)})}if(p){var a=U(2*d,e.x),l=U(h/2,e.y);return d2(d2({},e),{},{x:a(u),y:l(u)})}return d2(d2({},e),{},{x:e.x,y:e.y})});return r.renderCurveStatically(l,e,t)}var s=U(0,m)(u);if(i){var f="".concat(i).split(/[,\s]+/gim).map(function(e){return parseFloat(e)});a=r.getStrokeDasharray(s,m,f)}else a=r.generateSimpleStrokeDasharray(m,s);return r.renderCurveStatically(o,e,t,{strokeDasharray:a})})}},{key:"renderCurve",value:function(e,t){var r=this.props,n=r.points,o=r.isAnimationActive,i=this.state,a=i.prevPoints,u=i.totalLength;return o&&n&&n.length&&(!a&&u>0||!u7()(a,n))?this.renderCurveWithAnimation(e,t):this.renderCurveStatically(n,e,t)}},{key:"render",value:function(){var e,t=this.props,r=t.hide,n=t.dot,o=t.points,i=t.className,a=t.xAxis,u=t.yAxis,c=t.top,s=t.left,f=t.width,p=t.height,d=t.isAnimationActive,h=t.id;if(r||!o||!o.length)return null;var y=this.state.isAnimationFinished,v=1===o.length,m=(0,x.Z)("recharts-line",i),g=a&&a.allowDataOverflow,b=u&&u.allowDataOverflow,w=g||b,O=H()(h)?this.id:h,j=null!==(e=eb(n,!1))&&void 0!==e?e:{r:3,strokeWidth:2},S=j.r,P=j.strokeWidth,E=(n&&"object"===ec(n)&&"clipDot"in n?n:{}).clipDot,k=void 0===E||E,A=2*(void 0===S?3:S)+(void 0===P?2:P);return l().createElement(eF,{className:m},g||b?l().createElement("defs",null,l().createElement("clipPath",{id:"clipPath-".concat(O)},l().createElement("rect",{x:g?s:s-f/2,y:b?c:c-p/2,width:g?f:2*f,height:b?p:2*p})),!k&&l().createElement("clipPath",{id:"clipPath-dots-".concat(O)},l().createElement("rect",{x:s-A/2,y:c-A/2,width:f+A,height:p+A}))):null,!v&&this.renderCurve(w,O),this.renderErrorBar(w,O),(v||n)&&this.renderDots(w,k,O),(!d||y)&&s_.renderCallByParent(this.props,o))}}],r=[{key:"getDerivedStateFromProps",value:function(e,t){return e.animationId!==t.prevAnimationId?{prevAnimationId:e.animationId,curPoints:e.points,prevPoints:t.curPoints}:e.points!==t.curPoints?{curPoints:e.points}:null}},{key:"repeat",value:function(e,t){for(var r=e.length%2!=0?[].concat(d5(e),[0]):e,n=[],o=0;o<t;++o)n=[].concat(d5(n),d5(r));return n}},{key:"renderDotItem",value:function(e,t){var r;if(l().isValidElement(e))r=l().cloneElement(e,t);else if(X()(e))r=e(t);else{var n=t.key,o=dJ(t,dK),i=(0,x.Z)("recharts-line-dot","boolean"!=typeof e?e.className:"");r=l().createElement(ri,d0({key:n},o,{className:i}))}return r}}],t&&d4(n.prototype,t),r&&d4(n,r),Object.defineProperty(n,"prototype",{writable:!1}),n}(c.PureComponent);function hr(e,t,r){if(t<1)return[];if(1===t&&void 0===r)return e;for(var n=[],o=0;o<e.length;o+=t){if(void 0!==r&&!0!==r(e[o]))return;n.push(e[o])}return n}function hn(e,t,r,n,o){if(e*t<e*n||e*t>e*o)return!1;var i=r();return e*(t-e*i/2-n)>=0&&e*(t+e*i/2-o)<=0}function ho(e){return(ho="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function hi(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function ha(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?hi(Object(r),!0).forEach(function(t){var n,o;n=t,o=r[t],(n=function(e){var t=function(e,t){if("object"!=ho(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=ho(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==ho(t)?t:t+""}(n))in e?Object.defineProperty(e,n,{value:o,enumerable:!0,configurable:!0,writable:!0}):e[n]=o}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):hi(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function hu(e,t,r){var n,o,i,a,u,c=e.tick,l=e.ticks,s=e.viewBox,f=e.minTickGap,p=e.orientation,d=e.interval,h=e.tickFormatter,y=e.unit,v=e.angle;if(!l||!l.length||!c)return[];if(N(d)||e8.isSsr)return hr(l,("number"==typeof d&&N(d)?d:0)+1);var m="top"===p||"bottom"===p?"width":"height",g=y&&"width"===m?nU(y,{fontSize:t,letterSpacing:r}):{width:0,height:0},b=function(e,n){var o,i=X()(h)?h(e.value,n):e.value;return"width"===m?f_({width:(o=nU(i,{fontSize:t,letterSpacing:r})).width+g.width,height:o.height+g.height},v):nU(i,{fontSize:t,letterSpacing:r})[m]},x=l.length>=2?_(l[1].coordinate-l[0].coordinate):1,w=(n="width"===m,o=s.x,i=s.y,a=s.width,u=s.height,1===x?{start:n?o:i,end:n?o+a:i+u}:{start:n?o+a:i+u,end:n?o:i});return"equidistantPreserveStart"===d?function(e,t,r,n,o){for(var i,a=(n||[]).slice(),u=t.start,c=t.end,l=0,s=1,f=u;s<=a.length;)if(i=function(){var t,i=null==n?void 0:n[l];if(void 0===i)return{v:hr(n,s)};var a=l,p=function(){return void 0===t&&(t=r(i,a)),t},d=i.coordinate,h=0===l||hn(e,d,p,f,c);h||(l=0,f=u,s+=1),h&&(f=d+e*(p()/2+o),l+=s)}())return i.v;return[]}(x,w,b,l,f):("preserveStart"===d||"preserveStartEnd"===d?function(e,t,r,n,o,i){var a=(n||[]).slice(),u=a.length,c=t.start,l=t.end;if(i){var s=n[u-1],f=r(s,u-1),p=e*(s.coordinate+e*f/2-l);a[u-1]=s=ha(ha({},s),{},{tickCoord:p>0?s.coordinate-p*e:s.coordinate}),hn(e,s.tickCoord,function(){return f},c,l)&&(l=s.tickCoord-e*(f/2+o),a[u-1]=ha(ha({},s),{},{isShow:!0}))}for(var d=i?u-1:u,h=function(t){var n,i=a[t],u=function(){return void 0===n&&(n=r(i,t)),n};if(0===t){var s=e*(i.coordinate-e*u()/2-c);a[t]=i=ha(ha({},i),{},{tickCoord:s<0?i.coordinate-s*e:i.coordinate})}else a[t]=i=ha(ha({},i),{},{tickCoord:i.coordinate});hn(e,i.tickCoord,u,c,l)&&(c=i.tickCoord+e*(u()/2+o),a[t]=ha(ha({},i),{},{isShow:!0}))},y=0;y<d;y++)h(y);return a}(x,w,b,l,f,"preserveStartEnd"===d):function(e,t,r,n,o){for(var i=(n||[]).slice(),a=i.length,u=t.start,c=t.end,l=function(t){var n,l=i[t],s=function(){return void 0===n&&(n=r(l,t)),n};if(t===a-1){var f=e*(l.coordinate+e*s()/2-c);i[t]=l=ha(ha({},l),{},{tickCoord:f>0?l.coordinate-f*e:l.coordinate})}else i[t]=l=ha(ha({},l),{},{tickCoord:l.coordinate});hn(e,l.tickCoord,s,u,c)&&(c=l.tickCoord-e*(s()/2+o),i[t]=ha(ha({},l),{},{isShow:!0}))},s=a-1;s>=0;s--)l(s);return i}(x,w,b,l,f)).filter(function(e){return e.isShow})}d9(ht,"displayName","Line"),d9(ht,"defaultProps",{xAxisId:0,yAxisId:0,connectNulls:!1,activeDot:!0,dot:!0,legendType:"line",stroke:"#3182bd",strokeWidth:1,fill:"#fff",points:[],isAnimationActive:!e8.isSsr,animateNewValues:!0,animationBegin:0,animationDuration:1500,animationEasing:"ease",hide:!1,label:!1}),d9(ht,"getComposedData",function(e){var t=e.props,r=e.xAxis,n=e.yAxis,o=e.xAxisTicks,i=e.yAxisTicks,a=e.dataKey,u=e.bandSize,c=e.displayedData,l=e.offset,s=t.layout;return d2({points:c.map(function(e,t){var c=lo(e,a);return"horizontal"===s?{x:lk({axis:r,ticks:o,bandSize:u,entry:e,index:t}),y:H()(c)?null:n.scale(c),value:c,payload:e}:{x:H()(c)?null:r.scale(c),y:lk({axis:n,ticks:i,bandSize:u,entry:e,index:t}),value:c,payload:e}}),layout:s},l)});var hc=["viewBox"],hl=["viewBox"],hs=["ticks"];function hf(e){return(hf="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function hp(){return(hp=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(this,arguments)}function hd(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function hh(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?hd(Object(r),!0).forEach(function(t){hx(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):hd(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function hy(e,t){if(null==e)return{};var r,n,o=function(e,t){if(null==e)return{};var r={};for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){if(t.indexOf(n)>=0)continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(n=0;n<i.length;n++)r=i[n],!(t.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(e,r)&&(o[r]=e[r])}return o}function hv(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,hw(n.key),n)}}function hm(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(e){}return(hm=function(){return!!e})()}function hg(e){return(hg=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)})(e)}function hb(e,t){return(hb=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e})(e,t)}function hx(e,t,r){return(t=hw(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function hw(e){var t=function(e,t){if("object"!=hf(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=hf(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==hf(t)?t:t+""}var hO=function(e){var t,r;function n(e){var t,r,o;return function(e,t){if(!(e instanceof t))throw TypeError("Cannot call a class as a function")}(this,n),r=n,o=[e],r=hg(r),(t=function(e,t){if(t&&("object"===hf(t)||"function"==typeof t))return t;if(void 0!==t)throw TypeError("Derived constructors may only return object or undefined");return function(e){if(void 0===e)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return e}(e)}(this,hm()?Reflect.construct(r,o||[],hg(this).constructor):r.apply(this,o))).state={fontSize:"",letterSpacing:""},t}return function(e,t){if("function"!=typeof t&&null!==t)throw TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&hb(e,t)}(n,e),t=[{key:"shouldComponentUpdate",value:function(e,t){var r=e.viewBox,n=hy(e,hc),o=this.props,i=o.viewBox,a=hy(o,hl);return!K(r,i)||!K(n,a)||!K(t,this.state)}},{key:"componentDidMount",value:function(){var e=this.layerReference;if(e){var t=e.getElementsByClassName("recharts-cartesian-axis-tick-value")[0];t&&this.setState({fontSize:window.getComputedStyle(t).fontSize,letterSpacing:window.getComputedStyle(t).letterSpacing})}}},{key:"getTickLineCoord",value:function(e){var t,r,n,o,i,a,u=this.props,c=u.x,l=u.y,s=u.width,f=u.height,p=u.orientation,d=u.tickSize,h=u.mirror,y=u.tickMargin,v=h?-1:1,m=e.tickSize||d,g=N(e.tickCoord)?e.tickCoord:e.coordinate;switch(p){case"top":t=r=e.coordinate,a=(n=(o=l+ +!h*f)-v*m)-v*y,i=g;break;case"left":n=o=e.coordinate,i=(t=(r=c+ +!h*s)-v*m)-v*y,a=g;break;case"right":n=o=e.coordinate,i=(t=(r=c+ +h*s)+v*m)+v*y,a=g;break;default:t=r=e.coordinate,a=(n=(o=l+ +h*f)+v*m)+v*y,i=g}return{line:{x1:t,y1:n,x2:r,y2:o},tick:{x:i,y:a}}}},{key:"getTickTextAnchor",value:function(){var e,t=this.props,r=t.orientation,n=t.mirror;switch(r){case"left":e=n?"start":"end";break;case"right":e=n?"end":"start";break;default:e="middle"}return e}},{key:"getTickVerticalAnchor",value:function(){var e=this.props,t=e.orientation,r=e.mirror,n="end";switch(t){case"left":case"right":n="middle";break;case"top":n=r?"start":"end";break;default:n=r?"end":"start"}return n}},{key:"renderAxisLine",value:function(){var e=this.props,t=e.x,r=e.y,n=e.width,o=e.height,i=e.orientation,a=e.mirror,u=e.axisLine,c=hh(hh(hh({},eb(this.props,!1)),eb(u,!1)),{},{fill:"none"});if("top"===i||"bottom"===i){var s=+("top"===i&&!a||"bottom"===i&&a);c=hh(hh({},c),{},{x1:t,y1:r+s*o,x2:t+n,y2:r+s*o})}else{var f=+("left"===i&&!a||"right"===i&&a);c=hh(hh({},c),{},{x1:t+f*n,y1:r,x2:t+f*n,y2:r+o})}return l().createElement("line",hp({},c,{className:(0,x.Z)("recharts-cartesian-axis-line",A()(u,"className"))}))}},{key:"renderTicks",value:function(e,t,r){var o=this,i=this.props,a=i.tickLine,u=i.stroke,c=i.tick,s=i.tickFormatter,f=i.unit,p=hu(hh(hh({},this.props),{},{ticks:e}),t,r),d=this.getTickTextAnchor(),h=this.getTickVerticalAnchor(),y=eb(this.props,!1),v=eb(c,!1),m=hh(hh({},y),{},{fill:"none"},eb(a,!1)),g=p.map(function(e,t){var r=o.getTickLineCoord(e),i=r.line,g=r.tick,b=hh(hh(hh(hh({textAnchor:d,verticalAnchor:h},y),{},{stroke:"none",fill:u},v),g),{},{index:t,payload:e,visibleTicksCount:p.length,tickFormatter:s});return l().createElement(eF,hp({className:"recharts-cartesian-axis-tick",key:"tick-".concat(e.value,"-").concat(e.coordinate,"-").concat(e.tickCoord)},eo(o.props,e,t)),a&&l().createElement("line",hp({},m,i,{className:(0,x.Z)("recharts-cartesian-axis-tick-line",A()(a,"className"))})),c&&n.renderTickItem(c,b,"".concat(X()(s)?s(e.value,t):e.value).concat(f||"")))});return l().createElement("g",{className:"recharts-cartesian-axis-ticks"},g)}},{key:"render",value:function(){var e=this,t=this.props,r=t.axisLine,n=t.width,o=t.height,i=t.ticksGenerator,a=t.className;if(t.hide)return null;var u=this.props,c=u.ticks,s=hy(u,hs),f=c;return(X()(i)&&(f=i(c&&c.length>0?this.props:s)),n<=0||o<=0||!f||!f.length)?null:l().createElement(eF,{className:(0,x.Z)("recharts-cartesian-axis",a),ref:function(t){e.layerReference=t}},r&&this.renderAxisLine(),this.renderTicks(f,this.state.fontSize,this.state.letterSpacing),sp.renderCallByParent(this.props))}}],r=[{key:"renderTickItem",value:function(e,t,r){return l().isValidElement(e)?l().cloneElement(e,t):X()(e)?e(t):l().createElement(Text,hp({},t,{className:"recharts-cartesian-axis-tick-value"}),r)}}],t&&hv(n.prototype,t),r&&hv(n,r),Object.defineProperty(n,"prototype",{writable:!1}),n}(c.Component);function hj(e){return(hj="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function hS(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,hM(n.key),n)}}function hP(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(e){}return(hP=function(){return!!e})()}function hE(e){return(hE=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)})(e)}function hk(e,t){return(hk=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e})(e,t)}function hA(e,t,r){return(t=hM(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function hM(e){var t=function(e,t){if("object"!=hj(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=hj(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==hj(t)?t:t+""}function hT(){return(hT=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(this,arguments)}function h_(e){var t=e.xAxisId,r=f7(),n=f8(),o=f3(t);return null==o?null:l().createElement(hO,hT({},o,{className:(0,x.Z)("recharts-".concat(o.axisType," ").concat(o.axisType),o.className),viewBox:{x:0,y:0,width:r,height:n},ticksGenerator:function(e){return lv(e,!0)}}))}hx(hO,"displayName","CartesianAxis"),hx(hO,"defaultProps",{x:0,y:0,width:0,height:0,viewBox:{x:0,y:0,width:0,height:0},orientation:"bottom",ticks:[],stroke:"#666",tickLine:!0,axisLine:!0,tick:!0,mirror:!1,minTickGap:5,tickSize:6,tickMargin:2,interval:"preserveEnd"});var hC=function(e){var t,r;function n(){var e,t;return function(e,t){if(!(e instanceof t))throw TypeError("Cannot call a class as a function")}(this,n),e=n,t=arguments,e=hE(e),function(e,t){if(t&&("object"===hj(t)||"function"==typeof t))return t;if(void 0!==t)throw TypeError("Derived constructors may only return object or undefined");return function(e){if(void 0===e)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return e}(e)}(this,hP()?Reflect.construct(e,t||[],hE(this).constructor):e.apply(this,t))}return function(e,t){if("function"!=typeof t&&null!==t)throw TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&hk(e,t)}(n,e),t=[{key:"render",value:function(){return l().createElement(h_,this.props)}}],hS(n.prototype,t),r&&hS(n,r),Object.defineProperty(n,"prototype",{writable:!1}),n}(l().Component);function hN(e){return(hN="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function hD(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,hF(n.key),n)}}function hI(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(e){}return(hI=function(){return!!e})()}function hR(e){return(hR=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)})(e)}function hL(e,t){return(hL=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e})(e,t)}function hB(e,t,r){return(t=hF(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function hF(e){var t=function(e,t){if("object"!=hN(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=hN(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==hN(t)?t:t+""}function hU(){return(hU=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(this,arguments)}hA(hC,"displayName","XAxis"),hA(hC,"defaultProps",{allowDecimals:!0,hide:!1,orientation:"bottom",width:0,height:30,mirror:!1,xAxisId:0,tickCount:5,type:"category",padding:{left:0,right:0},allowDataOverflow:!1,scale:"auto",reversed:!1,allowDuplicatedCategory:!0});var hz=function(e){var t=e.yAxisId,r=f7(),n=f8(),o=f6(t);return null==o?null:l().createElement(hO,hU({},o,{className:(0,x.Z)("recharts-".concat(o.axisType," ").concat(o.axisType),o.className),viewBox:{x:0,y:0,width:r,height:n},ticksGenerator:function(e){return lv(e,!0)}}))},h$=function(e){var t,r;function n(){var e,t;return function(e,t){if(!(e instanceof t))throw TypeError("Cannot call a class as a function")}(this,n),e=n,t=arguments,e=hR(e),function(e,t){if(t&&("object"===hN(t)||"function"==typeof t))return t;if(void 0!==t)throw TypeError("Derived constructors may only return object or undefined");return function(e){if(void 0===e)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return e}(e)}(this,hI()?Reflect.construct(e,t||[],hR(this).constructor):e.apply(this,t))}return function(e,t){if("function"!=typeof t&&null!==t)throw TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&hL(e,t)}(n,e),t=[{key:"render",value:function(){return l().createElement(hz,this.props)}}],hD(n.prototype,t),r&&hD(n,r),Object.defineProperty(n,"prototype",{writable:!1}),n}(l().Component);hB(h$,"displayName","YAxis"),hB(h$,"defaultProps",{allowDuplicatedCategory:!0,allowDecimals:!0,hide:!1,orientation:"left",width:60,height:0,mirror:!1,yAxisId:0,tickCount:5,type:"number",padding:{top:0,bottom:0},allowDataOverflow:!1,scale:"auto",reversed:!1});var hW=dG({chartName:"LineChart",GraphicalChild:ht,axisComponents:[{axisType:"xAxis",AxisComp:hC},{axisType:"yAxis",AxisComp:h$}],formatAxisMap:fk}),hq=["x1","y1","x2","y2","key"],hH=["offset"];function hV(e){return(hV="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function hX(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function hZ(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?hX(Object(r),!0).forEach(function(t){var n,o;n=t,o=r[t],(n=function(e){var t=function(e,t){if("object"!=hV(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=hV(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==hV(t)?t:t+""}(n))in e?Object.defineProperty(e,n,{value:o,enumerable:!0,configurable:!0,writable:!0}):e[n]=o}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):hX(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function hG(){return(hG=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(this,arguments)}function hY(e,t){if(null==e)return{};var r,n,o=function(e,t){if(null==e)return{};var r={};for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){if(t.indexOf(n)>=0)continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(n=0;n<i.length;n++)r=i[n],!(t.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(e,r)&&(o[r]=e[r])}return o}var hK=function(e){var t=e.fill;if(!t||"none"===t)return null;var r=e.fillOpacity,n=e.x,o=e.y,i=e.width,a=e.height,u=e.ry;return l().createElement("rect",{x:n,y:o,ry:u,width:i,height:a,stroke:"none",fill:t,fillOpacity:r,className:"recharts-cartesian-grid-bg"})};function hQ(e,t){var r;if(l().isValidElement(e))r=l().cloneElement(e,t);else if(X()(e))r=e(t);else{var n=t.x1,o=t.y1,i=t.x2,a=t.y2,u=t.key,c=eb(hY(t,hq),!1),s=(c.offset,hY(c,hH));r=l().createElement("line",hG({},s,{x1:n,y1:o,x2:i,y2:a,fill:"none",key:u}))}return r}function hJ(e){var t=e.x,r=e.width,n=e.horizontal,o=void 0===n||n,i=e.horizontalPoints;if(!o||!i||!i.length)return null;var a=i.map(function(n,i){return hQ(o,hZ(hZ({},e),{},{x1:t,y1:n,x2:t+r,y2:n,key:"line-".concat(i),index:i}))});return l().createElement("g",{className:"recharts-cartesian-grid-horizontal"},a)}function h0(e){var t=e.y,r=e.height,n=e.vertical,o=void 0===n||n,i=e.verticalPoints;if(!o||!i||!i.length)return null;var a=i.map(function(n,i){return hQ(o,hZ(hZ({},e),{},{x1:n,y1:t,x2:n,y2:t+r,key:"line-".concat(i),index:i}))});return l().createElement("g",{className:"recharts-cartesian-grid-vertical"},a)}function h1(e){var t=e.horizontalFill,r=e.fillOpacity,n=e.x,o=e.y,i=e.width,a=e.height,u=e.horizontalPoints,c=e.horizontal;if(!(void 0===c||c)||!t||!t.length)return null;var s=u.map(function(e){return Math.round(e+o-o)}).sort(function(e,t){return e-t});o!==s[0]&&s.unshift(0);var f=s.map(function(e,u){var c=s[u+1]?s[u+1]-e:o+a-e;if(c<=0)return null;var f=u%t.length;return l().createElement("rect",{key:"react-".concat(u),y:e,x:n,height:c,width:i,stroke:"none",fill:t[f],fillOpacity:r,className:"recharts-cartesian-grid-bg"})});return l().createElement("g",{className:"recharts-cartesian-gridstripes-horizontal"},f)}function h2(e){var t=e.vertical,r=e.verticalFill,n=e.fillOpacity,o=e.x,i=e.y,a=e.width,u=e.height,c=e.verticalPoints;if(!(void 0===t||t)||!r||!r.length)return null;var s=c.map(function(e){return Math.round(e+o-o)}).sort(function(e,t){return e-t});o!==s[0]&&s.unshift(0);var f=s.map(function(e,t){var c=s[t+1]?s[t+1]-e:o+a-e;if(c<=0)return null;var f=t%r.length;return l().createElement("rect",{key:"react-".concat(t),x:e,y:i,width:c,height:u,stroke:"none",fill:r[f],fillOpacity:n,className:"recharts-cartesian-grid-bg"})});return l().createElement("g",{className:"recharts-cartesian-gridstripes-vertical"},f)}var h5=function(e,t){var r=e.xAxis,n=e.width,o=e.height,i=e.offset;return ly(hu(hZ(hZ(hZ({},hO.defaultProps),r),{},{ticks:lv(r,!0),viewBox:{x:0,y:0,width:n,height:o}})),i.left,i.left+i.width,t)},h3=function(e,t){var r=e.yAxis,n=e.width,o=e.height,i=e.offset;return ly(hu(hZ(hZ(hZ({},hO.defaultProps),r),{},{ticks:lv(r,!0),viewBox:{x:0,y:0,width:n,height:o}})),i.top,i.top+i.height,t)},h4={horizontal:!0,vertical:!0,stroke:"#ccc",fill:"none",verticalFill:[],horizontalFill:[]};function h6(e){var t,r,n,o,i,a,u=f7(),s=f8(),f=(0,c.useContext)(fJ),p=hZ(hZ({},e),{},{stroke:null!==(t=e.stroke)&&void 0!==t?t:h4.stroke,fill:null!==(r=e.fill)&&void 0!==r?r:h4.fill,horizontal:null!==(n=e.horizontal)&&void 0!==n?n:h4.horizontal,horizontalFill:null!==(o=e.horizontalFill)&&void 0!==o?o:h4.horizontalFill,vertical:null!==(i=e.vertical)&&void 0!==i?i:h4.vertical,verticalFill:null!==(a=e.verticalFill)&&void 0!==a?a:h4.verticalFill,x:N(e.x)?e.x:f.left,y:N(e.y)?e.y:f.top,width:N(e.width)?e.width:f.width,height:N(e.height)?e.height:f.height}),d=p.x,h=p.y,y=p.width,v=p.height,m=p.syncWithTicks,g=p.horizontalValues,b=p.verticalValues,x=B((0,c.useContext)(fY)),w=f4();if(!N(y)||y<=0||!N(v)||v<=0||!N(d)||d!==+d||!N(h)||h!==+h)return null;var O=p.verticalCoordinatesGenerator||h5,j=p.horizontalCoordinatesGenerator||h3,S=p.horizontalPoints,P=p.verticalPoints;if((!S||!S.length)&&X()(j)){var E=g&&g.length,k=j({yAxis:w?hZ(hZ({},w),{},{ticks:E?g:w.ticks}):void 0,width:u,height:s,offset:f},!!E||m);W(Array.isArray(k),"horizontalCoordinatesGenerator should return Array but instead it returned [".concat(hV(k),"]")),Array.isArray(k)&&(S=k)}if((!P||!P.length)&&X()(O)){var A=b&&b.length,M=O({xAxis:x?hZ(hZ({},x),{},{ticks:A?b:x.ticks}):void 0,width:u,height:s,offset:f},!!A||m);W(Array.isArray(M),"verticalCoordinatesGenerator should return Array but instead it returned [".concat(hV(M),"]")),Array.isArray(M)&&(P=M)}return l().createElement("g",{className:"recharts-cartesian-grid"},l().createElement(hK,{fill:p.fill,fillOpacity:p.fillOpacity,x:p.x,y:p.y,width:p.width,height:p.height,ry:p.ry}),l().createElement(hJ,hG({},p,{offset:f,horizontalPoints:S,xAxis:x,yAxis:w})),l().createElement(h0,hG({},p,{offset:f,verticalPoints:P,xAxis:x,yAxis:w})),l().createElement(h1,hG({},p,{horizontalPoints:S})),l().createElement(h2,hG({},p,{verticalPoints:P})))}h6.displayName="CartesianGrid";let h7=[{month:"يناير",users:400,experts:240},{month:"فبراير",users:300,experts:139},{month:"مارس",users:200,experts:980},{month:"أبريل",users:278,experts:390},{month:"مايو",users:189,experts:480},{month:"يونيو",users:239,experts:380},{month:"يوليو",users:349,experts:430}];function h8(){return(0,o.jsxs)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow p-6",children:[(0,o.jsxs)("div",{className:"mb-4",children:[o.jsx("h3",{className:"text-lg font-medium text-gray-900 dark:text-white",children:"نمو المستخدمين والخبراء"}),o.jsx("p",{className:"text-sm text-gray-500 dark:text-gray-400",children:"إحصائيات النمو الشهري للمنصة"})]}),o.jsx("div",{className:"h-80",children:o.jsx(eA,{width:"100%",height:"100%",children:(0,o.jsxs)(hW,{data:h7,children:[o.jsx(h6,{strokeDasharray:"3 3",className:"stroke-gray-200 dark:stroke-gray-700"}),o.jsx(hC,{dataKey:"month",className:"text-gray-600 dark:text-gray-400",tick:{fontSize:12}}),o.jsx(h$,{className:"text-gray-600 dark:text-gray-400",tick:{fontSize:12}}),o.jsx(tp,{contentStyle:{backgroundColor:"rgb(31 41 55)",border:"1px solid rgb(75 85 99)",borderRadius:"0.5rem",color:"rgb(***********)"}}),o.jsx(ht,{type:"monotone",dataKey:"users",stroke:"#3b82f6",strokeWidth:2,name:"المستخدمين"}),o.jsx(ht,{type:"monotone",dataKey:"experts",stroke:"#10b981",strokeWidth:2,name:"الخبراء"})]})})})]})}var h9=dG({chartName:"BarChart",GraphicalChild:fx,defaultTooltipEventType:"axis",validateTooltipEventTypes:["axis","item"],axisComponents:[{axisType:"xAxis",AxisComp:hC},{axisType:"yAxis",AxisComp:h$}],formatAxisMap:fk});let ye=[{month:"يناير",revenue:4e3,commissions:400},{month:"فبراير",revenue:3e3,commissions:300},{month:"مارس",revenue:2e3,commissions:200},{month:"أبريل",revenue:2780,commissions:278},{month:"مايو",revenue:1890,commissions:189},{month:"يونيو",revenue:2390,commissions:239},{month:"يوليو",revenue:3490,commissions:349}];function yt(){return(0,o.jsxs)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow p-6",children:[(0,o.jsxs)("div",{className:"mb-4",children:[o.jsx("h3",{className:"text-lg font-medium text-gray-900 dark:text-white",children:"الإيرادات والعمولات"}),o.jsx("p",{className:"text-sm text-gray-500 dark:text-gray-400",children:"إحصائيات الإيرادات الشهرية بالدولار الأمريكي"})]}),o.jsx("div",{className:"h-80",children:o.jsx(eA,{width:"100%",height:"100%",children:(0,o.jsxs)(h9,{data:ye,children:[o.jsx(h6,{strokeDasharray:"3 3",className:"stroke-gray-200 dark:stroke-gray-700"}),o.jsx(hC,{dataKey:"month",className:"text-gray-600 dark:text-gray-400",tick:{fontSize:12}}),o.jsx(h$,{className:"text-gray-600 dark:text-gray-400",tick:{fontSize:12}}),o.jsx(tp,{contentStyle:{backgroundColor:"rgb(31 41 55)",border:"1px solid rgb(75 85 99)",borderRadius:"0.5rem",color:"rgb(***********)"},formatter:e=>[`$${e}`,""]}),o.jsx(fx,{dataKey:"revenue",fill:"#3b82f6",name:"إجمالي الإيرادات",radius:[4,4,0,0]}),o.jsx(fx,{dataKey:"commissions",fill:"#10b981",name:"عمولات المنصة",radius:[4,4,0,0]})]})})})]})}function yr(){return(0,o.jsxs)("div",{className:"space-y-6",children:[(0,o.jsxs)("div",{children:[o.jsx("h1",{className:"text-2xl font-bold text-gray-900 dark:text-white",children:"لوحة التحكم الرئيسية"}),o.jsx("p",{className:"mt-1 text-sm text-gray-500 dark:text-gray-400",children:"نظرة عامة على منصة فريلا سوريا"})]}),o.jsx(p,{}),(0,o.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[o.jsx(h8,{}),o.jsx(yt,{})]}),o.jsx(b,{})]})}},5068:(e,t,r)=>{"use strict";r.r(t),r.d(t,{Providers:()=>l});var n=r(3854),o=r(5195),i=r(5045),a=r(253),u=r(1352),c=r(4218);function l({children:e}){let[t]=(0,c.useState)(()=>new o.S({defaultOptions:{queries:{staleTime:6e4,retry:1}}}));return n.jsx(i.aH,{client:t,children:(0,n.jsxs)(a.f,{attribute:"class",defaultTheme:"dark",enableSystem:!1,disableTransitionOnChange:!0,children:[e,n.jsx(u.x7,{position:"top-center",toastOptions:{duration:4e3,style:{background:"#1f2937",color:"#f9fafb",border:"1px solid #374151"},success:{iconTheme:{primary:"#10b981",secondary:"#f9fafb"}},error:{iconTheme:{primary:"#ef4444",secondary:"#f9fafb"}}}})]})})}},3935:e=>{"use strict";var t=Object.prototype.hasOwnProperty,r="~";function n(){}function o(e,t,r){this.fn=e,this.context=t,this.once=r||!1}function i(e,t,n,i,a){if("function"!=typeof n)throw TypeError("The listener must be a function");var u=new o(n,i||e,a),c=r?r+t:t;return e._events[c]?e._events[c].fn?e._events[c]=[e._events[c],u]:e._events[c].push(u):(e._events[c]=u,e._eventsCount++),e}function a(e,t){0==--e._eventsCount?e._events=new n:delete e._events[t]}function u(){this._events=new n,this._eventsCount=0}Object.create&&(n.prototype=Object.create(null),new n().__proto__||(r=!1)),u.prototype.eventNames=function(){var e,n,o=[];if(0===this._eventsCount)return o;for(n in e=this._events)t.call(e,n)&&o.push(r?n.slice(1):n);return Object.getOwnPropertySymbols?o.concat(Object.getOwnPropertySymbols(e)):o},u.prototype.listeners=function(e){var t=r?r+e:e,n=this._events[t];if(!n)return[];if(n.fn)return[n.fn];for(var o=0,i=n.length,a=Array(i);o<i;o++)a[o]=n[o].fn;return a},u.prototype.listenerCount=function(e){var t=r?r+e:e,n=this._events[t];return n?n.fn?1:n.length:0},u.prototype.emit=function(e,t,n,o,i,a){var u=r?r+e:e;if(!this._events[u])return!1;var c,l,s=this._events[u],f=arguments.length;if(s.fn){switch(s.once&&this.removeListener(e,s.fn,void 0,!0),f){case 1:return s.fn.call(s.context),!0;case 2:return s.fn.call(s.context,t),!0;case 3:return s.fn.call(s.context,t,n),!0;case 4:return s.fn.call(s.context,t,n,o),!0;case 5:return s.fn.call(s.context,t,n,o,i),!0;case 6:return s.fn.call(s.context,t,n,o,i,a),!0}for(l=1,c=Array(f-1);l<f;l++)c[l-1]=arguments[l];s.fn.apply(s.context,c)}else{var p,d=s.length;for(l=0;l<d;l++)switch(s[l].once&&this.removeListener(e,s[l].fn,void 0,!0),f){case 1:s[l].fn.call(s[l].context);break;case 2:s[l].fn.call(s[l].context,t);break;case 3:s[l].fn.call(s[l].context,t,n);break;case 4:s[l].fn.call(s[l].context,t,n,o);break;default:if(!c)for(p=1,c=Array(f-1);p<f;p++)c[p-1]=arguments[p];s[l].fn.apply(s[l].context,c)}}return!0},u.prototype.on=function(e,t,r){return i(this,e,t,r,!1)},u.prototype.once=function(e,t,r){return i(this,e,t,r,!0)},u.prototype.removeListener=function(e,t,n,o){var i=r?r+e:e;if(!this._events[i])return this;if(!t)return a(this,i),this;var u=this._events[i];if(u.fn)u.fn!==t||o&&!u.once||n&&u.context!==n||a(this,i);else{for(var c=0,l=[],s=u.length;c<s;c++)(u[c].fn!==t||o&&!u[c].once||n&&u[c].context!==n)&&l.push(u[c]);l.length?this._events[i]=1===l.length?l[0]:l:a(this,i)}return this},u.prototype.removeAllListeners=function(e){var t;return e?(t=r?r+e:e,this._events[t]&&a(this,t)):(this._events=new n,this._eventsCount=0),this},u.prototype.off=u.prototype.removeListener,u.prototype.addListener=u.prototype.on,u.prefixed=r,u.EventEmitter=u,e.exports=u},7018:(e,t,r)=>{"use strict";var DataView=r(3906)(r(8223),"DataView");e.exports=DataView},2265:(e,t,r)=>{"use strict";var n=r(3609),o=r(2412),i=r(5506),a=r(5287),u=r(2463);function c(e){var t=-1,r=null==e?0:e.length;for(this.clear();++t<r;){var n=e[t];this.set(n[0],n[1])}}c.prototype.clear=n,c.prototype.delete=o,c.prototype.get=i,c.prototype.has=a,c.prototype.set=u,e.exports=c},3324:(e,t,r)=>{"use strict";var n=r(7979),o=r(5869),i=r(5361),a=r(1100),u=r(5569);function c(e){var t=-1,r=null==e?0:e.length;for(this.clear();++t<r;){var n=e[t];this.set(n[0],n[1])}}c.prototype.clear=n,c.prototype.delete=o,c.prototype.get=i,c.prototype.has=a,c.prototype.set=u,e.exports=c},5608:(e,t,r)=>{"use strict";var Map=r(3906)(r(8223),"Map");e.exports=Map},3883:(e,t,r)=>{"use strict";var n=r(8844),o=r(1856),i=r(5353),a=r(7775),u=r(8399);function c(e){var t=-1,r=null==e?0:e.length;for(this.clear();++t<r;){var n=e[t];this.set(n[0],n[1])}}c.prototype.clear=n,c.prototype.delete=o,c.prototype.get=i,c.prototype.has=a,c.prototype.set=u,e.exports=c},1904:(e,t,r)=>{"use strict";var Promise=r(3906)(r(8223),"Promise");e.exports=Promise},193:(e,t,r)=>{"use strict";var Set=r(3906)(r(8223),"Set");e.exports=Set},7226:(e,t,r)=>{"use strict";var n=r(3883),o=r(7051),i=r(6816);function a(e){var t=-1,r=null==e?0:e.length;for(this.__data__=new n;++t<r;)this.add(e[t])}a.prototype.add=a.prototype.push=o,a.prototype.has=i,e.exports=a},1401:(e,t,r)=>{"use strict";var n=r(3324),o=r(9394),i=r(7263),a=r(4246),u=r(9915),c=r(6089);function l(e){var t=this.__data__=new n(e);this.size=t.size}l.prototype.clear=o,l.prototype.delete=i,l.prototype.get=a,l.prototype.has=u,l.prototype.set=c,e.exports=l},8586:(e,t,r)=>{"use strict";var Symbol=r(8223).Symbol;e.exports=Symbol},1055:(e,t,r)=>{"use strict";var Uint8Array=r(8223).Uint8Array;e.exports=Uint8Array},16:(e,t,r)=>{"use strict";var WeakMap=r(3906)(r(8223),"WeakMap");e.exports=WeakMap},9493:e=>{"use strict";e.exports=function(e,t,r){switch(r.length){case 0:return e.call(t);case 1:return e.call(t,r[0]);case 2:return e.call(t,r[0],r[1]);case 3:return e.call(t,r[0],r[1],r[2])}return e.apply(t,r)}},5395:e=>{"use strict";e.exports=function(e,t){for(var r=-1,n=null==e?0:e.length;++r<n;)if(!t(e[r],r,e))return!1;return!0}},5448:e=>{"use strict";e.exports=function(e,t){for(var r=-1,n=null==e?0:e.length,o=0,i=[];++r<n;){var a=e[r];t(a,r,e)&&(i[o++]=a)}return i}},6968:(e,t,r)=>{"use strict";var n=r(134);e.exports=function(e,t){return!!(null==e?0:e.length)&&n(e,t,0)>-1}},9774:e=>{"use strict";e.exports=function(e,t,r){for(var n=-1,o=null==e?0:e.length;++n<o;)if(r(t,e[n]))return!0;return!1}},4004:(e,t,r)=>{"use strict";var n=r(810),o=r(4451),i=r(6887),a=r(2014),u=r(9128),c=r(1209),l=Object.prototype.hasOwnProperty;e.exports=function(e,t){var r=i(e),s=!r&&o(e),f=!r&&!s&&a(e),p=!r&&!s&&!f&&c(e),d=r||s||f||p,h=d?n(e.length,String):[],y=h.length;for(var v in e)(t||l.call(e,v))&&!(d&&("length"==v||f&&("offset"==v||"parent"==v)||p&&("buffer"==v||"byteLength"==v||"byteOffset"==v)||u(v,y)))&&h.push(v);return h}},7174:e=>{"use strict";e.exports=function(e,t){for(var r=-1,n=null==e?0:e.length,o=Array(n);++r<n;)o[r]=t(e[r],r,e);return o}},86:e=>{"use strict";e.exports=function(e,t){for(var r=-1,n=t.length,o=e.length;++r<n;)e[o+r]=t[r];return e}},7951:e=>{"use strict";e.exports=function(e,t){for(var r=-1,n=null==e?0:e.length;++r<n;)if(t(e[r],r,e))return!0;return!1}},7678:e=>{"use strict";e.exports=function(e){return e.split("")}},6281:(e,t,r)=>{"use strict";var n=r(2012);e.exports=function(e,t){for(var r=e.length;r--;)if(n(e[r][0],t))return r;return -1}},8667:(e,t,r)=>{"use strict";var n=r(4356);e.exports=function(e,t,r){"__proto__"==t&&n?n(e,t,{configurable:!0,enumerable:!0,value:r,writable:!0}):e[t]=r}},3033:(e,t,r)=>{"use strict";var n=r(9999),o=r(9076)(n);e.exports=o},3747:(e,t,r)=>{"use strict";var n=r(3033);e.exports=function(e,t){var r=!0;return n(e,function(e,n,o){return r=!!t(e,n,o)}),r}},1827:(e,t,r)=>{"use strict";var n=r(2211);e.exports=function(e,t,r){for(var o=-1,i=e.length;++o<i;){var a=e[o],u=t(a);if(null!=u&&(void 0===c?u==u&&!n(u):r(u,c)))var c=u,l=a}return l}},2844:e=>{"use strict";e.exports=function(e,t,r,n){for(var o=e.length,i=r+(n?1:-1);n?i--:++i<o;)if(t(e[i],i,e))return i;return -1}},3514:(e,t,r)=>{"use strict";var n=r(86),o=r(3925);e.exports=function e(t,r,i,a,u){var c=-1,l=t.length;for(i||(i=o),u||(u=[]);++c<l;){var s=t[c];r>0&&i(s)?r>1?e(s,r-1,i,a,u):n(u,s):a||(u[u.length]=s)}return u}},1434:(e,t,r)=>{"use strict";var n=r(9855)();e.exports=n},9999:(e,t,r)=>{"use strict";var n=r(1434),o=r(1917);e.exports=function(e,t){return e&&n(e,t,o)}},4660:(e,t,r)=>{"use strict";var n=r(2288),o=r(6056);e.exports=function(e,t){t=n(t,e);for(var r=0,i=t.length;null!=e&&r<i;)e=e[o(t[r++])];return r&&r==i?e:void 0}},6175:(e,t,r)=>{"use strict";var n=r(86),o=r(6887);e.exports=function(e,t,r){var i=t(e);return o(e)?i:n(i,r(e))}},5020:(e,t,r)=>{"use strict";var Symbol=r(8586),n=r(5249),o=r(4729),i=Symbol?Symbol.toStringTag:void 0;e.exports=function(e){return null==e?void 0===e?"[object Undefined]":"[object Null]":i&&i in Object(e)?n(e):o(e)}},8339:e=>{"use strict";e.exports=function(e,t){return e>t}},1111:e=>{"use strict";e.exports=function(e,t){return null!=e&&t in Object(e)}},134:(e,t,r)=>{"use strict";var n=r(2844),o=r(3122),i=r(4580);e.exports=function(e,t,r){return t==t?i(e,t,r):n(e,o,r)}},8809:(e,t,r)=>{"use strict";var n=r(5020),o=r(492);e.exports=function(e){return o(e)&&"[object Arguments]"==n(e)}},8416:(e,t,r)=>{"use strict";var n=r(9578),o=r(492);e.exports=function e(t,r,i,a,u){return t===r||(null!=t&&null!=r&&(o(t)||o(r))?n(t,r,i,a,e,u):t!=t&&r!=r)}},9578:(e,t,r)=>{"use strict";var n=r(1401),o=r(1240),i=r(6810),a=r(9054),u=r(7242),c=r(6887),l=r(2014),s=r(1209),f="[object Arguments]",p="[object Array]",d="[object Object]",h=Object.prototype.hasOwnProperty;e.exports=function(e,t,r,y,v,m){var g=c(e),b=c(t),x=g?p:u(e),w=b?p:u(t);x=x==f?d:x,w=w==f?d:w;var O=x==d,j=w==d,S=x==w;if(S&&l(e)){if(!l(t))return!1;g=!0,O=!1}if(S&&!O)return m||(m=new n),g||s(e)?o(e,t,r,y,v,m):i(e,t,x,r,y,v,m);if(!(1&r)){var P=O&&h.call(e,"__wrapped__"),E=j&&h.call(t,"__wrapped__");if(P||E){var k=P?e.value():e,A=E?t.value():t;return m||(m=new n),v(k,A,r,y,m)}}return!!S&&(m||(m=new n),a(e,t,r,y,v,m))}},6681:(e,t,r)=>{"use strict";var n=r(1401),o=r(8416);e.exports=function(e,t,r,i){var a=r.length,u=a,c=!i;if(null==e)return!u;for(e=Object(e);a--;){var l=r[a];if(c&&l[2]?l[1]!==e[l[0]]:!(l[0]in e))return!1}for(;++a<u;){var s=(l=r[a])[0],f=e[s],p=l[1];if(c&&l[2]){if(void 0===f&&!(s in e))return!1}else{var d=new n;if(i)var h=i(f,p,s,e,t,d);if(!(void 0===h?o(p,f,3,i,d):h))return!1}}return!0}},3122:e=>{"use strict";e.exports=function(e){return e!=e}},3571:(e,t,r)=>{"use strict";var n=r(1488),o=r(8555),i=r(2581),a=r(3405),u=/^\[object .+?Constructor\]$/,c=Object.prototype,l=Function.prototype.toString,s=c.hasOwnProperty,f=RegExp("^"+l.call(s).replace(/[\\^$.*+?()[\]{}|]/g,"\\$&").replace(/hasOwnProperty|(function).*?(?=\\\()| for .+?(?=\\\])/g,"$1.*?")+"$");e.exports=function(e){return!(!i(e)||o(e))&&(n(e)?f:u).test(a(e))}},4242:(e,t,r)=>{"use strict";var n=r(5020),o=r(327),i=r(492),a={};a["[object Float32Array]"]=a["[object Float64Array]"]=a["[object Int8Array]"]=a["[object Int16Array]"]=a["[object Int32Array]"]=a["[object Uint8Array]"]=a["[object Uint8ClampedArray]"]=a["[object Uint16Array]"]=a["[object Uint32Array]"]=!0,a["[object Arguments]"]=a["[object Array]"]=a["[object ArrayBuffer]"]=a["[object Boolean]"]=a["[object DataView]"]=a["[object Date]"]=a["[object Error]"]=a["[object Function]"]=a["[object Map]"]=a["[object Number]"]=a["[object Object]"]=a["[object RegExp]"]=a["[object Set]"]=a["[object String]"]=a["[object WeakMap]"]=!1,e.exports=function(e){return i(e)&&o(e.length)&&!!a[n(e)]}},4026:(e,t,r)=>{"use strict";var n=r(7996),o=r(7487),i=r(605),a=r(6887),u=r(9518);e.exports=function(e){return"function"==typeof e?e:null==e?i:"object"==typeof e?a(e)?o(e[0],e[1]):n(e):u(e)}},9107:(e,t,r)=>{"use strict";var n=r(7522),o=r(2541),i=Object.prototype.hasOwnProperty;e.exports=function(e){if(!n(e))return o(e);var t=[];for(var r in Object(e))i.call(e,r)&&"constructor"!=r&&t.push(r);return t}},5225:e=>{"use strict";e.exports=function(e,t){return e<t}},419:(e,t,r)=>{"use strict";var n=r(3033),o=r(7524);e.exports=function(e,t){var r=-1,i=o(e)?Array(e.length):[];return n(e,function(e,n,o){i[++r]=t(e,n,o)}),i}},7996:(e,t,r)=>{"use strict";var n=r(6681),o=r(3222),i=r(5718);e.exports=function(e){var t=o(e);return 1==t.length&&t[0][2]?i(t[0][0],t[0][1]):function(r){return r===e||n(r,e,t)}}},7487:(e,t,r)=>{"use strict";var n=r(8416),o=r(7880),i=r(6723),a=r(1967),u=r(7011),c=r(5718),l=r(6056);e.exports=function(e,t){return a(e)&&u(t)?c(l(e),t):function(r){var a=o(r,e);return void 0===a&&a===t?i(r,e):n(t,a,3)}}},4128:(e,t,r)=>{"use strict";var n=r(7174),o=r(4660),i=r(4026),a=r(419),u=r(4264),c=r(2533),l=r(8713),s=r(605),f=r(6887);e.exports=function(e,t,r){t=t.length?n(t,function(e){return f(e)?function(t){return o(t,1===e.length?e[0]:e)}:e}):[s];var p=-1;return t=n(t,c(i)),u(a(e,function(e,r,o){return{criteria:n(t,function(t){return t(e)}),index:++p,value:e}}),function(e,t){return l(e,t,r)})}},2601:e=>{"use strict";e.exports=function(e){return function(t){return null==t?void 0:t[e]}}},761:(e,t,r)=>{"use strict";var n=r(4660);e.exports=function(e){return function(t){return n(t,e)}}},7791:e=>{"use strict";var t=Math.ceil,r=Math.max;e.exports=function(e,n,o,i){for(var a=-1,u=r(t((n-e)/(o||1)),0),c=Array(u);u--;)c[i?u:++a]=e,e+=o;return c}},1903:(e,t,r)=>{"use strict";var n=r(605),o=r(483),i=r(1411);e.exports=function(e,t){return i(o(e,t,n),e+"")}},4354:(e,t,r)=>{"use strict";var n=r(6203),o=r(4356),i=r(605),a=o?function(e,t){return o(e,"toString",{configurable:!0,enumerable:!1,value:n(t),writable:!0})}:i;e.exports=a},5881:e=>{"use strict";e.exports=function(e,t,r){var n=-1,o=e.length;t<0&&(t=-t>o?0:o+t),(r=r>o?o:r)<0&&(r+=o),o=t>r?0:r-t>>>0,t>>>=0;for(var i=Array(o);++n<o;)i[n]=e[n+t];return i}},8523:(e,t,r)=>{"use strict";var n=r(3033);e.exports=function(e,t){var r;return n(e,function(e,n,o){return!(r=t(e,n,o))}),!!r}},4264:e=>{"use strict";e.exports=function(e,t){var r=e.length;for(e.sort(t);r--;)e[r]=e[r].value;return e}},810:e=>{"use strict";e.exports=function(e,t){for(var r=-1,n=Array(e);++r<e;)n[r]=t(r);return n}},4883:(e,t,r)=>{"use strict";var Symbol=r(8586),n=r(7174),o=r(6887),i=r(2211),a=1/0,u=Symbol?Symbol.prototype:void 0,c=u?u.toString:void 0;e.exports=function e(t){if("string"==typeof t)return t;if(o(t))return n(t,e)+"";if(i(t))return c?c.call(t):"";var r=t+"";return"0"==r&&1/t==-a?"-0":r}},7091:(e,t,r)=>{"use strict";var n=r(246),o=/^\s+/;e.exports=function(e){return e?e.slice(0,n(e)+1).replace(o,""):e}},2533:e=>{"use strict";e.exports=function(e){return function(t){return e(t)}}},121:(e,t,r)=>{"use strict";var n=r(7226),o=r(6968),i=r(9774),a=r(5758),u=r(7168),c=r(8799);e.exports=function(e,t,r){var l=-1,s=o,f=e.length,p=!0,d=[],h=d;if(r)p=!1,s=i;else if(f>=200){var y=t?null:u(e);if(y)return c(y);p=!1,s=a,h=new n}else h=t?[]:d;t:for(;++l<f;){var v=e[l],m=t?t(v):v;if(v=r||0!==v?v:0,p&&m==m){for(var g=h.length;g--;)if(h[g]===m)continue t;t&&h.push(m),d.push(v)}else s(h,m,r)||(h!==d&&h.push(m),d.push(v))}return d}},5758:e=>{"use strict";e.exports=function(e,t){return e.has(t)}},2288:(e,t,r)=>{"use strict";var n=r(6887),o=r(1967),i=r(1165),a=r(439);e.exports=function(e,t){return n(e)?e:o(e,t)?[e]:i(a(e))}},994:(e,t,r)=>{"use strict";var n=r(5881);e.exports=function(e,t,r){var o=e.length;return r=void 0===r?o:r,!t&&r>=o?e:n(e,t,r)}},4386:(e,t,r)=>{"use strict";var n=r(2211);e.exports=function(e,t){if(e!==t){var r=void 0!==e,o=null===e,i=e==e,a=n(e),u=void 0!==t,c=null===t,l=t==t,s=n(t);if(!c&&!s&&!a&&e>t||a&&u&&l&&!c&&!s||o&&u&&l||!r&&l||!i)return 1;if(!o&&!a&&!s&&e<t||s&&r&&i&&!o&&!a||c&&r&&i||!u&&i||!l)return -1}return 0}},8713:(e,t,r)=>{"use strict";var n=r(4386);e.exports=function(e,t,r){for(var o=-1,i=e.criteria,a=t.criteria,u=i.length,c=r.length;++o<u;){var l=n(i[o],a[o]);if(l){if(o>=c)return l;return l*("desc"==r[o]?-1:1)}}return e.index-t.index}},6307:(e,t,r)=>{"use strict";var n=r(8223)["__core-js_shared__"];e.exports=n},9076:(e,t,r)=>{"use strict";var n=r(7524);e.exports=function(e,t){return function(r,o){if(null==r)return r;if(!n(r))return e(r,o);for(var i=r.length,a=t?i:-1,u=Object(r);(t?a--:++a<i)&&!1!==o(u[a],a,u););return r}}},9855:e=>{"use strict";e.exports=function(e){return function(t,r,n){for(var o=-1,i=Object(t),a=n(t),u=a.length;u--;){var c=a[e?u:++o];if(!1===r(i[c],c,i))break}return t}}},1675:(e,t,r)=>{"use strict";var n=r(994),o=r(4676),i=r(3921),a=r(439);e.exports=function(e){return function(t){var r=o(t=a(t))?i(t):void 0,u=r?r[0]:t.charAt(0),c=r?n(r,1).join(""):t.slice(1);return u[e]()+c}}},2720:(e,t,r)=>{"use strict";var n=r(4026),o=r(7524),i=r(1917);e.exports=function(e){return function(t,r,a){var u=Object(t);if(!o(t)){var c=n(r,3);t=i(t),r=function(e){return c(u[e],e,u)}}var l=e(t,r,a);return l>-1?u[c?t[l]:l]:void 0}}},1285:(e,t,r)=>{"use strict";var n=r(7791),o=r(8271),i=r(7889);e.exports=function(e){return function(t,r,a){return a&&"number"!=typeof a&&o(t,r,a)&&(r=a=void 0),t=i(t),void 0===r?(r=t,t=0):r=i(r),a=void 0===a?t<r?1:-1:i(a),n(t,r,a,e)}}},7168:(e,t,r)=>{"use strict";var Set=r(193),n=r(1539),o=r(8799),i=Set&&1/o(new Set([,-0]))[1]==1/0?function(e){return new Set(e)}:n;e.exports=i},4356:(e,t,r)=>{"use strict";var n=r(3906),o=function(){try{var e=n(Object,"defineProperty");return e({},"",{}),e}catch(e){}}();e.exports=o},1240:(e,t,r)=>{"use strict";var n=r(7226),o=r(7951),i=r(5758);e.exports=function(e,t,r,a,u,c){var l=1&r,s=e.length,f=t.length;if(s!=f&&!(l&&f>s))return!1;var p=c.get(e),d=c.get(t);if(p&&d)return p==t&&d==e;var h=-1,y=!0,v=2&r?new n:void 0;for(c.set(e,t),c.set(t,e);++h<s;){var m=e[h],g=t[h];if(a)var b=l?a(g,m,h,t,e,c):a(m,g,h,e,t,c);if(void 0!==b){if(b)continue;y=!1;break}if(v){if(!o(t,function(e,t){if(!i(v,t)&&(m===e||u(m,e,r,a,c)))return v.push(t)})){y=!1;break}}else if(!(m===g||u(m,g,r,a,c))){y=!1;break}}return c.delete(e),c.delete(t),y}},6810:(e,t,r)=>{"use strict";var Symbol=r(8586),Uint8Array=r(1055),n=r(2012),o=r(1240),i=r(2691),a=r(8799),u=Symbol?Symbol.prototype:void 0,c=u?u.valueOf:void 0;e.exports=function(e,t,r,u,l,s,f){switch(r){case"[object DataView]":if(e.byteLength!=t.byteLength||e.byteOffset!=t.byteOffset)break;e=e.buffer,t=t.buffer;case"[object ArrayBuffer]":if(e.byteLength!=t.byteLength||!s(new Uint8Array(e),new Uint8Array(t)))break;return!0;case"[object Boolean]":case"[object Date]":case"[object Number]":return n(+e,+t);case"[object Error]":return e.name==t.name&&e.message==t.message;case"[object RegExp]":case"[object String]":return e==t+"";case"[object Map]":var p=i;case"[object Set]":var d=1&u;if(p||(p=a),e.size!=t.size&&!d)break;var h=f.get(e);if(h)return h==t;u|=2,f.set(e,t);var y=o(p(e),p(t),u,l,s,f);return f.delete(e),y;case"[object Symbol]":if(c)return c.call(e)==c.call(t)}return!1}},9054:(e,t,r)=>{"use strict";var n=r(3229),o=Object.prototype.hasOwnProperty;e.exports=function(e,t,r,i,a,u){var c=1&r,l=n(e),s=l.length;if(s!=n(t).length&&!c)return!1;for(var f=s;f--;){var p=l[f];if(!(c?p in t:o.call(t,p)))return!1}var d=u.get(e),h=u.get(t);if(d&&h)return d==t&&h==e;var y=!0;u.set(e,t),u.set(t,e);for(var v=c;++f<s;){var m=e[p=l[f]],g=t[p];if(i)var b=c?i(g,m,p,t,e,u):i(m,g,p,e,t,u);if(!(void 0===b?m===g||a(m,g,r,i,u):b)){y=!1;break}v||(v="constructor"==p)}if(y&&!v){var x=e.constructor,w=t.constructor;x!=w&&"constructor"in e&&"constructor"in t&&!("function"==typeof x&&x instanceof x&&"function"==typeof w&&w instanceof w)&&(y=!1)}return u.delete(e),u.delete(t),y}},9470:e=>{"use strict";var t="object"==typeof global&&global&&global.Object===Object&&global;e.exports=t},3229:(e,t,r)=>{"use strict";var n=r(6175),o=r(6418),i=r(1917);e.exports=function(e){return n(e,i,o)}},2061:(e,t,r)=>{"use strict";var n=r(2916);e.exports=function(e,t){var r=e.__data__;return n(t)?r["string"==typeof t?"string":"hash"]:r.map}},3222:(e,t,r)=>{"use strict";var n=r(7011),o=r(1917);e.exports=function(e){for(var t=o(e),r=t.length;r--;){var i=t[r],a=e[i];t[r]=[i,a,n(a)]}return t}},3906:(e,t,r)=>{"use strict";var n=r(3571),o=r(6422);e.exports=function(e,t){var r=o(e,t);return n(r)?r:void 0}},6612:(e,t,r)=>{"use strict";var n=r(896)(Object.getPrototypeOf,Object);e.exports=n},5249:(e,t,r)=>{"use strict";var Symbol=r(8586),n=Object.prototype,o=n.hasOwnProperty,i=n.toString,a=Symbol?Symbol.toStringTag:void 0;e.exports=function(e){var t=o.call(e,a),r=e[a];try{e[a]=void 0;var n=!0}catch(e){}var u=i.call(e);return n&&(t?e[a]=r:delete e[a]),u}},6418:(e,t,r)=>{"use strict";var n=r(5448),o=r(5928),i=Object.prototype.propertyIsEnumerable,a=Object.getOwnPropertySymbols,u=a?function(e){return null==e?[]:n(a(e=Object(e)),function(t){return i.call(e,t)})}:o;e.exports=u},7242:(e,t,r)=>{"use strict";var DataView=r(7018),Map=r(5608),Promise=r(1904),Set=r(193),WeakMap=r(16),n=r(5020),o=r(3405),i="[object Map]",a="[object Promise]",u="[object Set]",c="[object WeakMap]",l="[object DataView]",s=o(DataView),f=o(Map),p=o(Promise),d=o(Set),h=o(WeakMap),y=n;(DataView&&y(new DataView(new ArrayBuffer(1)))!=l||Map&&y(new Map)!=i||Promise&&y(Promise.resolve())!=a||Set&&y(new Set)!=u||WeakMap&&y(new WeakMap)!=c)&&(y=function(e){var t=n(e),r="[object Object]"==t?e.constructor:void 0,y=r?o(r):"";if(y)switch(y){case s:return l;case f:return i;case p:return a;case d:return u;case h:return c}return t}),e.exports=y},6422:e=>{"use strict";e.exports=function(e,t){return null==e?void 0:e[t]}},9414:(e,t,r)=>{"use strict";var n=r(2288),o=r(4451),i=r(6887),a=r(9128),u=r(327),c=r(6056);e.exports=function(e,t,r){t=n(t,e);for(var l=-1,s=t.length,f=!1;++l<s;){var p=c(t[l]);if(!(f=null!=e&&r(e,p)))break;e=e[p]}return f||++l!=s?f:!!(s=null==e?0:e.length)&&u(s)&&a(p,s)&&(i(e)||o(e))}},4676:e=>{"use strict";var t=RegExp("[\\u200d\ud800-\udfff\\u0300-\\u036f\\ufe20-\\ufe2f\\u20d0-\\u20ff\\ufe0e\\ufe0f]");e.exports=function(e){return t.test(e)}},3609:(e,t,r)=>{"use strict";var n=r(6271);e.exports=function(){this.__data__=n?n(null):{},this.size=0}},2412:e=>{"use strict";e.exports=function(e){var t=this.has(e)&&delete this.__data__[e];return this.size-=t?1:0,t}},5506:(e,t,r)=>{"use strict";var n=r(6271),o=Object.prototype.hasOwnProperty;e.exports=function(e){var t=this.__data__;if(n){var r=t[e];return"__lodash_hash_undefined__"===r?void 0:r}return o.call(t,e)?t[e]:void 0}},5287:(e,t,r)=>{"use strict";var n=r(6271),o=Object.prototype.hasOwnProperty;e.exports=function(e){var t=this.__data__;return n?void 0!==t[e]:o.call(t,e)}},2463:(e,t,r)=>{"use strict";var n=r(6271);e.exports=function(e,t){var r=this.__data__;return this.size+=this.has(e)?0:1,r[e]=n&&void 0===t?"__lodash_hash_undefined__":t,this}},3925:(e,t,r)=>{"use strict";var Symbol=r(8586),n=r(4451),o=r(6887),i=Symbol?Symbol.isConcatSpreadable:void 0;e.exports=function(e){return o(e)||n(e)||!!(i&&e&&e[i])}},9128:e=>{"use strict";var t=/^(?:0|[1-9]\d*)$/;e.exports=function(e,r){var n=typeof e;return!!(r=null==r?9007199254740991:r)&&("number"==n||"symbol"!=n&&t.test(e))&&e>-1&&e%1==0&&e<r}},8271:(e,t,r)=>{"use strict";var n=r(2012),o=r(7524),i=r(9128),a=r(2581);e.exports=function(e,t,r){if(!a(r))return!1;var u=typeof t;return("number"==u?!!(o(r)&&i(t,r.length)):"string"==u&&t in r)&&n(r[t],e)}},1967:(e,t,r)=>{"use strict";var n=r(6887),o=r(2211),i=/\.|\[(?:[^[\]]*|(["'])(?:(?!\1)[^\\]|\\.)*?\1)\]/,a=/^\w*$/;e.exports=function(e,t){if(n(e))return!1;var r=typeof e;return!!("number"==r||"symbol"==r||"boolean"==r||null==e||o(e))||a.test(e)||!i.test(e)||null!=t&&e in Object(t)}},2916:e=>{"use strict";e.exports=function(e){var t=typeof e;return"string"==t||"number"==t||"symbol"==t||"boolean"==t?"__proto__"!==e:null===e}},8555:(e,t,r)=>{"use strict";var n=r(6307),o=function(){var e=/[^.]+$/.exec(n&&n.keys&&n.keys.IE_PROTO||"");return e?"Symbol(src)_1."+e:""}();e.exports=function(e){return!!o&&o in e}},7522:e=>{"use strict";var t=Object.prototype;e.exports=function(e){var r=e&&e.constructor;return e===("function"==typeof r&&r.prototype||t)}},7011:(e,t,r)=>{"use strict";var n=r(2581);e.exports=function(e){return e==e&&!n(e)}},7979:e=>{"use strict";e.exports=function(){this.__data__=[],this.size=0}},5869:(e,t,r)=>{"use strict";var n=r(6281),o=Array.prototype.splice;e.exports=function(e){var t=this.__data__,r=n(t,e);return!(r<0)&&(r==t.length-1?t.pop():o.call(t,r,1),--this.size,!0)}},5361:(e,t,r)=>{"use strict";var n=r(6281);e.exports=function(e){var t=this.__data__,r=n(t,e);return r<0?void 0:t[r][1]}},1100:(e,t,r)=>{"use strict";var n=r(6281);e.exports=function(e){return n(this.__data__,e)>-1}},5569:(e,t,r)=>{"use strict";var n=r(6281);e.exports=function(e,t){var r=this.__data__,o=n(r,e);return o<0?(++this.size,r.push([e,t])):r[o][1]=t,this}},8844:(e,t,r)=>{"use strict";var n=r(2265),o=r(3324),Map=r(5608);e.exports=function(){this.size=0,this.__data__={hash:new n,map:new(Map||o),string:new n}}},1856:(e,t,r)=>{"use strict";var n=r(2061);e.exports=function(e){var t=n(this,e).delete(e);return this.size-=t?1:0,t}},5353:(e,t,r)=>{"use strict";var n=r(2061);e.exports=function(e){return n(this,e).get(e)}},7775:(e,t,r)=>{"use strict";var n=r(2061);e.exports=function(e){return n(this,e).has(e)}},8399:(e,t,r)=>{"use strict";var n=r(2061);e.exports=function(e,t){var r=n(this,e),o=r.size;return r.set(e,t),this.size+=r.size==o?0:1,this}},2691:e=>{"use strict";e.exports=function(e){var t=-1,r=Array(e.size);return e.forEach(function(e,n){r[++t]=[n,e]}),r}},5718:e=>{"use strict";e.exports=function(e,t){return function(r){return null!=r&&r[e]===t&&(void 0!==t||e in Object(r))}}},4975:(e,t,r)=>{"use strict";var n=r(5109);e.exports=function(e){var t=n(e,function(e){return 500===r.size&&r.clear(),e}),r=t.cache;return t}},6271:(e,t,r)=>{"use strict";var n=r(3906)(Object,"create");e.exports=n},2541:(e,t,r)=>{"use strict";var n=r(896)(Object.keys,Object);e.exports=n},9108:(e,t,r)=>{"use strict";e=r.nmd(e);var n=r(9470),o=t&&!t.nodeType&&t,i=o&&e&&!e.nodeType&&e,a=i&&i.exports===o&&n.process,u=function(){try{var e=i&&i.require&&i.require("util").types;if(e)return e;return a&&a.binding&&a.binding("util")}catch(e){}}();e.exports=u},4729:e=>{"use strict";var t=Object.prototype.toString;e.exports=function(e){return t.call(e)}},896:e=>{"use strict";e.exports=function(e,t){return function(r){return e(t(r))}}},483:(e,t,r)=>{"use strict";var n=r(9493),o=Math.max;e.exports=function(e,t,r){return t=o(void 0===t?e.length-1:t,0),function(){for(var i=arguments,a=-1,u=o(i.length-t,0),c=Array(u);++a<u;)c[a]=i[t+a];a=-1;for(var l=Array(t+1);++a<t;)l[a]=i[a];return l[t]=r(c),n(e,this,l)}}},8223:(e,t,r)=>{"use strict";var n=r(9470),o="object"==typeof self&&self&&self.Object===Object&&self,i=n||o||Function("return this")();e.exports=i},7051:e=>{"use strict";e.exports=function(e){return this.__data__.set(e,"__lodash_hash_undefined__"),this}},6816:e=>{"use strict";e.exports=function(e){return this.__data__.has(e)}},8799:e=>{"use strict";e.exports=function(e){var t=-1,r=Array(e.size);return e.forEach(function(e){r[++t]=e}),r}},1411:(e,t,r)=>{"use strict";var n=r(4354),o=r(1761)(n);e.exports=o},1761:e=>{"use strict";var t=Date.now;e.exports=function(e){var r=0,n=0;return function(){var o=t(),i=16-(o-n);if(n=o,i>0){if(++r>=800)return arguments[0]}else r=0;return e.apply(void 0,arguments)}}},9394:(e,t,r)=>{"use strict";var n=r(3324);e.exports=function(){this.__data__=new n,this.size=0}},7263:e=>{"use strict";e.exports=function(e){var t=this.__data__,r=t.delete(e);return this.size=t.size,r}},4246:e=>{"use strict";e.exports=function(e){return this.__data__.get(e)}},9915:e=>{"use strict";e.exports=function(e){return this.__data__.has(e)}},6089:(e,t,r)=>{"use strict";var n=r(3324),Map=r(5608),o=r(3883);e.exports=function(e,t){var r=this.__data__;if(r instanceof n){var i=r.__data__;if(!Map||i.length<199)return i.push([e,t]),this.size=++r.size,this;r=this.__data__=new o(i)}return r.set(e,t),this.size=r.size,this}},4580:e=>{"use strict";e.exports=function(e,t,r){for(var n=r-1,o=e.length;++n<o;)if(e[n]===t)return n;return -1}},3921:(e,t,r)=>{"use strict";var n=r(7678),o=r(4676),i=r(969);e.exports=function(e){return o(e)?i(e):n(e)}},1165:(e,t,r)=>{"use strict";var n=r(4975),o=/[^.[\]]+|\[(?:(-?\d+(?:\.\d+)?)|(["'])((?:(?!\2)[^\\]|\\.)*?)\2)\]|(?=(?:\.|\[\])(?:\.|\[\]|$))/g,i=/\\(\\)?/g,a=n(function(e){var t=[];return 46===e.charCodeAt(0)&&t.push(""),e.replace(o,function(e,r,n,o){t.push(n?o.replace(i,"$1"):r||e)}),t});e.exports=a},6056:(e,t,r)=>{"use strict";var n=r(2211),o=1/0;e.exports=function(e){if("string"==typeof e||n(e))return e;var t=e+"";return"0"==t&&1/e==-o?"-0":t}},3405:e=>{"use strict";var t=Function.prototype.toString;e.exports=function(e){if(null!=e){try{return t.call(e)}catch(e){}try{return e+""}catch(e){}}return""}},246:e=>{"use strict";var t=/\s/;e.exports=function(e){for(var r=e.length;r--&&t.test(e.charAt(r)););return r}},969:e=>{"use strict";var t="\ud800-\udfff",r="[\\u0300-\\u036f\\ufe20-\\ufe2f\\u20d0-\\u20ff]",n="\ud83c[\udffb-\udfff]",o="[^"+t+"]",i="(?:\ud83c[\udde6-\uddff]){2}",a="[\ud800-\udbff][\udc00-\udfff]",u="(?:"+r+"|"+n+")?",c="[\\ufe0e\\ufe0f]?",l="(?:\\u200d(?:"+[o,i,a].join("|")+")"+c+u+")*",s=RegExp(n+"(?="+n+")|(?:"+[o+r+"?",r,i,a,"["+t+"]"].join("|")+")"+(c+u+l),"g");e.exports=function(e){return e.match(s)||[]}},6203:e=>{"use strict";e.exports=function(e){return function(){return e}}},8422:(e,t,r)=>{"use strict";var n=r(2581),o=r(3706),i=r(602),a=Math.max,u=Math.min;e.exports=function(e,t,r){var c,l,s,f,p,d,h=0,y=!1,v=!1,m=!0;if("function"!=typeof e)throw TypeError("Expected a function");function g(t){var r=c,n=l;return c=l=void 0,h=t,f=e.apply(n,r)}function b(e){var r=e-d,n=e-h;return void 0===d||r>=t||r<0||v&&n>=s}function x(){var e,r,n,i=o();if(b(i))return w(i);p=setTimeout(x,(e=i-d,r=i-h,n=t-e,v?u(n,s-r):n))}function w(e){return(p=void 0,m&&c)?g(e):(c=l=void 0,f)}function O(){var e,r=o(),n=b(r);if(c=arguments,l=this,d=r,n){if(void 0===p)return h=e=d,p=setTimeout(x,t),y?g(e):f;if(v)return clearTimeout(p),p=setTimeout(x,t),g(d)}return void 0===p&&(p=setTimeout(x,t)),f}return t=i(t)||0,n(r)&&(y=!!r.leading,s=(v="maxWait"in r)?a(i(r.maxWait)||0,t):s,m="trailing"in r?!!r.trailing:m),O.cancel=function(){void 0!==p&&clearTimeout(p),h=0,c=d=l=p=void 0},O.flush=function(){return void 0===p?f:w(o())},O}},2012:e=>{"use strict";e.exports=function(e,t){return e===t||e!=e&&t!=t}},6871:(e,t,r)=>{"use strict";var n=r(5395),o=r(3747),i=r(4026),a=r(6887),u=r(8271);e.exports=function(e,t,r){var c=a(e)?n:o;return r&&u(e,t,r)&&(t=void 0),c(e,i(t,3))}},7712:(e,t,r)=>{"use strict";var n=r(2720)(r(6991));e.exports=n},6991:(e,t,r)=>{"use strict";var n=r(2844),o=r(4026),i=r(4502),a=Math.max;e.exports=function(e,t,r){var u=null==e?0:e.length;if(!u)return -1;var c=null==r?0:i(r);return c<0&&(c=a(u+c,0)),n(e,o(t,3),c)}},1969:(e,t,r)=>{"use strict";var n=r(3514),o=r(9484);e.exports=function(e,t){return n(o(e,t),1)}},7880:(e,t,r)=>{"use strict";var n=r(4660);e.exports=function(e,t,r){var o=null==e?void 0:n(e,t);return void 0===o?r:o}},6723:(e,t,r)=>{"use strict";var n=r(1111),o=r(9414);e.exports=function(e,t){return null!=e&&o(e,t,n)}},605:e=>{"use strict";e.exports=function(e){return e}},4451:(e,t,r)=>{"use strict";var n=r(8809),o=r(492),i=Object.prototype,a=i.hasOwnProperty,u=i.propertyIsEnumerable,c=n(function(){return arguments}())?n:function(e){return o(e)&&a.call(e,"callee")&&!u.call(e,"callee")};e.exports=c},6887:e=>{"use strict";var t=Array.isArray;e.exports=t},7524:(e,t,r)=>{"use strict";var n=r(1488),o=r(327);e.exports=function(e){return null!=e&&o(e.length)&&!n(e)}},518:(e,t,r)=>{"use strict";var n=r(5020),o=r(492);e.exports=function(e){return!0===e||!1===e||o(e)&&"[object Boolean]"==n(e)}},2014:(e,t,r)=>{"use strict";e=r.nmd(e);var n=r(8223),o=r(6998),i=t&&!t.nodeType&&t,a=i&&e&&!e.nodeType&&e,u=a&&a.exports===i?n.Buffer:void 0,c=u?u.isBuffer:void 0;e.exports=c||o},9154:(e,t,r)=>{"use strict";var n=r(8416);e.exports=function(e,t){return n(e,t)}},1488:(e,t,r)=>{"use strict";var n=r(5020),o=r(2581);e.exports=function(e){if(!o(e))return!1;var t=n(e);return"[object Function]"==t||"[object GeneratorFunction]"==t||"[object AsyncFunction]"==t||"[object Proxy]"==t}},327:e=>{"use strict";e.exports=function(e){return"number"==typeof e&&e>-1&&e%1==0&&e<=9007199254740991}},4829:(e,t,r)=>{"use strict";var n=r(5208);e.exports=function(e){return n(e)&&e!=+e}},1556:e=>{"use strict";e.exports=function(e){return null==e}},5208:(e,t,r)=>{"use strict";var n=r(5020),o=r(492);e.exports=function(e){return"number"==typeof e||o(e)&&"[object Number]"==n(e)}},2581:e=>{"use strict";e.exports=function(e){var t=typeof e;return null!=e&&("object"==t||"function"==t)}},492:e=>{"use strict";e.exports=function(e){return null!=e&&"object"==typeof e}},6030:(e,t,r)=>{"use strict";var n=r(5020),o=r(6612),i=r(492),a=Object.prototype,u=Function.prototype.toString,c=a.hasOwnProperty,l=u.call(Object);e.exports=function(e){if(!i(e)||"[object Object]"!=n(e))return!1;var t=o(e);if(null===t)return!0;var r=c.call(t,"constructor")&&t.constructor;return"function"==typeof r&&r instanceof r&&u.call(r)==l}},631:(e,t,r)=>{"use strict";var n=r(5020),o=r(6887),i=r(492);e.exports=function(e){return"string"==typeof e||!o(e)&&i(e)&&"[object String]"==n(e)}},2211:(e,t,r)=>{"use strict";var n=r(5020),o=r(492);e.exports=function(e){return"symbol"==typeof e||o(e)&&"[object Symbol]"==n(e)}},1209:(e,t,r)=>{"use strict";var n=r(4242),o=r(2533),i=r(9108),a=i&&i.isTypedArray,u=a?o(a):n;e.exports=u},1917:(e,t,r)=>{"use strict";var n=r(4004),o=r(9107),i=r(7524);e.exports=function(e){return i(e)?n(e):o(e)}},6839:e=>{"use strict";e.exports=function(e){var t=null==e?0:e.length;return t?e[t-1]:void 0}},9484:(e,t,r)=>{"use strict";var n=r(7174),o=r(4026),i=r(419),a=r(6887);e.exports=function(e,t){return(a(e)?n:i)(e,o(t,3))}},5125:(e,t,r)=>{"use strict";var n=r(8667),o=r(9999),i=r(4026);e.exports=function(e,t){var r={};return t=i(t,3),o(e,function(e,o,i){n(r,o,t(e,o,i))}),r}},8172:(e,t,r)=>{"use strict";var n=r(1827),o=r(8339),i=r(605);e.exports=function(e){return e&&e.length?n(e,i,o):void 0}},5109:(e,t,r)=>{"use strict";var n=r(3883);function o(e,t){if("function"!=typeof e||null!=t&&"function"!=typeof t)throw TypeError("Expected a function");var r=function(){var n=arguments,o=t?t.apply(this,n):n[0],i=r.cache;if(i.has(o))return i.get(o);var a=e.apply(this,n);return r.cache=i.set(o,a)||i,a};return r.cache=new(o.Cache||n),r}o.Cache=n,e.exports=o},5140:(e,t,r)=>{"use strict";var n=r(1827),o=r(5225),i=r(605);e.exports=function(e){return e&&e.length?n(e,i,o):void 0}},1539:e=>{"use strict";e.exports=function(){}},3706:(e,t,r)=>{"use strict";var n=r(8223);e.exports=function(){return n.Date.now()}},9518:(e,t,r)=>{"use strict";var n=r(2601),o=r(761),i=r(1967),a=r(6056);e.exports=function(e){return i(e)?n(a(e)):o(e)}},4579:(e,t,r)=>{"use strict";var n=r(1285)();e.exports=n},7942:(e,t,r)=>{"use strict";var n=r(7951),o=r(4026),i=r(8523),a=r(6887),u=r(8271);e.exports=function(e,t,r){var c=a(e)?n:i;return r&&u(e,t,r)&&(t=void 0),c(e,o(t,3))}},4560:(e,t,r)=>{"use strict";var n=r(3514),o=r(4128),i=r(1903),a=r(8271),u=i(function(e,t){if(null==e)return[];var r=t.length;return r>1&&a(e,t[0],t[1])?t=[]:r>2&&a(t[0],t[1],t[2])&&(t=[t[0]]),o(e,n(t,1),[])});e.exports=u},5928:e=>{"use strict";e.exports=function(){return[]}},6998:e=>{"use strict";e.exports=function(){return!1}},8892:(e,t,r)=>{"use strict";var n=r(8422),o=r(2581);e.exports=function(e,t,r){var i=!0,a=!0;if("function"!=typeof e)throw TypeError("Expected a function");return o(r)&&(i="leading"in r?!!r.leading:i,a="trailing"in r?!!r.trailing:a),n(e,t,{leading:i,maxWait:t,trailing:a})}},7889:(e,t,r)=>{"use strict";var n=r(602),o=1/0;e.exports=function(e){return e?(e=n(e))===o||e===-o?(e<0?-1:1)*17976931348623157e292:e==e?e:0:0===e?e:0}},4502:(e,t,r)=>{"use strict";var n=r(7889);e.exports=function(e){var t=n(e),r=t%1;return t==t?r?t-r:t:0}},602:(e,t,r)=>{"use strict";var n=r(7091),o=r(2581),i=r(2211),a=0/0,u=/^[-+]0x[0-9a-f]+$/i,c=/^0b[01]+$/i,l=/^0o[0-7]+$/i,s=parseInt;e.exports=function(e){if("number"==typeof e)return e;if(i(e))return a;if(o(e)){var t="function"==typeof e.valueOf?e.valueOf():e;e=o(t)?t+"":t}if("string"!=typeof e)return 0===e?e:+e;e=n(e);var r=c.test(e);return r||l.test(e)?s(e.slice(2),r?2:8):u.test(e)?a:+e}},439:(e,t,r)=>{"use strict";var n=r(4883);e.exports=function(e){return null==e?"":n(e)}},2873:(e,t,r)=>{"use strict";var n=r(4026),o=r(121);e.exports=function(e,t){return e&&e.length?o(e,n(t,2)):[]}},1801:(e,t,r)=>{"use strict";var n=r(1675)("toUpperCase");e.exports=n},2920:(e,t,r)=>{"use strict";var n=r(4054);function o(){}function i(){}i.resetWarningCache=o,e.exports=function(){function e(e,t,r,o,i,a){if(a!==n){var u=Error("Calling PropTypes validators directly is not supported by the `prop-types` package. Use PropTypes.checkPropTypes() to call them. Read more at http://fb.me/use-check-prop-types");throw u.name="Invariant Violation",u}}function t(){return e}e.isRequired=e;var r={array:e,bigint:e,bool:e,func:e,number:e,object:e,string:e,symbol:e,any:e,arrayOf:t,element:e,elementType:e,instanceOf:t,node:e,objectOf:t,oneOf:t,oneOfType:t,shape:t,exact:t,checkPropTypes:i,resetWarningCache:o};return r.PropTypes=r,r}},9069:(e,t,r)=>{"use strict";e.exports=r(2920)()},4054:e=>{"use strict";e.exports="SECRET_DO_NOT_PASS_THIS_OR_YOU_WILL_BE_FIRED"},8182:(e,t,r)=>{"use strict";r.r(t),r.d(t,{$$typeof:()=>a,__esModule:()=>i,default:()=>c});var n=r(5153);let o=(0,n.createProxy)(String.raw`C:\Users\<USER>\Documents\Freela\apps\admin-dashboard\src\app\dashboard\layout.tsx`),{__esModule:i,$$typeof:a}=o,u=o.default,c=u},9228:(e,t,r)=>{"use strict";r.r(t),r.d(t,{$$typeof:()=>a,__esModule:()=>i,default:()=>c});var n=r(5153);let o=(0,n.createProxy)(String.raw`C:\Users\<USER>\Documents\Freela\apps\admin-dashboard\src\app\dashboard\page.tsx`),{__esModule:i,$$typeof:a}=o,u=o.default,c=u},2594:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>d,metadata:()=>f,viewport:()=>p});var n=r(4656),o=r(4302),i=r.n(o),a=r(5153);let u=(0,a.createProxy)(String.raw`C:\Users\<USER>\Documents\Freela\apps\admin-dashboard\src\app\providers.tsx`),{__esModule:c,$$typeof:l}=u;u.default;let s=(0,a.createProxy)(String.raw`C:\Users\<USER>\Documents\Freela\apps\admin-dashboard\src\app\providers.tsx#Providers`);r(5023);let f={title:"Freela Syria - Admin Dashboard",description:"Administrative dashboard for Freela Syria marketplace",keywords:["freelance","syria","admin","dashboard"],authors:[{name:"Freela Syria Team"}]},p={width:"device-width",initialScale:1};function d({children:e}){return(0,n.jsxs)("html",{lang:"ar",dir:"rtl",suppressHydrationWarning:!0,children:[n.jsx("head",{children:n.jsx("link",{href:"https://fonts.googleapis.com/css2?family=Noto+Sans+Arabic:wght@300;400;500;600;700&display=swap",rel:"stylesheet"})}),n.jsx("body",{className:`${i().variable} font-arabic antialiased`,children:n.jsx(s,{children:e})})]})}},5023:()=>{},4700:(e,t,r)=>{"use strict";r.d(t,{Z:()=>i});var n=r(4218);let o=n.forwardRef(function({title:e,titleId:t,...r},o){return n.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:o,"aria-labelledby":t},r),e?n.createElement("title",{id:t},e):null,n.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M20.25 14.15v4.25c0 1.094-.787 2.036-1.872 2.18-2.087.277-4.216.42-6.378.42s-4.291-.143-6.378-.42c-1.085-.144-1.872-1.086-1.872-2.18v-4.25m16.5 0a2.18 2.18 0 0 0 .75-1.661V8.706c0-1.081-.768-2.015-1.837-2.175a48.114 48.114 0 0 0-3.413-.387m4.5 8.006c-.194.165-.42.295-.673.38A23.978 23.978 0 0 1 12 15.75c-2.648 0-5.195-.429-7.577-1.22a2.016 2.016 0 0 1-.673-.38m0 0A2.18 2.18 0 0 1 3 12.489V8.706c0-1.081.768-2.015 1.837-2.175a48.111 48.111 0 0 1 3.413-.387m7.5 0V5.25A2.25 2.25 0 0 0 13.5 3h-3a2.25 2.25 0 0 0-2.25 2.25v.894m7.5 0a48.667 48.667 0 0 0-7.5 0M12 12.75h.008v.008H12v-.008Z"}))}),i=o},7607:(e,t,r)=>{"use strict";r.d(t,{Z:()=>i});var n=r(4218);let o=n.forwardRef(function({title:e,titleId:t,...r},o){return n.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:o,"aria-labelledby":t},r),e?n.createElement("title",{id:t},e):null,n.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M18 18.72a9.094 9.094 0 0 0 3.741-.479 3 3 0 0 0-4.682-2.72m.94 3.198.001.031c0 .225-.012.447-.037.666A11.944 11.944 0 0 1 12 21c-2.17 0-4.207-.576-5.963-1.584A6.062 6.062 0 0 1 6 18.719m12 0a5.971 5.971 0 0 0-.941-3.197m0 0A5.995 5.995 0 0 0 12 12.75a5.995 5.995 0 0 0-5.058 2.772m0 0a3 3 0 0 0-4.681 2.72 8.986 8.986 0 0 0 3.74.477m.94-3.197a5.971 5.971 0 0 0-.94 3.197M15 6.75a3 3 0 1 1-6 0 3 3 0 0 1 6 0Zm6 3a2.25 2.25 0 1 1-4.5 0 2.25 2.25 0 0 1 4.5 0Zm-13.5 0a2.25 2.25 0 1 1-4.5 0 2.25 2.25 0 0 1 4.5 0Z"}))}),i=o},717:(e,t,r)=>{"use strict";r.d(t,{Z:()=>i});var n=r(4218);let o=n.forwardRef(function({title:e,titleId:t,...r},o){return n.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:o,"aria-labelledby":t},r),e?n.createElement("title",{id:t},e):null,n.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M15 19.128a9.38 9.38 0 0 0 2.625.372 9.337 9.337 0 0 0 4.121-.952 4.125 4.125 0 0 0-7.533-2.493M15 19.128v-.003c0-1.113-.285-2.16-.786-3.07M15 19.128v.106A12.318 12.318 0 0 1 8.624 21c-2.331 0-4.512-.645-6.374-1.766l-.001-.109a6.375 6.375 0 0 1 11.964-3.07M12 6.375a3.375 3.375 0 1 1-6.75 0 3.375 3.375 0 0 1 6.75 0Zm8.25 2.25a2.625 2.625 0 1 1-5.25 0 2.625 2.625 0 0 1 5.25 0Z"}))}),i=o},2597:(e,t,r)=>{"use strict";function n(){for(var e,t,r=0,n="",o=arguments.length;r<o;r++)(e=arguments[r])&&(t=function e(t){var r,n,o="";if("string"==typeof t||"number"==typeof t)o+=t;else if("object"==typeof t){if(Array.isArray(t)){var i=t.length;for(r=0;r<i;r++)t[r]&&(n=e(t[r]))&&(o&&(o+=" "),o+=n)}else for(n in t)t[n]&&(o&&(o+=" "),o+=n)}return o}(e))&&(n&&(n+=" "),n+=t);return n}r.d(t,{W:()=>n,Z:()=>o});let o=n}};var t=require("../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),n=t.X(0,[67],()=>r(2756));module.exports=n})();