{"version": 1, "config": {"env": {"NEXT_PUBLIC_API_URL": "http://localhost:3000", "NEXT_PUBLIC_APP_NAME": "Freela Syria Admin"}, "eslint": {"ignoreDuringBuilds": false}, "typescript": {"ignoreBuildErrors": false, "tsconfigPath": "tsconfig.json"}, "distDir": ".next", "cleanDistDir": true, "assetPrefix": "", "configOrigin": "next.config.js", "useFileSystemPublicRoutes": true, "generateEtags": true, "pageExtensions": ["tsx", "ts", "jsx", "js"], "poweredByHeader": true, "compress": true, "analyticsId": "", "images": {"deviceSizes": [640, 750, 828, 1080, 1200, 1920, 2048, 3840], "imageSizes": [16, 32, 48, 64, 96, 128, 256, 384], "path": "/_next/image/", "loader": "default", "loaderFile": "", "domains": ["localhost", "freela-syria.com"], "disableStaticImages": false, "minimumCacheTTL": 60, "formats": ["image/webp", "image/avif"], "dangerouslyAllowSVG": false, "contentSecurityPolicy": "script-src 'none'; frame-src 'none'; sandbox;", "contentDispositionType": "inline", "remotePatterns": [], "unoptimized": false}, "devIndicators": {"buildActivity": true, "buildActivityPosition": "bottom-right"}, "onDemandEntries": {"maxInactiveAge": 60000, "pagesBufferLength": 5}, "amp": {"canonicalBase": ""}, "basePath": "", "sassOptions": {}, "trailingSlash": true, "i18n": null, "productionBrowserSourceMaps": false, "optimizeFonts": true, "excludeDefaultMomentLocales": true, "serverRuntimeConfig": {}, "publicRuntimeConfig": {}, "reactProductionProfiling": false, "reactStrictMode": null, "httpAgentOptions": {"keepAlive": true}, "outputFileTracing": true, "staticPageGenerationTimeout": 60, "swcMinify": true, "output": "export", "modularizeImports": {"@mui/icons-material": {"transform": "@mui/icons-material/{{member}}"}, "date-fns": {"transform": "date-fns/{{member}}"}, "lodash": {"transform": "lodash/{{member}}"}, "lodash-es": {"transform": "lodash-es/{{member}}"}, "ramda": {"transform": "ramda/es/{{member}}"}, "react-bootstrap": {"transform": {"useAccordionButton": "modularize-import-loader?name=useAccordionButton&from=named&as=default!react-bootstrap/AccordionButton", "*": "react-bootstrap/{{member}}"}}, "antd": {"transform": "antd/es/{{kebabCase member}}"}, "ahooks": {"transform": {"createUpdateEffect": "modularize-import-loader?name=createUpdateEffect&from=named&as=default!ahooks/es/createUpdateEffect", "*": "ahooks/es/{{member}}"}}, "@ant-design/icons": {"transform": {"IconProvider": "modularize-import-loader?name=IconProvider&from=named&as=default!@ant-design/icons", "createFromIconfontCN": "@ant-design/icons/es/components/IconFont", "getTwoToneColor": "modularize-import-loader?name=getTwoToneColor&from=named&as=default!@ant-design/icons/es/components/twoTonePrimaryColor", "setTwoToneColor": "modularize-import-loader?name=setTwoToneColor&from=named&as=default!@ant-design/icons/es/components/twoTonePrimaryColor", "*": "@ant-design/icons/lib/icons/{{member}}"}}, "next/server": {"transform": "next/dist/server/web/exports/{{ kebabCase member }}"}}, "experimental": {"windowHistorySupport": false, "serverMinification": true, "serverSourceMaps": false, "caseSensitiveRoutes": false, "useDeploymentId": false, "useDeploymentIdServerActions": false, "clientRouterFilter": true, "clientRouterFilterRedirects": false, "fetchCacheKeyPrefix": "", "middlewarePrefetch": "flexible", "optimisticClientCache": true, "manualClientBasePath": false, "cpus": 15, "memoryBasedWorkersCount": false, "isrFlushToDisk": true, "workerThreads": false, "optimizeCss": false, "nextScriptWorkers": false, "scrollRestoration": false, "externalDir": false, "disableOptimizedLoading": false, "gzipSize": true, "craCompat": false, "esmExternals": false, "isrMemoryCacheSize": 52428800, "fullySpecified": false, "outputFileTracingRoot": "C:\\Users\\<USER>\\Documents\\Freela", "swcTraceProfiling": false, "forceSwcTransforms": false, "largePageDataBytes": 128000, "adjustFontFallbacks": false, "adjustFontFallbacksWithSizeAdjust": false, "typedRoutes": false, "instrumentationHook": false, "bundlePagesExternals": false, "ppr": false, "optimizePackageImports": ["lucide-react", "@headlessui/react", "@headlessui-float/react", "@heroicons/react/20/solid", "@heroicons/react/24/solid", "@heroicons/react/24/outline", "@visx/visx", "@tremor/react", "rxjs", "@mui/material", "@mui/icons-material", "recharts", "react-use", "@material-ui/core", "@material-ui/icons", "@tabler/icons-react", "mui-core", "react-icons/ai", "react-icons/bi", "react-icons/bs", "react-icons/cg", "react-icons/ci", "react-icons/di", "react-icons/fa", "react-icons/fa6", "react-icons/fc", "react-icons/fi", "react-icons/gi", "react-icons/go", "react-icons/gr", "react-icons/hi", "react-icons/hi2", "react-icons/im", "react-icons/io", "react-icons/io5", "react-icons/lia", "react-icons/lib", "react-icons/lu", "react-icons/md", "react-icons/pi", "react-icons/ri", "react-icons/rx", "react-icons/si", "react-icons/sl", "react-icons/tb", "react-icons/tfi", "react-icons/ti", "react-icons/vsc", "react-icons/wi"], "trustHostHeader": false, "isExperimentalCompile": false}, "configFileName": "next.config.js", "skipTrailingSlashRedirect": true, "_originalRewrites": {"beforeFiles": [], "afterFiles": [{"source": "/api/:path*", "destination": "http://localhost:3000/api/:path*"}], "fallback": []}}, "appDir": "C:\\Users\\<USER>\\Documents\\Freela\\apps\\admin-dashboard", "relativeAppDir": "apps\\admin-dashboard", "files": [".next\\routes-manifest.json", ".next\\server\\pages-manifest.json", ".next\\build-manifest.json", ".next\\prerender-manifest.json", ".next\\prerender-manifest.js", ".next\\server\\middleware-manifest.json", ".next\\server\\middleware-build-manifest.js", ".next\\server\\middleware-react-loadable-manifest.js", ".next\\server\\app-paths-manifest.json", ".next\\app-path-routes-manifest.json", ".next\\app-build-manifest.json", ".next\\server\\server-reference-manifest.js", ".next\\server\\server-reference-manifest.json", ".next\\react-loadable-manifest.json", ".next\\server\\font-manifest.json", ".next\\BUILD_ID", ".next\\server\\next-font-manifest.js", ".next\\server\\next-font-manifest.json"], "ignore": ["node_modules\\next\\dist\\compiled\\@ampproject\\toolbox-optimizer\\**\\*"]}