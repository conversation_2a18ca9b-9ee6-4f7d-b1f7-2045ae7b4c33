{"name": "freela-syria", "version": "1.0.0", "description": "Freela Syria - AI-Powered Freelance Marketplace for Syrian Experts", "private": true, "workspaces": ["apps/*", "packages/*"], "scripts": {"dev": "turbo run dev", "dev:api": "turbo run dev --filter=api", "dev:mobile": "turbo run dev --filter=mobile", "dev:admin": "turbo run dev --filter=admin-dashboard", "dev:expert": "turbo run dev --filter=expert-dashboard", "build": "turbo run build", "test": "turbo run test", "test:unit": "turbo run test:unit", "test:integration": "turbo run test:integration", "test:e2e": "turbo run test:e2e", "lint": "turbo run lint", "lint:fix": "turbo run lint:fix", "type-check": "turbo run type-check", "format": "prettier --write \"**/*.{ts,tsx,js,jsx,json,md}\"", "clean": "turbo run clean", "db:migrate": "cd packages/database && npx prisma migrate dev", "db:seed": "cd packages/database && npx prisma db seed", "db:studio": "cd packages/database && npx prisma studio", "db:generate": "cd packages/database && npx prisma generate", "docker:up": "docker-compose up -d", "docker:down": "docker-compose down", "docker:logs": "docker-compose logs -f"}, "devDependencies": {"@types/node": "^20.10.0", "@typescript-eslint/eslint-plugin": "^6.21.0", "@typescript-eslint/parser": "^6.21.0", "eslint": "^8.54.0", "eslint-config-prettier": "^9.0.0", "eslint-config-standard-with-typescript": "^43.0.1", "eslint-plugin-react": "^7.33.0", "eslint-plugin-react-hooks": "^4.6.0", "prettier": "^3.1.0", "turbo": "^1.11.0", "typescript": "^5.3.0"}, "engines": {"node": ">=18.0.0", "npm": ">=9.0.0"}, "repository": {"type": "git", "url": "https://github.com/freela-syria/platform.git"}, "keywords": ["freelance", "marketplace", "syria", "arabic", "ai-powered", "react-native", "nextjs", "nodejs"], "author": "Freela Syria Team", "license": "MIT", "dependencies": {"next-themes": "^0.4.6", "react-hot-toast": "^2.5.2"}}