[{"C:\\Users\\<USER>\\Documents\\Freela\\apps\\admin-dashboard\\src\\app\\dashboard\\layout.tsx": "1", "C:\\Users\\<USER>\\Documents\\Freela\\apps\\admin-dashboard\\src\\app\\dashboard\\page.tsx": "2", "C:\\Users\\<USER>\\Documents\\Freela\\apps\\admin-dashboard\\src\\app\\layout.tsx": "3", "C:\\Users\\<USER>\\Documents\\Freela\\apps\\admin-dashboard\\src\\app\\page.tsx": "4", "C:\\Users\\<USER>\\Documents\\Freela\\apps\\admin-dashboard\\src\\app\\providers.tsx": "5", "C:\\Users\\<USER>\\Documents\\Freela\\apps\\admin-dashboard\\src\\components\\dashboard\\RecentActivity.tsx": "6", "C:\\Users\\<USER>\\Documents\\Freela\\apps\\admin-dashboard\\src\\components\\dashboard\\RevenueChart.tsx": "7", "C:\\Users\\<USER>\\Documents\\Freela\\apps\\admin-dashboard\\src\\components\\dashboard\\StatsCards.tsx": "8", "C:\\Users\\<USER>\\Documents\\Freela\\apps\\admin-dashboard\\src\\components\\dashboard\\UserGrowthChart.tsx": "9", "C:\\Users\\<USER>\\Documents\\Freela\\apps\\admin-dashboard\\src\\components\\layout\\Header.tsx": "10", "C:\\Users\\<USER>\\Documents\\Freela\\apps\\admin-dashboard\\src\\components\\layout\\Sidebar.tsx": "11"}, {"size": 814, "mtime": 1749555746251, "results": "12", "hashOfConfig": "13"}, {"size": 993, "mtime": 1749555755319, "results": "14", "hashOfConfig": "13"}, {"size": 1066, "mtime": 1749559121902, "results": "15", "hashOfConfig": "13"}, {"size": 137, "mtime": 1749555737078, "results": "16", "hashOfConfig": "13"}, {"size": 1372, "mtime": 1749555709670, "results": "17", "hashOfConfig": "13"}, {"size": 3314, "mtime": 1749559018534, "results": "18", "hashOfConfig": "13"}, {"size": 2325, "mtime": 1749555851984, "results": "19", "hashOfConfig": "13"}, {"size": 2365, "mtime": 1749555824545, "results": "20", "hashOfConfig": "13"}, {"size": 2224, "mtime": 1749555838500, "results": "21", "hashOfConfig": "13"}, {"size": 4782, "mtime": 1749555807776, "results": "22", "hashOfConfig": "13"}, {"size": 5193, "mtime": 1749555783340, "results": "23", "hashOfConfig": "13"}, {"filePath": "24", "messages": "25", "suppressedMessages": "26", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "enzm8s", {"filePath": "27", "messages": "28", "suppressedMessages": "29", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "30", "messages": "31", "suppressedMessages": "32", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "33", "messages": "34", "suppressedMessages": "35", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "36", "messages": "37", "suppressedMessages": "38", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "39", "messages": "40", "suppressedMessages": "41", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "42", "messages": "43", "suppressedMessages": "44", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "45", "messages": "46", "suppressedMessages": "47", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "48", "messages": "49", "suppressedMessages": "50", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "51", "messages": "52", "suppressedMessages": "53", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "54", "messages": "55", "suppressedMessages": "56", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\Documents\\Freela\\apps\\admin-dashboard\\src\\app\\dashboard\\layout.tsx", [], [], "C:\\Users\\<USER>\\Documents\\Freela\\apps\\admin-dashboard\\src\\app\\dashboard\\page.tsx", [], [], "C:\\Users\\<USER>\\Documents\\Freela\\apps\\admin-dashboard\\src\\app\\layout.tsx", [], [], "C:\\Users\\<USER>\\Documents\\Freela\\apps\\admin-dashboard\\src\\app\\page.tsx", [], [], "C:\\Users\\<USER>\\Documents\\Freela\\apps\\admin-dashboard\\src\\app\\providers.tsx", [], [], "C:\\Users\\<USER>\\Documents\\Freela\\apps\\admin-dashboard\\src\\components\\dashboard\\RecentActivity.tsx", [], [], "C:\\Users\\<USER>\\Documents\\Freela\\apps\\admin-dashboard\\src\\components\\dashboard\\RevenueChart.tsx", [], [], "C:\\Users\\<USER>\\Documents\\Freela\\apps\\admin-dashboard\\src\\components\\dashboard\\StatsCards.tsx", [], [], "C:\\Users\\<USER>\\Documents\\Freela\\apps\\admin-dashboard\\src\\components\\dashboard\\UserGrowthChart.tsx", [], [], "C:\\Users\\<USER>\\Documents\\Freela\\apps\\admin-dashboard\\src\\components\\layout\\Header.tsx", [], [], "C:\\Users\\<USER>\\Documents\\Freela\\apps\\admin-dashboard\\src\\components\\layout\\Sidebar.tsx", [], []]