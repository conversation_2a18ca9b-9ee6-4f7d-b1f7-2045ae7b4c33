'use client';

import { useEffect } from 'react';

export default function Error({
  error,
  reset,
}: {
  error: Error & { digest?: string };
  reset: () => void;
}) {
  useEffect(() => {
    console.error(error);
  }, [error]);

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50 dark:bg-gray-900">
      <div className="max-w-md w-full space-y-8 text-center">
        <div>
          <h1 className="text-9xl font-bold text-gray-300 dark:text-gray-700">500</h1>
          <h2 className="mt-6 text-3xl font-bold text-gray-900 dark:text-white">
            حدث خطأ
          </h2>
          <p className="mt-2 text-sm text-gray-600 dark:text-gray-400">
            عذراً، حدث خطأ غير متوقع. يرجى المحاولة مرة أخرى.
          </p>
        </div>
        <div className="space-y-4">
          <button
            onClick={reset}
            className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
          >
            المحاولة مرة أخرى
          </button>
          <div>
            <a
              href="/"
              className="text-sm text-primary-600 hover:text-primary-500"
            >
              العودة للرئيسية
            </a>
          </div>
        </div>
      </div>
    </div>
  );
}
