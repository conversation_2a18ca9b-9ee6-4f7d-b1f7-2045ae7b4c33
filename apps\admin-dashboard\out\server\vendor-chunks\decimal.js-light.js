"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/decimal.js-light";
exports.ids = ["vendor-chunks/decimal.js-light"];
exports.modules = {

/***/ "(ssr)/../../node_modules/decimal.js-light/decimal.mjs":
/*!*******************************************************!*\
  !*** ../../node_modules/decimal.js-light/decimal.mjs ***!
  \*******************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Decimal: () => (/* binding */ Decimal),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/*\r\n *  decimal.js-light v2.5.1\r\n *  An arbitrary-precision Decimal type for JavaScript.\r\n *  https://github.com/MikeMcl/decimal.js-light\r\n *  Copyright (c) 2020 Michael Mclaughlin <<EMAIL>>\r\n *  MIT Expat Licence\r\n */ // ------------------------------------  EDITABLE DEFAULTS  ------------------------------------- //\n// The limit on the value of `precision`, and on the value of the first argument to\n// `toDecimalPlaces`, `toExponential`, `toFixed`, `toPrecision` and `toSignificantDigits`.\nvar MAX_DIGITS = 1e9, // The initial configuration properties of the Decimal constructor.\ndefaults = {\n    // These values must be integers within the stated ranges (inclusive).\n    // Most of these values can be changed during run-time using `Decimal.config`.\n    // The maximum number of significant digits of the result of a calculation or base conversion.\n    // E.g. `Decimal.config({ precision: 20 });`\n    precision: 20,\n    // The rounding mode used by default by `toInteger`, `toDecimalPlaces`, `toExponential`,\n    // `toFixed`, `toPrecision` and `toSignificantDigits`.\n    //\n    // ROUND_UP         0 Away from zero.\n    // ROUND_DOWN       1 Towards zero.\n    // ROUND_CEIL       2 Towards +Infinity.\n    // ROUND_FLOOR      3 Towards -Infinity.\n    // ROUND_HALF_UP    4 Towards nearest neighbour. If equidistant, up.\n    // ROUND_HALF_DOWN  5 Towards nearest neighbour. If equidistant, down.\n    // ROUND_HALF_EVEN  6 Towards nearest neighbour. If equidistant, towards even neighbour.\n    // ROUND_HALF_CEIL  7 Towards nearest neighbour. If equidistant, towards +Infinity.\n    // ROUND_HALF_FLOOR 8 Towards nearest neighbour. If equidistant, towards -Infinity.\n    //\n    // E.g.\n    // `Decimal.rounding = 4;`\n    // `Decimal.rounding = Decimal.ROUND_HALF_UP;`\n    rounding: 4,\n    // The exponent value at and beneath which `toString` returns exponential notation.\n    // JavaScript numbers: -7\n    toExpNeg: -7,\n    // The exponent value at and above which `toString` returns exponential notation.\n    // JavaScript numbers: 21\n    toExpPos: 21,\n    // The natural logarithm of 10.\n    // 115 digits\n    LN10: \"2.302585092994045684017991454684364207601101488628772976033327900967572609677352480235997205089598298341967784042286\"\n}, // ------------------------------------ END OF EDITABLE DEFAULTS -------------------------------- //\nDecimal, external = true, decimalError = \"[DecimalError] \", invalidArgument = decimalError + \"Invalid argument: \", exponentOutOfRange = decimalError + \"Exponent out of range: \", mathfloor = Math.floor, mathpow = Math.pow, isDecimal = /^(\\d+(\\.\\d*)?|\\.\\d+)(e[+-]?\\d+)?$/i, ONE, BASE = 1e7, LOG_BASE = 7, MAX_SAFE_INTEGER = 9007199254740991, MAX_E = mathfloor(MAX_SAFE_INTEGER / LOG_BASE), // Decimal.prototype object\nP = {};\n// Decimal prototype methods\n/*\r\n *  absoluteValue                       abs\r\n *  comparedTo                          cmp\r\n *  decimalPlaces                       dp\r\n *  dividedBy                           div\r\n *  dividedToIntegerBy                  idiv\r\n *  equals                              eq\r\n *  exponent\r\n *  greaterThan                         gt\r\n *  greaterThanOrEqualTo                gte\r\n *  isInteger                           isint\r\n *  isNegative                          isneg\r\n *  isPositive                          ispos\r\n *  isZero\r\n *  lessThan                            lt\r\n *  lessThanOrEqualTo                   lte\r\n *  logarithm                           log\r\n *  minus                               sub\r\n *  modulo                              mod\r\n *  naturalExponential                  exp\r\n *  naturalLogarithm                    ln\r\n *  negated                             neg\r\n *  plus                                add\r\n *  precision                           sd\r\n *  squareRoot                          sqrt\r\n *  times                               mul\r\n *  toDecimalPlaces                     todp\r\n *  toExponential\r\n *  toFixed\r\n *  toInteger                           toint\r\n *  toNumber\r\n *  toPower                             pow\r\n *  toPrecision\r\n *  toSignificantDigits                 tosd\r\n *  toString\r\n *  valueOf                             val\r\n */ /*\r\n * Return a new Decimal whose value is the absolute value of this Decimal.\r\n *\r\n */ P.absoluteValue = P.abs = function() {\n    var x = new this.constructor(this);\n    if (x.s) x.s = 1;\n    return x;\n};\n/*\r\n * Return\r\n *   1    if the value of this Decimal is greater than the value of `y`,\r\n *  -1    if the value of this Decimal is less than the value of `y`,\r\n *   0    if they have the same value\r\n *\r\n */ P.comparedTo = P.cmp = function(y) {\n    var i, j, xdL, ydL, x = this;\n    y = new x.constructor(y);\n    // Signs differ?\n    if (x.s !== y.s) return x.s || -y.s;\n    // Compare exponents.\n    if (x.e !== y.e) return x.e > y.e ^ x.s < 0 ? 1 : -1;\n    xdL = x.d.length;\n    ydL = y.d.length;\n    // Compare digit by digit.\n    for(i = 0, j = xdL < ydL ? xdL : ydL; i < j; ++i){\n        if (x.d[i] !== y.d[i]) return x.d[i] > y.d[i] ^ x.s < 0 ? 1 : -1;\n    }\n    // Compare lengths.\n    return xdL === ydL ? 0 : xdL > ydL ^ x.s < 0 ? 1 : -1;\n};\n/*\r\n * Return the number of decimal places of the value of this Decimal.\r\n *\r\n */ P.decimalPlaces = P.dp = function() {\n    var x = this, w = x.d.length - 1, dp = (w - x.e) * LOG_BASE;\n    // Subtract the number of trailing zeros of the last word.\n    w = x.d[w];\n    if (w) for(; w % 10 == 0; w /= 10)dp--;\n    return dp < 0 ? 0 : dp;\n};\n/*\r\n * Return a new Decimal whose value is the value of this Decimal divided by `y`, truncated to\r\n * `precision` significant digits.\r\n *\r\n */ P.dividedBy = P.div = function(y) {\n    return divide(this, new this.constructor(y));\n};\n/*\r\n * Return a new Decimal whose value is the integer part of dividing the value of this Decimal\r\n * by the value of `y`, truncated to `precision` significant digits.\r\n *\r\n */ P.dividedToIntegerBy = P.idiv = function(y) {\n    var x = this, Ctor = x.constructor;\n    return round(divide(x, new Ctor(y), 0, 1), Ctor.precision);\n};\n/*\r\n * Return true if the value of this Decimal is equal to the value of `y`, otherwise return false.\r\n *\r\n */ P.equals = P.eq = function(y) {\n    return !this.cmp(y);\n};\n/*\r\n * Return the (base 10) exponent value of this Decimal (this.e is the base 10000000 exponent).\r\n *\r\n */ P.exponent = function() {\n    return getBase10Exponent(this);\n};\n/*\r\n * Return true if the value of this Decimal is greater than the value of `y`, otherwise return\r\n * false.\r\n *\r\n */ P.greaterThan = P.gt = function(y) {\n    return this.cmp(y) > 0;\n};\n/*\r\n * Return true if the value of this Decimal is greater than or equal to the value of `y`,\r\n * otherwise return false.\r\n *\r\n */ P.greaterThanOrEqualTo = P.gte = function(y) {\n    return this.cmp(y) >= 0;\n};\n/*\r\n * Return true if the value of this Decimal is an integer, otherwise return false.\r\n *\r\n */ P.isInteger = P.isint = function() {\n    return this.e > this.d.length - 2;\n};\n/*\r\n * Return true if the value of this Decimal is negative, otherwise return false.\r\n *\r\n */ P.isNegative = P.isneg = function() {\n    return this.s < 0;\n};\n/*\r\n * Return true if the value of this Decimal is positive, otherwise return false.\r\n *\r\n */ P.isPositive = P.ispos = function() {\n    return this.s > 0;\n};\n/*\r\n * Return true if the value of this Decimal is 0, otherwise return false.\r\n *\r\n */ P.isZero = function() {\n    return this.s === 0;\n};\n/*\r\n * Return true if the value of this Decimal is less than `y`, otherwise return false.\r\n *\r\n */ P.lessThan = P.lt = function(y) {\n    return this.cmp(y) < 0;\n};\n/*\r\n * Return true if the value of this Decimal is less than or equal to `y`, otherwise return false.\r\n *\r\n */ P.lessThanOrEqualTo = P.lte = function(y) {\n    return this.cmp(y) < 1;\n};\n/*\r\n * Return the logarithm of the value of this Decimal to the specified base, truncated to\r\n * `precision` significant digits.\r\n *\r\n * If no base is specified, return log[10](x).\r\n *\r\n * log[base](x) = ln(x) / ln(base)\r\n *\r\n * The maximum error of the result is 1 ulp (unit in the last place).\r\n *\r\n * [base] {number|string|Decimal} The base of the logarithm.\r\n *\r\n */ P.logarithm = P.log = function(base) {\n    var r, x = this, Ctor = x.constructor, pr = Ctor.precision, wpr = pr + 5;\n    // Default base is 10.\n    if (base === void 0) {\n        base = new Ctor(10);\n    } else {\n        base = new Ctor(base);\n        // log[-b](x) = NaN\n        // log[0](x)  = NaN\n        // log[1](x)  = NaN\n        if (base.s < 1 || base.eq(ONE)) throw Error(decimalError + \"NaN\");\n    }\n    // log[b](-x) = NaN\n    // log[b](0) = -Infinity\n    if (x.s < 1) throw Error(decimalError + (x.s ? \"NaN\" : \"-Infinity\"));\n    // log[b](1) = 0\n    if (x.eq(ONE)) return new Ctor(0);\n    external = false;\n    r = divide(ln(x, wpr), ln(base, wpr), wpr);\n    external = true;\n    return round(r, pr);\n};\n/*\r\n * Return a new Decimal whose value is the value of this Decimal minus `y`, truncated to\r\n * `precision` significant digits.\r\n *\r\n */ P.minus = P.sub = function(y) {\n    var x = this;\n    y = new x.constructor(y);\n    return x.s == y.s ? subtract(x, y) : add(x, (y.s = -y.s, y));\n};\n/*\r\n * Return a new Decimal whose value is the value of this Decimal modulo `y`, truncated to\r\n * `precision` significant digits.\r\n *\r\n */ P.modulo = P.mod = function(y) {\n    var q, x = this, Ctor = x.constructor, pr = Ctor.precision;\n    y = new Ctor(y);\n    // x % 0 = NaN\n    if (!y.s) throw Error(decimalError + \"NaN\");\n    // Return x if x is 0.\n    if (!x.s) return round(new Ctor(x), pr);\n    // Prevent rounding of intermediate calculations.\n    external = false;\n    q = divide(x, y, 0, 1).times(y);\n    external = true;\n    return x.minus(q);\n};\n/*\r\n * Return a new Decimal whose value is the natural exponential of the value of this Decimal,\r\n * i.e. the base e raised to the power the value of this Decimal, truncated to `precision`\r\n * significant digits.\r\n *\r\n */ P.naturalExponential = P.exp = function() {\n    return exp(this);\n};\n/*\r\n * Return a new Decimal whose value is the natural logarithm of the value of this Decimal,\r\n * truncated to `precision` significant digits.\r\n *\r\n */ P.naturalLogarithm = P.ln = function() {\n    return ln(this);\n};\n/*\r\n * Return a new Decimal whose value is the value of this Decimal negated, i.e. as if multiplied by\r\n * -1.\r\n *\r\n */ P.negated = P.neg = function() {\n    var x = new this.constructor(this);\n    x.s = -x.s || 0;\n    return x;\n};\n/*\r\n * Return a new Decimal whose value is the value of this Decimal plus `y`, truncated to\r\n * `precision` significant digits.\r\n *\r\n */ P.plus = P.add = function(y) {\n    var x = this;\n    y = new x.constructor(y);\n    return x.s == y.s ? add(x, y) : subtract(x, (y.s = -y.s, y));\n};\n/*\r\n * Return the number of significant digits of the value of this Decimal.\r\n *\r\n * [z] {boolean|number} Whether to count integer-part trailing zeros: true, false, 1 or 0.\r\n *\r\n */ P.precision = P.sd = function(z) {\n    var e, sd, w, x = this;\n    if (z !== void 0 && z !== !!z && z !== 1 && z !== 0) throw Error(invalidArgument + z);\n    e = getBase10Exponent(x) + 1;\n    w = x.d.length - 1;\n    sd = w * LOG_BASE + 1;\n    w = x.d[w];\n    // If non-zero...\n    if (w) {\n        // Subtract the number of trailing zeros of the last word.\n        for(; w % 10 == 0; w /= 10)sd--;\n        // Add the number of digits of the first word.\n        for(w = x.d[0]; w >= 10; w /= 10)sd++;\n    }\n    return z && e > sd ? e : sd;\n};\n/*\r\n * Return a new Decimal whose value is the square root of this Decimal, truncated to `precision`\r\n * significant digits.\r\n *\r\n */ P.squareRoot = P.sqrt = function() {\n    var e, n, pr, r, s, t, wpr, x = this, Ctor = x.constructor;\n    // Negative or zero?\n    if (x.s < 1) {\n        if (!x.s) return new Ctor(0);\n        // sqrt(-x) = NaN\n        throw Error(decimalError + \"NaN\");\n    }\n    e = getBase10Exponent(x);\n    external = false;\n    // Initial estimate.\n    s = Math.sqrt(+x);\n    // Math.sqrt underflow/overflow?\n    // Pass x to Math.sqrt as integer, then adjust the exponent of the result.\n    if (s == 0 || s == 1 / 0) {\n        n = digitsToString(x.d);\n        if ((n.length + e) % 2 == 0) n += \"0\";\n        s = Math.sqrt(n);\n        e = mathfloor((e + 1) / 2) - (e < 0 || e % 2);\n        if (s == 1 / 0) {\n            n = \"5e\" + e;\n        } else {\n            n = s.toExponential();\n            n = n.slice(0, n.indexOf(\"e\") + 1) + e;\n        }\n        r = new Ctor(n);\n    } else {\n        r = new Ctor(s.toString());\n    }\n    pr = Ctor.precision;\n    s = wpr = pr + 3;\n    // Newton-Raphson iteration.\n    for(;;){\n        t = r;\n        r = t.plus(divide(x, t, wpr + 2)).times(0.5);\n        if (digitsToString(t.d).slice(0, wpr) === (n = digitsToString(r.d)).slice(0, wpr)) {\n            n = n.slice(wpr - 3, wpr + 1);\n            // The 4th rounding digit may be in error by -1 so if the 4 rounding digits are 9999 or\n            // 4999, i.e. approaching a rounding boundary, continue the iteration.\n            if (s == wpr && n == \"4999\") {\n                // On the first iteration only, check to see if rounding up gives the exact result as the\n                // nines may infinitely repeat.\n                round(t, pr + 1, 0);\n                if (t.times(t).eq(x)) {\n                    r = t;\n                    break;\n                }\n            } else if (n != \"9999\") {\n                break;\n            }\n            wpr += 4;\n        }\n    }\n    external = true;\n    return round(r, pr);\n};\n/*\r\n * Return a new Decimal whose value is the value of this Decimal times `y`, truncated to\r\n * `precision` significant digits.\r\n *\r\n */ P.times = P.mul = function(y) {\n    var carry, e, i, k, r, rL, t, xdL, ydL, x = this, Ctor = x.constructor, xd = x.d, yd = (y = new Ctor(y)).d;\n    // Return 0 if either is 0.\n    if (!x.s || !y.s) return new Ctor(0);\n    y.s *= x.s;\n    e = x.e + y.e;\n    xdL = xd.length;\n    ydL = yd.length;\n    // Ensure xd points to the longer array.\n    if (xdL < ydL) {\n        r = xd;\n        xd = yd;\n        yd = r;\n        rL = xdL;\n        xdL = ydL;\n        ydL = rL;\n    }\n    // Initialise the result array with zeros.\n    r = [];\n    rL = xdL + ydL;\n    for(i = rL; i--;)r.push(0);\n    // Multiply!\n    for(i = ydL; --i >= 0;){\n        carry = 0;\n        for(k = xdL + i; k > i;){\n            t = r[k] + yd[i] * xd[k - i - 1] + carry;\n            r[k--] = t % BASE | 0;\n            carry = t / BASE | 0;\n        }\n        r[k] = (r[k] + carry) % BASE | 0;\n    }\n    // Remove trailing zeros.\n    for(; !r[--rL];)r.pop();\n    if (carry) ++e;\n    else r.shift();\n    y.d = r;\n    y.e = e;\n    return external ? round(y, Ctor.precision) : y;\n};\n/*\r\n * Return a new Decimal whose value is the value of this Decimal rounded to a maximum of `dp`\r\n * decimal places using rounding mode `rm` or `rounding` if `rm` is omitted.\r\n *\r\n * If `dp` is omitted, return a new Decimal whose value is the value of this Decimal.\r\n *\r\n * [dp] {number} Decimal places. Integer, 0 to MAX_DIGITS inclusive.\r\n * [rm] {number} Rounding mode. Integer, 0 to 8 inclusive.\r\n *\r\n */ P.toDecimalPlaces = P.todp = function(dp, rm) {\n    var x = this, Ctor = x.constructor;\n    x = new Ctor(x);\n    if (dp === void 0) return x;\n    checkInt32(dp, 0, MAX_DIGITS);\n    if (rm === void 0) rm = Ctor.rounding;\n    else checkInt32(rm, 0, 8);\n    return round(x, dp + getBase10Exponent(x) + 1, rm);\n};\n/*\r\n * Return a string representing the value of this Decimal in exponential notation rounded to\r\n * `dp` fixed decimal places using rounding mode `rounding`.\r\n *\r\n * [dp] {number} Decimal places. Integer, 0 to MAX_DIGITS inclusive.\r\n * [rm] {number} Rounding mode. Integer, 0 to 8 inclusive.\r\n *\r\n */ P.toExponential = function(dp, rm) {\n    var str, x = this, Ctor = x.constructor;\n    if (dp === void 0) {\n        str = toString(x, true);\n    } else {\n        checkInt32(dp, 0, MAX_DIGITS);\n        if (rm === void 0) rm = Ctor.rounding;\n        else checkInt32(rm, 0, 8);\n        x = round(new Ctor(x), dp + 1, rm);\n        str = toString(x, true, dp + 1);\n    }\n    return str;\n};\n/*\r\n * Return a string representing the value of this Decimal in normal (fixed-point) notation to\r\n * `dp` fixed decimal places and rounded using rounding mode `rm` or `rounding` if `rm` is\r\n * omitted.\r\n *\r\n * As with JavaScript numbers, (-0).toFixed(0) is '0', but e.g. (-0.00001).toFixed(0) is '-0'.\r\n *\r\n * [dp] {number} Decimal places. Integer, 0 to MAX_DIGITS inclusive.\r\n * [rm] {number} Rounding mode. Integer, 0 to 8 inclusive.\r\n *\r\n * (-0).toFixed(0) is '0', but (-0.1).toFixed(0) is '-0'.\r\n * (-0).toFixed(1) is '0.0', but (-0.01).toFixed(1) is '-0.0'.\r\n * (-0).toFixed(3) is '0.000'.\r\n * (-0.5).toFixed(0) is '-0'.\r\n *\r\n */ P.toFixed = function(dp, rm) {\n    var str, y, x = this, Ctor = x.constructor;\n    if (dp === void 0) return toString(x);\n    checkInt32(dp, 0, MAX_DIGITS);\n    if (rm === void 0) rm = Ctor.rounding;\n    else checkInt32(rm, 0, 8);\n    y = round(new Ctor(x), dp + getBase10Exponent(x) + 1, rm);\n    str = toString(y.abs(), false, dp + getBase10Exponent(y) + 1);\n    // To determine whether to add the minus sign look at the value before it was rounded,\n    // i.e. look at `x` rather than `y`.\n    return x.isneg() && !x.isZero() ? \"-\" + str : str;\n};\n/*\r\n * Return a new Decimal whose value is the value of this Decimal rounded to a whole number using\r\n * rounding mode `rounding`.\r\n *\r\n */ P.toInteger = P.toint = function() {\n    var x = this, Ctor = x.constructor;\n    return round(new Ctor(x), getBase10Exponent(x) + 1, Ctor.rounding);\n};\n/*\r\n * Return the value of this Decimal converted to a number primitive.\r\n *\r\n */ P.toNumber = function() {\n    return +this;\n};\n/*\r\n * Return a new Decimal whose value is the value of this Decimal raised to the power `y`,\r\n * truncated to `precision` significant digits.\r\n *\r\n * For non-integer or very large exponents pow(x, y) is calculated using\r\n *\r\n *   x^y = exp(y*ln(x))\r\n *\r\n * The maximum error is 1 ulp (unit in last place).\r\n *\r\n * y {number|string|Decimal} The power to which to raise this Decimal.\r\n *\r\n */ P.toPower = P.pow = function(y) {\n    var e, k, pr, r, sign, yIsInt, x = this, Ctor = x.constructor, guard = 12, yn = +(y = new Ctor(y));\n    // pow(x, 0) = 1\n    if (!y.s) return new Ctor(ONE);\n    x = new Ctor(x);\n    // pow(0, y > 0) = 0\n    // pow(0, y < 0) = Infinity\n    if (!x.s) {\n        if (y.s < 1) throw Error(decimalError + \"Infinity\");\n        return x;\n    }\n    // pow(1, y) = 1\n    if (x.eq(ONE)) return x;\n    pr = Ctor.precision;\n    // pow(x, 1) = x\n    if (y.eq(ONE)) return round(x, pr);\n    e = y.e;\n    k = y.d.length - 1;\n    yIsInt = e >= k;\n    sign = x.s;\n    if (!yIsInt) {\n        // pow(x < 0, y non-integer) = NaN\n        if (sign < 0) throw Error(decimalError + \"NaN\");\n    // If y is a small integer use the 'exponentiation by squaring' algorithm.\n    } else if ((k = yn < 0 ? -yn : yn) <= MAX_SAFE_INTEGER) {\n        r = new Ctor(ONE);\n        // Max k of 9007199254740991 takes 53 loop iterations.\n        // Maximum digits array length; leaves [28, 34] guard digits.\n        e = Math.ceil(pr / LOG_BASE + 4);\n        external = false;\n        for(;;){\n            if (k % 2) {\n                r = r.times(x);\n                truncate(r.d, e);\n            }\n            k = mathfloor(k / 2);\n            if (k === 0) break;\n            x = x.times(x);\n            truncate(x.d, e);\n        }\n        external = true;\n        return y.s < 0 ? new Ctor(ONE).div(r) : round(r, pr);\n    }\n    // Result is negative if x is negative and the last digit of integer y is odd.\n    sign = sign < 0 && y.d[Math.max(e, k)] & 1 ? -1 : 1;\n    x.s = 1;\n    external = false;\n    r = y.times(ln(x, pr + guard));\n    external = true;\n    r = exp(r);\n    r.s = sign;\n    return r;\n};\n/*\r\n * Return a string representing the value of this Decimal rounded to `sd` significant digits\r\n * using rounding mode `rounding`.\r\n *\r\n * Return exponential notation if `sd` is less than the number of digits necessary to represent\r\n * the integer part of the value in normal notation.\r\n *\r\n * [sd] {number} Significant digits. Integer, 1 to MAX_DIGITS inclusive.\r\n * [rm] {number} Rounding mode. Integer, 0 to 8 inclusive.\r\n *\r\n */ P.toPrecision = function(sd, rm) {\n    var e, str, x = this, Ctor = x.constructor;\n    if (sd === void 0) {\n        e = getBase10Exponent(x);\n        str = toString(x, e <= Ctor.toExpNeg || e >= Ctor.toExpPos);\n    } else {\n        checkInt32(sd, 1, MAX_DIGITS);\n        if (rm === void 0) rm = Ctor.rounding;\n        else checkInt32(rm, 0, 8);\n        x = round(new Ctor(x), sd, rm);\n        e = getBase10Exponent(x);\n        str = toString(x, sd <= e || e <= Ctor.toExpNeg, sd);\n    }\n    return str;\n};\n/*\r\n * Return a new Decimal whose value is the value of this Decimal rounded to a maximum of `sd`\r\n * significant digits using rounding mode `rm`, or to `precision` and `rounding` respectively if\r\n * omitted.\r\n *\r\n * [sd] {number} Significant digits. Integer, 1 to MAX_DIGITS inclusive.\r\n * [rm] {number} Rounding mode. Integer, 0 to 8 inclusive.\r\n *\r\n */ P.toSignificantDigits = P.tosd = function(sd, rm) {\n    var x = this, Ctor = x.constructor;\n    if (sd === void 0) {\n        sd = Ctor.precision;\n        rm = Ctor.rounding;\n    } else {\n        checkInt32(sd, 1, MAX_DIGITS);\n        if (rm === void 0) rm = Ctor.rounding;\n        else checkInt32(rm, 0, 8);\n    }\n    return round(new Ctor(x), sd, rm);\n};\n/*\r\n * Return a string representing the value of this Decimal.\r\n *\r\n * Return exponential notation if this Decimal has a positive exponent equal to or greater than\r\n * `toExpPos`, or a negative exponent equal to or less than `toExpNeg`.\r\n *\r\n */ P.toString = P.valueOf = P.val = P.toJSON = P[Symbol.for(\"nodejs.util.inspect.custom\")] = function() {\n    var x = this, e = getBase10Exponent(x), Ctor = x.constructor;\n    return toString(x, e <= Ctor.toExpNeg || e >= Ctor.toExpPos);\n};\n// Helper functions for Decimal.prototype (P) and/or Decimal methods, and their callers.\n/*\r\n *  add                 P.minus, P.plus\r\n *  checkInt32          P.todp, P.toExponential, P.toFixed, P.toPrecision, P.tosd\r\n *  digitsToString      P.log, P.sqrt, P.pow, toString, exp, ln\r\n *  divide              P.div, P.idiv, P.log, P.mod, P.sqrt, exp, ln\r\n *  exp                 P.exp, P.pow\r\n *  getBase10Exponent   P.exponent, P.sd, P.toint, P.sqrt, P.todp, P.toFixed, P.toPrecision,\r\n *                      P.toString, divide, round, toString, exp, ln\r\n *  getLn10             P.log, ln\r\n *  getZeroString       digitsToString, toString\r\n *  ln                  P.log, P.ln, P.pow, exp\r\n *  parseDecimal        Decimal\r\n *  round               P.abs, P.idiv, P.log, P.minus, P.mod, P.neg, P.plus, P.toint, P.sqrt,\r\n *                      P.times, P.todp, P.toExponential, P.toFixed, P.pow, P.toPrecision, P.tosd,\r\n *                      divide, getLn10, exp, ln\r\n *  subtract            P.minus, P.plus\r\n *  toString            P.toExponential, P.toFixed, P.toPrecision, P.toString, P.valueOf\r\n *  truncate            P.pow\r\n *\r\n *  Throws:             P.log, P.mod, P.sd, P.sqrt, P.pow,  checkInt32, divide, round,\r\n *                      getLn10, exp, ln, parseDecimal, Decimal, config\r\n */ function add(x, y) {\n    var carry, d, e, i, k, len, xd, yd, Ctor = x.constructor, pr = Ctor.precision;\n    // If either is zero...\n    if (!x.s || !y.s) {\n        // Return x if y is zero.\n        // Return y if y is non-zero.\n        if (!y.s) y = new Ctor(x);\n        return external ? round(y, pr) : y;\n    }\n    xd = x.d;\n    yd = y.d;\n    // x and y are finite, non-zero numbers with the same sign.\n    k = x.e;\n    e = y.e;\n    xd = xd.slice();\n    i = k - e;\n    // If base 1e7 exponents differ...\n    if (i) {\n        if (i < 0) {\n            d = xd;\n            i = -i;\n            len = yd.length;\n        } else {\n            d = yd;\n            e = k;\n            len = xd.length;\n        }\n        // Limit number of zeros prepended to max(ceil(pr / LOG_BASE), len) + 1.\n        k = Math.ceil(pr / LOG_BASE);\n        len = k > len ? k + 1 : len + 1;\n        if (i > len) {\n            i = len;\n            d.length = 1;\n        }\n        // Prepend zeros to equalise exponents. Note: Faster to use reverse then do unshifts.\n        d.reverse();\n        for(; i--;)d.push(0);\n        d.reverse();\n    }\n    len = xd.length;\n    i = yd.length;\n    // If yd is longer than xd, swap xd and yd so xd points to the longer array.\n    if (len - i < 0) {\n        i = len;\n        d = yd;\n        yd = xd;\n        xd = d;\n    }\n    // Only start adding at yd.length - 1 as the further digits of xd can be left as they are.\n    for(carry = 0; i;){\n        carry = (xd[--i] = xd[i] + yd[i] + carry) / BASE | 0;\n        xd[i] %= BASE;\n    }\n    if (carry) {\n        xd.unshift(carry);\n        ++e;\n    }\n    // Remove trailing zeros.\n    // No need to check for zero, as +x + +y != 0 && -x + -y != 0\n    for(len = xd.length; xd[--len] == 0;)xd.pop();\n    y.d = xd;\n    y.e = e;\n    return external ? round(y, pr) : y;\n}\nfunction checkInt32(i, min, max) {\n    if (i !== ~~i || i < min || i > max) {\n        throw Error(invalidArgument + i);\n    }\n}\nfunction digitsToString(d) {\n    var i, k, ws, indexOfLastWord = d.length - 1, str = \"\", w = d[0];\n    if (indexOfLastWord > 0) {\n        str += w;\n        for(i = 1; i < indexOfLastWord; i++){\n            ws = d[i] + \"\";\n            k = LOG_BASE - ws.length;\n            if (k) str += getZeroString(k);\n            str += ws;\n        }\n        w = d[i];\n        ws = w + \"\";\n        k = LOG_BASE - ws.length;\n        if (k) str += getZeroString(k);\n    } else if (w === 0) {\n        return \"0\";\n    }\n    // Remove trailing zeros of last w.\n    for(; w % 10 === 0;)w /= 10;\n    return str + w;\n}\nvar divide = function() {\n    // Assumes non-zero x and k, and hence non-zero result.\n    function multiplyInteger(x, k) {\n        var temp, carry = 0, i = x.length;\n        for(x = x.slice(); i--;){\n            temp = x[i] * k + carry;\n            x[i] = temp % BASE | 0;\n            carry = temp / BASE | 0;\n        }\n        if (carry) x.unshift(carry);\n        return x;\n    }\n    function compare(a, b, aL, bL) {\n        var i, r;\n        if (aL != bL) {\n            r = aL > bL ? 1 : -1;\n        } else {\n            for(i = r = 0; i < aL; i++){\n                if (a[i] != b[i]) {\n                    r = a[i] > b[i] ? 1 : -1;\n                    break;\n                }\n            }\n        }\n        return r;\n    }\n    function subtract(a, b, aL) {\n        var i = 0;\n        // Subtract b from a.\n        for(; aL--;){\n            a[aL] -= i;\n            i = a[aL] < b[aL] ? 1 : 0;\n            a[aL] = i * BASE + a[aL] - b[aL];\n        }\n        // Remove leading zeros.\n        for(; !a[0] && a.length > 1;)a.shift();\n    }\n    return function(x, y, pr, dp) {\n        var cmp, e, i, k, prod, prodL, q, qd, rem, remL, rem0, sd, t, xi, xL, yd0, yL, yz, Ctor = x.constructor, sign = x.s == y.s ? 1 : -1, xd = x.d, yd = y.d;\n        // Either 0?\n        if (!x.s) return new Ctor(x);\n        if (!y.s) throw Error(decimalError + \"Division by zero\");\n        e = x.e - y.e;\n        yL = yd.length;\n        xL = xd.length;\n        q = new Ctor(sign);\n        qd = q.d = [];\n        // Result exponent may be one less than e.\n        for(i = 0; yd[i] == (xd[i] || 0);)++i;\n        if (yd[i] > (xd[i] || 0)) --e;\n        if (pr == null) {\n            sd = pr = Ctor.precision;\n        } else if (dp) {\n            sd = pr + (getBase10Exponent(x) - getBase10Exponent(y)) + 1;\n        } else {\n            sd = pr;\n        }\n        if (sd < 0) return new Ctor(0);\n        // Convert precision in number of base 10 digits to base 1e7 digits.\n        sd = sd / LOG_BASE + 2 | 0;\n        i = 0;\n        // divisor < 1e7\n        if (yL == 1) {\n            k = 0;\n            yd = yd[0];\n            sd++;\n            // k is the carry.\n            for(; (i < xL || k) && sd--; i++){\n                t = k * BASE + (xd[i] || 0);\n                qd[i] = t / yd | 0;\n                k = t % yd | 0;\n            }\n        // divisor >= 1e7\n        } else {\n            // Normalise xd and yd so highest order digit of yd is >= BASE/2\n            k = BASE / (yd[0] + 1) | 0;\n            if (k > 1) {\n                yd = multiplyInteger(yd, k);\n                xd = multiplyInteger(xd, k);\n                yL = yd.length;\n                xL = xd.length;\n            }\n            xi = yL;\n            rem = xd.slice(0, yL);\n            remL = rem.length;\n            // Add zeros to make remainder as long as divisor.\n            for(; remL < yL;)rem[remL++] = 0;\n            yz = yd.slice();\n            yz.unshift(0);\n            yd0 = yd[0];\n            if (yd[1] >= BASE / 2) ++yd0;\n            do {\n                k = 0;\n                // Compare divisor and remainder.\n                cmp = compare(yd, rem, yL, remL);\n                // If divisor < remainder.\n                if (cmp < 0) {\n                    // Calculate trial digit, k.\n                    rem0 = rem[0];\n                    if (yL != remL) rem0 = rem0 * BASE + (rem[1] || 0);\n                    // k will be how many times the divisor goes into the current remainder.\n                    k = rem0 / yd0 | 0;\n                    //  Algorithm:\n                    //  1. product = divisor * trial digit (k)\n                    //  2. if product > remainder: product -= divisor, k--\n                    //  3. remainder -= product\n                    //  4. if product was < remainder at 2:\n                    //    5. compare new remainder and divisor\n                    //    6. If remainder > divisor: remainder -= divisor, k++\n                    if (k > 1) {\n                        if (k >= BASE) k = BASE - 1;\n                        // product = divisor * trial digit.\n                        prod = multiplyInteger(yd, k);\n                        prodL = prod.length;\n                        remL = rem.length;\n                        // Compare product and remainder.\n                        cmp = compare(prod, rem, prodL, remL);\n                        // product > remainder.\n                        if (cmp == 1) {\n                            k--;\n                            // Subtract divisor from product.\n                            subtract(prod, yL < prodL ? yz : yd, prodL);\n                        }\n                    } else {\n                        // cmp is -1.\n                        // If k is 0, there is no need to compare yd and rem again below, so change cmp to 1\n                        // to avoid it. If k is 1 there is a need to compare yd and rem again below.\n                        if (k == 0) cmp = k = 1;\n                        prod = yd.slice();\n                    }\n                    prodL = prod.length;\n                    if (prodL < remL) prod.unshift(0);\n                    // Subtract product from remainder.\n                    subtract(rem, prod, remL);\n                    // If product was < previous remainder.\n                    if (cmp == -1) {\n                        remL = rem.length;\n                        // Compare divisor and new remainder.\n                        cmp = compare(yd, rem, yL, remL);\n                        // If divisor < new remainder, subtract divisor from remainder.\n                        if (cmp < 1) {\n                            k++;\n                            // Subtract divisor from remainder.\n                            subtract(rem, yL < remL ? yz : yd, remL);\n                        }\n                    }\n                    remL = rem.length;\n                } else if (cmp === 0) {\n                    k++;\n                    rem = [\n                        0\n                    ];\n                } // if cmp === 1, k will be 0\n                // Add the next digit, k, to the result array.\n                qd[i++] = k;\n                // Update the remainder.\n                if (cmp && rem[0]) {\n                    rem[remL++] = xd[xi] || 0;\n                } else {\n                    rem = [\n                        xd[xi]\n                    ];\n                    remL = 1;\n                }\n            }while ((xi++ < xL || rem[0] !== void 0) && sd--);\n        }\n        // Leading zero?\n        if (!qd[0]) qd.shift();\n        q.e = e;\n        return round(q, dp ? pr + getBase10Exponent(q) + 1 : pr);\n    };\n}();\n/*\r\n * Return a new Decimal whose value is the natural exponential of `x` truncated to `sd`\r\n * significant digits.\r\n *\r\n * Taylor/Maclaurin series.\r\n *\r\n * exp(x) = x^0/0! + x^1/1! + x^2/2! + x^3/3! + ...\r\n *\r\n * Argument reduction:\r\n *   Repeat x = x / 32, k += 5, until |x| < 0.1\r\n *   exp(x) = exp(x / 2^k)^(2^k)\r\n *\r\n * Previously, the argument was initially reduced by\r\n * exp(x) = exp(r) * 10^k  where r = x - k * ln10, k = floor(x / ln10)\r\n * to first put r in the range [0, ln10], before dividing by 32 until |x| < 0.1, but this was\r\n * found to be slower than just dividing repeatedly by 32 as above.\r\n *\r\n * (Math object integer min/max: Math.exp(709) = 8.2e+307, Math.exp(-745) = 5e-324)\r\n *\r\n *  exp(x) is non-terminating for any finite, non-zero x.\r\n *\r\n */ function exp(x, sd) {\n    var denominator, guard, pow, sum, t, wpr, i = 0, k = 0, Ctor = x.constructor, pr = Ctor.precision;\n    if (getBase10Exponent(x) > 16) throw Error(exponentOutOfRange + getBase10Exponent(x));\n    // exp(0) = 1\n    if (!x.s) return new Ctor(ONE);\n    if (sd == null) {\n        external = false;\n        wpr = pr;\n    } else {\n        wpr = sd;\n    }\n    t = new Ctor(0.03125);\n    while(x.abs().gte(0.1)){\n        x = x.times(t); // x = x / 2^5\n        k += 5;\n    }\n    // Estimate the precision increase necessary to ensure the first 4 rounding digits are correct.\n    guard = Math.log(mathpow(2, k)) / Math.LN10 * 2 + 5 | 0;\n    wpr += guard;\n    denominator = pow = sum = new Ctor(ONE);\n    Ctor.precision = wpr;\n    for(;;){\n        pow = round(pow.times(x), wpr);\n        denominator = denominator.times(++i);\n        t = sum.plus(divide(pow, denominator, wpr));\n        if (digitsToString(t.d).slice(0, wpr) === digitsToString(sum.d).slice(0, wpr)) {\n            while(k--)sum = round(sum.times(sum), wpr);\n            Ctor.precision = pr;\n            return sd == null ? (external = true, round(sum, pr)) : sum;\n        }\n        sum = t;\n    }\n}\n// Calculate the base 10 exponent from the base 1e7 exponent.\nfunction getBase10Exponent(x) {\n    var e = x.e * LOG_BASE, w = x.d[0];\n    // Add the number of digits of the first word of the digits array.\n    for(; w >= 10; w /= 10)e++;\n    return e;\n}\nfunction getLn10(Ctor, sd, pr) {\n    if (sd > Ctor.LN10.sd()) {\n        // Reset global state in case the exception is caught.\n        external = true;\n        if (pr) Ctor.precision = pr;\n        throw Error(decimalError + \"LN10 precision limit exceeded\");\n    }\n    return round(new Ctor(Ctor.LN10), sd);\n}\nfunction getZeroString(k) {\n    var zs = \"\";\n    for(; k--;)zs += \"0\";\n    return zs;\n}\n/*\r\n * Return a new Decimal whose value is the natural logarithm of `x` truncated to `sd` significant\r\n * digits.\r\n *\r\n *  ln(n) is non-terminating (n != 1)\r\n *\r\n */ function ln(y, sd) {\n    var c, c0, denominator, e, numerator, sum, t, wpr, x2, n = 1, guard = 10, x = y, xd = x.d, Ctor = x.constructor, pr = Ctor.precision;\n    // ln(-x) = NaN\n    // ln(0) = -Infinity\n    if (x.s < 1) throw Error(decimalError + (x.s ? \"NaN\" : \"-Infinity\"));\n    // ln(1) = 0\n    if (x.eq(ONE)) return new Ctor(0);\n    if (sd == null) {\n        external = false;\n        wpr = pr;\n    } else {\n        wpr = sd;\n    }\n    if (x.eq(10)) {\n        if (sd == null) external = true;\n        return getLn10(Ctor, wpr);\n    }\n    wpr += guard;\n    Ctor.precision = wpr;\n    c = digitsToString(xd);\n    c0 = c.charAt(0);\n    e = getBase10Exponent(x);\n    if (Math.abs(e) < 1.5e15) {\n        // Argument reduction.\n        // The series converges faster the closer the argument is to 1, so using\n        // ln(a^b) = b * ln(a),   ln(a) = ln(a^b) / b\n        // multiply the argument by itself until the leading digits of the significand are 7, 8, 9,\n        // 10, 11, 12 or 13, recording the number of multiplications so the sum of the series can\n        // later be divided by this number, then separate out the power of 10 using\n        // ln(a*10^b) = ln(a) + b*ln(10).\n        // max n is 21 (gives 0.9, 1.0 or 1.1) (9e15 / 21 = 4.2e14).\n        //while (c0 < 9 && c0 != 1 || c0 == 1 && c.charAt(1) > 1) {\n        // max n is 6 (gives 0.7 - 1.3)\n        while(c0 < 7 && c0 != 1 || c0 == 1 && c.charAt(1) > 3){\n            x = x.times(y);\n            c = digitsToString(x.d);\n            c0 = c.charAt(0);\n            n++;\n        }\n        e = getBase10Exponent(x);\n        if (c0 > 1) {\n            x = new Ctor(\"0.\" + c);\n            e++;\n        } else {\n            x = new Ctor(c0 + \".\" + c.slice(1));\n        }\n    } else {\n        // The argument reduction method above may result in overflow if the argument y is a massive\n        // number with exponent >= 1500000000000000 (9e15 / 6 = 1.5e15), so instead recall this\n        // function using ln(x*10^e) = ln(x) + e*ln(10).\n        t = getLn10(Ctor, wpr + 2, pr).times(e + \"\");\n        x = ln(new Ctor(c0 + \".\" + c.slice(1)), wpr - guard).plus(t);\n        Ctor.precision = pr;\n        return sd == null ? (external = true, round(x, pr)) : x;\n    }\n    // x is reduced to a value near 1.\n    // Taylor series.\n    // ln(y) = ln((1 + x)/(1 - x)) = 2(x + x^3/3 + x^5/5 + x^7/7 + ...)\n    // where x = (y - 1)/(y + 1)    (|x| < 1)\n    sum = numerator = x = divide(x.minus(ONE), x.plus(ONE), wpr);\n    x2 = round(x.times(x), wpr);\n    denominator = 3;\n    for(;;){\n        numerator = round(numerator.times(x2), wpr);\n        t = sum.plus(divide(numerator, new Ctor(denominator), wpr));\n        if (digitsToString(t.d).slice(0, wpr) === digitsToString(sum.d).slice(0, wpr)) {\n            sum = sum.times(2);\n            // Reverse the argument reduction.\n            if (e !== 0) sum = sum.plus(getLn10(Ctor, wpr + 2, pr).times(e + \"\"));\n            sum = divide(sum, new Ctor(n), wpr);\n            Ctor.precision = pr;\n            return sd == null ? (external = true, round(sum, pr)) : sum;\n        }\n        sum = t;\n        denominator += 2;\n    }\n}\n/*\r\n * Parse the value of a new Decimal `x` from string `str`.\r\n */ function parseDecimal(x, str) {\n    var e, i, len;\n    // Decimal point?\n    if ((e = str.indexOf(\".\")) > -1) str = str.replace(\".\", \"\");\n    // Exponential form?\n    if ((i = str.search(/e/i)) > 0) {\n        // Determine exponent.\n        if (e < 0) e = i;\n        e += +str.slice(i + 1);\n        str = str.substring(0, i);\n    } else if (e < 0) {\n        // Integer.\n        e = str.length;\n    }\n    // Determine leading zeros.\n    for(i = 0; str.charCodeAt(i) === 48;)++i;\n    // Determine trailing zeros.\n    for(len = str.length; str.charCodeAt(len - 1) === 48;)--len;\n    str = str.slice(i, len);\n    if (str) {\n        len -= i;\n        e = e - i - 1;\n        x.e = mathfloor(e / LOG_BASE);\n        x.d = [];\n        // Transform base\n        // e is the base 10 exponent.\n        // i is where to slice str to get the first word of the digits array.\n        i = (e + 1) % LOG_BASE;\n        if (e < 0) i += LOG_BASE;\n        if (i < len) {\n            if (i) x.d.push(+str.slice(0, i));\n            for(len -= LOG_BASE; i < len;)x.d.push(+str.slice(i, i += LOG_BASE));\n            str = str.slice(i);\n            i = LOG_BASE - str.length;\n        } else {\n            i -= len;\n        }\n        for(; i--;)str += \"0\";\n        x.d.push(+str);\n        if (external && (x.e > MAX_E || x.e < -MAX_E)) throw Error(exponentOutOfRange + e);\n    } else {\n        // Zero.\n        x.s = 0;\n        x.e = 0;\n        x.d = [\n            0\n        ];\n    }\n    return x;\n}\n/*\r\n * Round `x` to `sd` significant digits, using rounding mode `rm` if present (truncate otherwise).\r\n */ function round(x, sd, rm) {\n    var i, j, k, n, rd, doRound, w, xdi, xd = x.d;\n    // rd: the rounding digit, i.e. the digit after the digit that may be rounded up.\n    // w: the word of xd which contains the rounding digit, a base 1e7 number.\n    // xdi: the index of w within xd.\n    // n: the number of digits of w.\n    // i: what would be the index of rd within w if all the numbers were 7 digits long (i.e. if\n    // they had leading zeros)\n    // j: if > 0, the actual index of rd within w (if < 0, rd is a leading zero).\n    // Get the length of the first word of the digits array xd.\n    for(n = 1, k = xd[0]; k >= 10; k /= 10)n++;\n    i = sd - n;\n    // Is the rounding digit in the first word of xd?\n    if (i < 0) {\n        i += LOG_BASE;\n        j = sd;\n        w = xd[xdi = 0];\n    } else {\n        xdi = Math.ceil((i + 1) / LOG_BASE);\n        k = xd.length;\n        if (xdi >= k) return x;\n        w = k = xd[xdi];\n        // Get the number of digits of w.\n        for(n = 1; k >= 10; k /= 10)n++;\n        // Get the index of rd within w.\n        i %= LOG_BASE;\n        // Get the index of rd within w, adjusted for leading zeros.\n        // The number of leading zeros of w is given by LOG_BASE - n.\n        j = i - LOG_BASE + n;\n    }\n    if (rm !== void 0) {\n        k = mathpow(10, n - j - 1);\n        // Get the rounding digit at index j of w.\n        rd = w / k % 10 | 0;\n        // Are there any non-zero digits after the rounding digit?\n        doRound = sd < 0 || xd[xdi + 1] !== void 0 || w % k;\n        // The expression `w % mathpow(10, n - j - 1)` returns all the digits of w to the right of the\n        // digit at (left-to-right) index j, e.g. if w is 908714 and j is 2, the expression will give\n        // 714.\n        doRound = rm < 4 ? (rd || doRound) && (rm == 0 || rm == (x.s < 0 ? 3 : 2)) : rd > 5 || rd == 5 && (rm == 4 || doRound || rm == 6 && (i > 0 ? j > 0 ? w / mathpow(10, n - j) : 0 : xd[xdi - 1]) % 10 & 1 || rm == (x.s < 0 ? 8 : 7));\n    }\n    if (sd < 1 || !xd[0]) {\n        if (doRound) {\n            k = getBase10Exponent(x);\n            xd.length = 1;\n            // Convert sd to decimal places.\n            sd = sd - k - 1;\n            // 1, 0.1, 0.01, 0.001, 0.0001 etc.\n            xd[0] = mathpow(10, (LOG_BASE - sd % LOG_BASE) % LOG_BASE);\n            x.e = mathfloor(-sd / LOG_BASE) || 0;\n        } else {\n            xd.length = 1;\n            // Zero.\n            xd[0] = x.e = x.s = 0;\n        }\n        return x;\n    }\n    // Remove excess digits.\n    if (i == 0) {\n        xd.length = xdi;\n        k = 1;\n        xdi--;\n    } else {\n        xd.length = xdi + 1;\n        k = mathpow(10, LOG_BASE - i);\n        // E.g. 56700 becomes 56000 if 7 is the rounding digit.\n        // j > 0 means i > number of leading zeros of w.\n        xd[xdi] = j > 0 ? (w / mathpow(10, n - j) % mathpow(10, j) | 0) * k : 0;\n    }\n    if (doRound) {\n        for(;;){\n            // Is the digit to be rounded up in the first word of xd?\n            if (xdi == 0) {\n                if ((xd[0] += k) == BASE) {\n                    xd[0] = 1;\n                    ++x.e;\n                }\n                break;\n            } else {\n                xd[xdi] += k;\n                if (xd[xdi] != BASE) break;\n                xd[xdi--] = 0;\n                k = 1;\n            }\n        }\n    }\n    // Remove trailing zeros.\n    for(i = xd.length; xd[--i] === 0;)xd.pop();\n    if (external && (x.e > MAX_E || x.e < -MAX_E)) {\n        throw Error(exponentOutOfRange + getBase10Exponent(x));\n    }\n    return x;\n}\nfunction subtract(x, y) {\n    var d, e, i, j, k, len, xd, xe, xLTy, yd, Ctor = x.constructor, pr = Ctor.precision;\n    // Return y negated if x is zero.\n    // Return x if y is zero and x is non-zero.\n    if (!x.s || !y.s) {\n        if (y.s) y.s = -y.s;\n        else y = new Ctor(x);\n        return external ? round(y, pr) : y;\n    }\n    xd = x.d;\n    yd = y.d;\n    // x and y are non-zero numbers with the same sign.\n    e = y.e;\n    xe = x.e;\n    xd = xd.slice();\n    k = xe - e;\n    // If exponents differ...\n    if (k) {\n        xLTy = k < 0;\n        if (xLTy) {\n            d = xd;\n            k = -k;\n            len = yd.length;\n        } else {\n            d = yd;\n            e = xe;\n            len = xd.length;\n        }\n        // Numbers with massively different exponents would result in a very high number of zeros\n        // needing to be prepended, but this can be avoided while still ensuring correct rounding by\n        // limiting the number of zeros to `Math.ceil(pr / LOG_BASE) + 2`.\n        i = Math.max(Math.ceil(pr / LOG_BASE), len) + 2;\n        if (k > i) {\n            k = i;\n            d.length = 1;\n        }\n        // Prepend zeros to equalise exponents.\n        d.reverse();\n        for(i = k; i--;)d.push(0);\n        d.reverse();\n    // Base 1e7 exponents equal.\n    } else {\n        // Check digits to determine which is the bigger number.\n        i = xd.length;\n        len = yd.length;\n        xLTy = i < len;\n        if (xLTy) len = i;\n        for(i = 0; i < len; i++){\n            if (xd[i] != yd[i]) {\n                xLTy = xd[i] < yd[i];\n                break;\n            }\n        }\n        k = 0;\n    }\n    if (xLTy) {\n        d = xd;\n        xd = yd;\n        yd = d;\n        y.s = -y.s;\n    }\n    len = xd.length;\n    // Append zeros to xd if shorter.\n    // Don't add zeros to yd if shorter as subtraction only needs to start at yd length.\n    for(i = yd.length - len; i > 0; --i)xd[len++] = 0;\n    // Subtract yd from xd.\n    for(i = yd.length; i > k;){\n        if (xd[--i] < yd[i]) {\n            for(j = i; j && xd[--j] === 0;)xd[j] = BASE - 1;\n            --xd[j];\n            xd[i] += BASE;\n        }\n        xd[i] -= yd[i];\n    }\n    // Remove trailing zeros.\n    for(; xd[--len] === 0;)xd.pop();\n    // Remove leading zeros and adjust exponent accordingly.\n    for(; xd[0] === 0; xd.shift())--e;\n    // Zero?\n    if (!xd[0]) return new Ctor(0);\n    y.d = xd;\n    y.e = e;\n    //return external && xd.length >= pr / LOG_BASE ? round(y, pr) : y;\n    return external ? round(y, pr) : y;\n}\nfunction toString(x, isExp, sd) {\n    var k, e = getBase10Exponent(x), str = digitsToString(x.d), len = str.length;\n    if (isExp) {\n        if (sd && (k = sd - len) > 0) {\n            str = str.charAt(0) + \".\" + str.slice(1) + getZeroString(k);\n        } else if (len > 1) {\n            str = str.charAt(0) + \".\" + str.slice(1);\n        }\n        str = str + (e < 0 ? \"e\" : \"e+\") + e;\n    } else if (e < 0) {\n        str = \"0.\" + getZeroString(-e - 1) + str;\n        if (sd && (k = sd - len) > 0) str += getZeroString(k);\n    } else if (e >= len) {\n        str += getZeroString(e + 1 - len);\n        if (sd && (k = sd - e - 1) > 0) str = str + \".\" + getZeroString(k);\n    } else {\n        if ((k = e + 1) < len) str = str.slice(0, k) + \".\" + str.slice(k);\n        if (sd && (k = sd - len) > 0) {\n            if (e + 1 === len) str += \".\";\n            str += getZeroString(k);\n        }\n    }\n    return x.s < 0 ? \"-\" + str : str;\n}\n// Does not strip trailing zeros.\nfunction truncate(arr, len) {\n    if (arr.length > len) {\n        arr.length = len;\n        return true;\n    }\n}\n// Decimal methods\n/*\r\n *  clone\r\n *  config/set\r\n */ /*\r\n * Create and return a Decimal constructor with the same configuration properties as this Decimal\r\n * constructor.\r\n *\r\n */ function clone(obj) {\n    var i, p, ps;\n    /*\r\n   * The Decimal constructor and exported function.\r\n   * Return a new Decimal instance.\r\n   *\r\n   * value {number|string|Decimal} A numeric value.\r\n   *\r\n   */ function Decimal(value) {\n        var x = this;\n        // Decimal called without new.\n        if (!(x instanceof Decimal)) return new Decimal(value);\n        // Retain a reference to this Decimal constructor, and shadow Decimal.prototype.constructor\n        // which points to Object.\n        x.constructor = Decimal;\n        // Duplicate.\n        if (value instanceof Decimal) {\n            x.s = value.s;\n            x.e = value.e;\n            x.d = (value = value.d) ? value.slice() : value;\n            return;\n        }\n        if (typeof value === \"number\") {\n            // Reject Infinity/NaN.\n            if (value * 0 !== 0) {\n                throw Error(invalidArgument + value);\n            }\n            if (value > 0) {\n                x.s = 1;\n            } else if (value < 0) {\n                value = -value;\n                x.s = -1;\n            } else {\n                x.s = 0;\n                x.e = 0;\n                x.d = [\n                    0\n                ];\n                return;\n            }\n            // Fast path for small integers.\n            if (value === ~~value && value < 1e7) {\n                x.e = 0;\n                x.d = [\n                    value\n                ];\n                return;\n            }\n            return parseDecimal(x, value.toString());\n        } else if (typeof value !== \"string\") {\n            throw Error(invalidArgument + value);\n        }\n        // Minus sign?\n        if (value.charCodeAt(0) === 45) {\n            value = value.slice(1);\n            x.s = -1;\n        } else {\n            x.s = 1;\n        }\n        if (isDecimal.test(value)) parseDecimal(x, value);\n        else throw Error(invalidArgument + value);\n    }\n    Decimal.prototype = P;\n    Decimal.ROUND_UP = 0;\n    Decimal.ROUND_DOWN = 1;\n    Decimal.ROUND_CEIL = 2;\n    Decimal.ROUND_FLOOR = 3;\n    Decimal.ROUND_HALF_UP = 4;\n    Decimal.ROUND_HALF_DOWN = 5;\n    Decimal.ROUND_HALF_EVEN = 6;\n    Decimal.ROUND_HALF_CEIL = 7;\n    Decimal.ROUND_HALF_FLOOR = 8;\n    Decimal.clone = clone;\n    Decimal.config = Decimal.set = config;\n    if (obj === void 0) obj = {};\n    if (obj) {\n        ps = [\n            \"precision\",\n            \"rounding\",\n            \"toExpNeg\",\n            \"toExpPos\",\n            \"LN10\"\n        ];\n        for(i = 0; i < ps.length;)if (!obj.hasOwnProperty(p = ps[i++])) obj[p] = this[p];\n    }\n    Decimal.config(obj);\n    return Decimal;\n}\n/*\r\n * Configure global settings for a Decimal constructor.\r\n *\r\n * `obj` is an object with one or more of the following properties,\r\n *\r\n *   precision  {number}\r\n *   rounding   {number}\r\n *   toExpNeg   {number}\r\n *   toExpPos   {number}\r\n *\r\n * E.g. Decimal.config({ precision: 20, rounding: 4 })\r\n *\r\n */ function config(obj) {\n    if (!obj || typeof obj !== \"object\") {\n        throw Error(decimalError + \"Object expected\");\n    }\n    var i, p, v, ps = [\n        \"precision\",\n        1,\n        MAX_DIGITS,\n        \"rounding\",\n        0,\n        8,\n        \"toExpNeg\",\n        -1 / 0,\n        0,\n        \"toExpPos\",\n        0,\n        1 / 0\n    ];\n    for(i = 0; i < ps.length; i += 3){\n        if ((v = obj[p = ps[i]]) !== void 0) {\n            if (mathfloor(v) === v && v >= ps[i + 1] && v <= ps[i + 2]) this[p] = v;\n            else throw Error(invalidArgument + p + \": \" + v);\n        }\n    }\n    if ((v = obj[p = \"LN10\"]) !== void 0) {\n        if (v == Math.LN10) this[p] = new this(v);\n        else throw Error(invalidArgument + p + \": \" + v);\n    }\n    return this;\n}\n// Create and configure initial Decimal constructor.\nvar Decimal = clone(defaults);\n// Internal constant.\nONE = new Decimal(1);\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Decimal);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/decimal.js-light/decimal.mjs\n");

/***/ })

};
;